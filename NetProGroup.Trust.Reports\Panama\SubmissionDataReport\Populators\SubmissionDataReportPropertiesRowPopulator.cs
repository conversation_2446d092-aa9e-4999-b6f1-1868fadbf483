// <copyright file="SubmissionDataReportPropertiesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportPropertiesRowPopulator : LinePopulatorBase, ISubmissionDataReportPropertiesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the transfer properties
            var transferProperties = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.EquityProperties, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (transferProperties.Count > 1)
            {
                // Group the properties by the index
                var propertyGroups = transferProperties.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.EquityProperties + ".")[1].Split(".")[0]);

                foreach (var group in propertyGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the property type
                    SetCellValueAndStyle(worksheet, currentRow, 4, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Type, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherType, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the property value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Value, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }
        }
    }
}