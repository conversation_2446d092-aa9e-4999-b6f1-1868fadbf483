// <copyright file="SearchBoDirRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.BoDir.RequestResponses
{
    /// <summary>
    /// Request model for searching Beneficial Owners and Directors.
    /// </summary>
    public class SearchBoDirRequest : PagedAndSortedRequest, IJurisdictionFilteredRequest
    {
        /// <summary>
        /// Gets or sets the Search Term filter.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the Position filter (Director, BO).
        /// </summary>
        public BoDirPosition? Position { get; set; }

        /// <summary>
        /// Gets or sets the Production Office filter (THKO, TBVI, TCYP, TPANVG TNEV).
        /// </summary>
        public string ProductionOffice { get; set; }

        /// <summary>
        /// Gets or sets the Confirmed Date range start.
        /// </summary>
        public DateTime? ConfirmedDateFrom { get; set; }

        /// <summary>
        /// Gets or sets the Confirmed Date range end.
        /// </summary>
        public DateTime? ConfirmedDateTo { get; set; }

        /// <summary>
        /// Gets or sets the Data Status filters.
        /// </summary>
        public List<LegalEntityRelationStatus> DataStatuses { get; set; } = new ();

        /// <summary>
        /// Gets or sets the BO/Dir specifics.
        /// </summary>
        public List<BoDirSpecifics> Specifics { get; set; }

        /// <inheritdoc />
        public List<Guid> AuthorizedJurisdictionIDs { get; set; }
    }
}
