using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a confirmation schema in the old database.
    /// Not used for Nevis.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class ConfirmationSchema
    {
        /// <summary>
        /// Gets or sets a value indicating whether the submission is confirmed.
        /// </summary>
        [BsonElement("confirmed")]
        public bool? Confirmed { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the authority is confirmed.
        /// </summary>
        [BsonElement("confirmed_authority")]
        public bool? ConfirmedAuthority { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the conditions are confirmed.
        /// </summary>
        [BsonElement("confirmed_conditions")]
        public bool? ConfirmedConditions { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the payment is confirmed.
        /// </summary>
        [BsonElement("confirmed_payment")]
        public bool? ConfirmedPayment { get; set; }

        /// <summary>
        /// Gets or sets the user's full name.
        /// </summary>
        [BsonElement("user_fullname")]
        public string UserFullName { get; set; }

        /// <summary>
        /// Gets or sets the user's phone number.
        /// </summary>
        [BsonElement("user_phonenumber")]
        public string UserPhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets the relation to the entity.
        /// </summary>
        [BsonElement("relation_to_entity")]
        public string RelationToEntity { get; set; }

        /// <summary>
        /// Gets or sets the other relation to the entity.
        /// </summary>
        [BsonElement("relation_to_entity_other")]
        public string RelationToEntityOther { get; set; }

        /// <summary>
        /// Gets or sets the ultimate parent entity name.
        /// </summary>
        [BsonElement("ultimate_parent_entity_name")]
        public string UltimateParentEntityName { get; set; }

        /// <summary>
        /// Gets or sets the ultimate parent entity address.
        /// </summary>
        [BsonElement("ultimate_parent_entity_address")]
        public string UltimateParentEntityAddress { get; set; }
    }
}
