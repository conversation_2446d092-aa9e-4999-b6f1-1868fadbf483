// <copyright file="BasicFinancialReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Panama.BasicFinancialReport.Populators;

namespace NetProGroup.Trust.Reports.Panama.BasicFinancialReport
{
    /// <summary>
    /// Generates reports for Panama's basic financial report module.
    /// </summary>
    /// <remarks>
    /// This report is an excel with all submissions not exported yet and with the attribute 'financial-period.tridentAccountingRecordsTool' set.
    /// The template is retrieved from the DataManager.
    /// </remarks>
    public class BasicFinancialReportGenerator : IBasicFinancialReportGenerator
    {
        private const string ReportName = "financial-report";
        private const string TemplateName = "basic-financial-report-submissions";
        private const string Version = "v1";

        private readonly IBasicFinancialReportRowPopulator _basicFinancialReportRowPopulator;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IExcelTemplateService<Submission> _excelTemplateService;
        private readonly ILateFeeLinePopulator _lateFeeLinePopulator;
        private readonly IRegularFeeLinePopulator _regularFeeLinePopulator;
        private readonly ISubmissionReportsDataManager _submissionReportsDataManager;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="BasicFinancialReportGenerator"/> class.
        /// </summary>
        /// <param name="basicFinancialReportRowPopulator">The submission line populator.</param>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="regularFeeLinePopulator">The regular fee line populator.</param>
        /// <param name="lateFeeLinePopulator">The late fee line populator.</param>
        /// <param name="submissionReportsDataManager">The submission reports datamanager.</param>
        /// <param name="templateProvider">The template provider.</param>
        public BasicFinancialReportGenerator(
            IBasicFinancialReportRowPopulator basicFinancialReportRowPopulator,
            IDateTimeProvider dateTimeProvider,
            IExcelTemplateService<Submission> excelTemplateService,
            IRegularFeeLinePopulator regularFeeLinePopulator,
            ILateFeeLinePopulator lateFeeLinePopulator,
            ISubmissionReportsDataManager submissionReportsDataManager,
            IReportTemplateProvider templateProvider)
        {
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;

            _basicFinancialReportRowPopulator = basicFinancialReportRowPopulator;
            _regularFeeLinePopulator = regularFeeLinePopulator;
            _lateFeeLinePopulator = lateFeeLinePopulator;

            _dateTimeProvider = dateTimeProvider;
            _submissionReportsDataManager = submissionReportsDataManager;
        }

        /// <inheritdoc/>
        public async Task<ReportOutput> GenerateSubmissionsReportAsync(List<Submission> submissions)
        {
            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionsReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new ReportOutput(stream.ToArray());
        }

        /// <inheritdoc />
        public async Task<FinancialReportOutput> GenerateTodayFinancialReportAsync()
        {
            var submissions = (await GetSubmissions()).ToList();
            if (submissions.Count == 0)
            {
                return new FinancialReportOutput(submissions);
            }

            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(ReportName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionExcelReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new FinancialReportOutput(stream.ToArray(), submissions);
        }

        /// <inheritdoc />
        public string GenerateReportNameForTodayAsync()
        {
            return $"{ReportName}-{_dateTimeProvider.Now:yyyy-MM-dd HH-mm-ss}-{Version}";
        }

        /// <inheritdoc />
        public async Task CompleteFinancialExport(IEnumerable<Submission> submissions, Guid reportId)
        {
            await _submissionReportsDataManager.CompleteFinancialExport(submissions, reportId);
        }

        /// <summary>
        /// Creates the default template configuration settings.
        /// </summary>
        /// <returns>A configured TemplateConfiguration object.</returns>
        private static TemplateConfiguration CreateTemplateConfiguration()
        {
            return new TemplateConfiguration
            {
                TemplateRowCount = 6, // We have 6 rows per submission
                HeaderRowCount = 0, // We have no header rows
                StartingRow = 7
            };
        }

        /// <summary>
        /// Creates an Excel report for the given companies.
        /// </summary>
        /// <param name="workbook">The workbook to add the companies to.</param>
        /// <param name="submissions">The submissions to add in the report.</param>
        private void CreateSubmissionsReport(XLWorkbook workbook, List<Submission> submissions)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per company
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };

            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_basicFinancialReportRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig);
        }

        /// <summary>
        /// Creates an Excel report for the given submissions.
        /// </summary>
        /// <param name="workbook">The workbook to add the submissions to.</param>
        /// <param name="submissions">The submissions to add.</param>
        private void CreateSubmissionExcelReport(XLWorkbook workbook, IEnumerable<Submission> submissions)
        {
            var templateConfig = CreateTemplateConfiguration();

            // Register row populators
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_regularFeeLinePopulator);
            _excelTemplateService.RegisterRowPopulator(_lateFeeLinePopulator);

            // Apply template
            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig);
        }

        private async Task<IEnumerable<Submission>> GetSubmissions()
        {
            // Get the submissions for today's basic financial report
            return await _submissionReportsDataManager.GetSubmissionsForPanamaBasicFinancialReportExport();
        }
    }
}