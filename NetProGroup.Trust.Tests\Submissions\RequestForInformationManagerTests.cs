﻿using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Tests.Shared;
using System.Text;

namespace NetProGroup.Trust.Tests.Submissions
{
    [TestFixture()]
    public class RequestForInformationManagerTests : TestBase
    {
        private IRequestForInformationManager _sut; // System Under Test
        private ISystemAuditManager _systemAuditManager;
        private ISubmissionsRepository _submissionsRepository;
        private IRequestForInformationRepository _requestForInformationRepository;
        private IAnnouncementsRepository _announcementsRepository;
        private Guid _submissionId;

        [SetUp]
        public void Setup()
        {
            _sut = _server.Services.GetRequiredService<IRequestForInformationManager>();
            _systemAuditManager = _server.Services.GetRequiredService<ISystemAuditManager>();
            _submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();
            _requestForInformationRepository = _server.Services.GetRequiredService<IRequestForInformationRepository>();
            _announcementsRepository = _server.Services.GetRequiredService<IAnnouncementsRepository>();

            // Create test data
            CreateTestSubmission().Wait();
        }

        private async Task CreateTestSubmission()
        {
            // Create a test submission
            var submission = new Submission
            {
                Name = "Test Submission",
                Status = SubmissionStatus.Submitted,
                LegalEntityId = Guid.NewGuid(),
                ModuleId = Guid.NewGuid(),
                FinancialYear = 2024,
                StartsAt = new DateTime(2024, 1, 1),
                EndsAt = new DateTime(2024, 12, 31),
                Layout = "TridentTrust", // Required property
                ReportId = Guid.NewGuid().ToString(), // Required property
                LegalEntity = new Domain.LegalEntities.LegalEntity
                {
                    Name = "Test Company",
                    Code = "TEST",
                    Jurisdiction = new Domain.Jurisdictions.Jurisdiction
                    {
                        Code = JurisdictionCodes.Bahamas,
                        Name = "Bahamas"
                    },
                    MasterClient = _masterClient
                }
            };

            await _submissionsRepository.InsertAsync(submission);
            await _submissionsRepository.SaveChangesAsync();

            _submissionId = submission.Id;
        }

        [Test]
        public async Task CreateRequestForInformationAsync_WithoutAttachments_AddsRfiCreatedNotificationActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // Create RFI data with IncludeAttachments = false to trigger notification
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Act - call the SUT method
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI creation notification activity log
            var rfiCreationLog = activityLogs.ActivityLogItems.Should().ContainSingle(
                log => log.ActivityType == ActivityLogActivityTypes.RfiCreatedNotificationCreated).Subject;

            rfiCreationLog.ShortDescription.Should().Be($"Notification of RFI creation with id '{rfiId}'.");
            rfiCreationLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CreateRequestForInformationAsync_WithoutAttachements_CreateAnnoucementWithLegalEntityAndMasterClientIDsUrl()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            // Create RFI data with IncludeAttachments = false to trigger notification
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Act - call the SUT method
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Assert
            var rfi = await _requestForInformationRepository.GetByIdAsync(rfiId);
            // Retrieve the last created Announcement.
            var announcement = await _announcementsRepository.FindFirstOrDefaultByConditionAsync(x => x.Recipients.Any(r => r.RecipientId == rfi.Submission.LegalEntityId));

            // Verify that the Announcement Body contains a URL with the correct legal entity and master client IDs
            var expectedUrlPart = $"setCompanyId={rfi.Submission.LegalEntityId}&amp;setMasterClientId={rfi.Submission.LegalEntity.MasterClientId}";
            announcement.Body.Should().Contain(expectedUrlPart);
        }

        [Test]
        public async Task ProcessRFIsDueInOneWeekAsync_ProcessesRFIsAndSendsNotifications()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // Create an RFI that is due in one week
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(6), // Set deadline to 6 days from now to match the condition in ProcessRFIsDueInOneWeekAsync
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Act - call the SUT method to process RFIs due in one week
            await _sut.ProcessRFIsDueInOneWeekAsync();

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI due in one week notification activity log
            var rfiDueInOneWeekLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.RfiDueInOneWeekNotificationCreated).Subject;

            rfiDueInOneWeekLog.ShortDescription.Should().Be($"Notification of RFI due in one week with id '{rfiId}'.");
            rfiDueInOneWeekLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task ProcessRFIsDueInOneDayAsync_ProcessesRFIsAndSendsNotifications()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI with DueInOneWeek reminder type
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(1), // Set deadline to 1 day from now
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // First process it to set the reminder type to DueInOneWeek
            await _sut.ProcessRFIsDueInOneWeekAsync();

            // Act - call the SUT method to process RFIs due in one day
            await _sut.ProcessRFIsDueInOneDayAsync();

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI due in one day notification activity log
            var rfiDueInOneDayLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.RfiDueInOneDayNotificationCreated).Subject;

            rfiDueInOneDayLog.ShortDescription.Should().Be($"Notification of RFI due in one day with id '{rfiId}'.");
            rfiDueInOneDayLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task ProcessRFIsThreeDaysOverdueAsync_ProcessesRFIsAndSendsNotifications()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // Create an RFI that is three days overdue
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(-3), // Set deadline to 3 days ago
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // First process it to set the reminder type to DueInOneWeek
            await _sut.ProcessRFIsDueInOneWeekAsync();

            // Then process it to set the reminder type to DueInOneDay
            await _sut.ProcessRFIsDueInOneDayAsync();

            // Act - call the SUT method to process RFIs three days overdue
            await _sut.ProcessRFIsThreeDaysOverdueAsync();

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI three days overdue notification activity log
            var rfiThreeDaysOverdueLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.Rfi3DaysOverDueNotificationCreated).Subject;

            rfiThreeDaysOverdueLog.ShortDescription.Should().Be($"Notification of RFI three days overdue with id '{rfiId}'.");
            rfiThreeDaysOverdueLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CompleteRequestForInformationAsync_WithoutAttachments_AddsRfiCompletedNotificationActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Act - call the SUT method to complete the RFI
            var completeRfiData = new CompleteRequestForInformationDTO
            {
                Response = "Test response for RFI",
                IncludeAttachments = false
            };

            await _sut.CompleteRequestForInformationAsync(rfiId, completeRfiData);

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI completed notification activity log
            var rfiCompletedLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.RfiCompletedNotificationCreated).Subject;

            rfiCompletedLog.ShortDescription.Should().Be($"Notification of completion of RFI with id '{rfiId}'.");
            rfiCompletedLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CancelRequestForInformationAsync_WhenRFIAlreadyCompleted_ThrowsException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Act - call the SUT method to complete the RFI
            var completeRfiData = new CompleteRequestForInformationDTO
            {
                Response = "Test response for RFI",
                IncludeAttachments = false
            };

            await _sut.CompleteRequestForInformationAsync(rfiId, completeRfiData);

            // Act
            var task = () => _sut.CancelRequestForInformationAsync(rfiId, "test");

            // Assert
            await task.Should().ThrowAsync<Framework.Exceptions.ConstraintException>()
                .WithMessage($"The request for information with id '{rfiId}' is already completed so it cannot be cancelled.")
                .Where(e => e.Code == (int)ApplicationErrors.INVALID_REQUEST_FOR_INFORMATION_STATUS);
        }

        [Test]
        public async Task CancelledRequestForInformation_WhenRfiAlreadyCancelled_ThrowsException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();
            string cancellationReason = "reason to cancel: .....";

            // First create an RFI
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Act - call the SUT method to complete the RFI
            var completeRfiData = new CompleteRequestForInformationDTO
            {
                Response = "Test response for RFI",
                IncludeAttachments = false
            };

            // Cancel the request
            await _sut.CancelRequestForInformationAsync(rfiId, cancellationReason);

            // Act
            var task = () => _sut.CancelRequestForInformationAsync(rfiId, "test");

            // Assert
            await task.Should().ThrowAsync<Framework.Exceptions.ConstraintException>()
                .WithMessage($"The request for information with id '{rfiId}' is already cancelled.")
                .Where(e => e.Code == (int)ApplicationErrors.INVALID_REQUEST_FOR_INFORMATION_STATUS);
        }

        [Test]
        public async Task CancelRequestForInformationAsync_AddsRequestForInformationCancelledActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();
            string cancellationReason = "reason to cancel: .....";

            // First create an RFI
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Act - call the SUT method to cancel the RFI
            await _sut.CancelRequestForInformationAsync(rfiId, cancellationReason);

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI cancelled activity log
            var rfiCancelledLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.SubmissionInformationRequestCancelled).Subject;

            rfiCancelledLog.ShortDescription.Should().Be("Information request cancelled.");
            rfiCancelledLog.EntityName.Should().Be(nameof(Submission));
            rfiCancelledLog.Text.Should().Contain(cancellationReason);
        }

        [Test]
        public async Task CreateRFIDocumentAsync_WithUploadComplete_AddsRfiCreatedNotificationActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI with attachments
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI with attachments",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = true // This will create a draft RFI
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Create a mock file for upload
            var fileContent = "This is a test file content"u8.ToArray();
            var formFile = new FormFile(
                baseStream: new MemoryStream(fileContent),
                baseStreamOffset: 0,
                length: fileContent.Length,
                name: "testFile",
                fileName: "test.txt"
            );

            // Create document data
            var documentData = new CreateRFIDocumentDTO
            {
                File = formFile,
                Description = "Test document",
                Type = DocumentType.Pdf,
                UploadComplete = true // This will activate the RFI
            };

            // Act - call the SUT method to upload document and complete the RFI
            await _sut.CreateRFIDocumentAsync(rfiId, documentData, true);

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the RFI creation notification activity log
            var rfiCreationLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.RfiCreatedNotificationCreated).Subject;

            rfiCreationLog.ShortDescription.Should().Be($"Notification of RFI creation with id '{rfiId}'.");
            rfiCreationLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CreateRequestForInformationAsync_WithoutAttachments_AddsSubmissionInformationRequestedActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // Create RFI data with IncludeAttachments = false to trigger notification
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Act - call the SUT method
            await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the SubmissionInformationRequested activity log
            var submissionInformationRequestedLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.SubmissionInformationRequested).Subject;

            submissionInformationRequestedLog.ShortDescription.Should().Be("Information requested.");
            submissionInformationRequestedLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CreateRFIDocumentAsync_WithUploadCompleteByManagementUser_AddsSubmissionInformationRequestedActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI with attachments
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI with attachments",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = true // This will create a draft RFI
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Create a mock file for upload
            var fileContent = "This is a test file content"u8.ToArray();
            var formFile = new FormFile(
                baseStream: new MemoryStream(fileContent),
                baseStreamOffset: 0,
                length: fileContent.Length,
                name: "testFile",
                fileName: "test.txt"
            );

            // Create document data
            var documentData = new CreateRFIDocumentDTO
            {
                File = formFile,
                Description = "Test document",
                Type = DocumentType.Pdf,
                UploadComplete = true // This will activate the RFI
            };

            // Act - call the SUT method to upload document and complete the RFI
            await _sut.CreateRFIDocumentAsync(rfiId, documentData, true); // true = created by management user

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the SubmissionInformationRequested activity log
            var submissionInformationRequestedLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.SubmissionInformationRequested).Subject;

            submissionInformationRequestedLog.ShortDescription.Should().Be("Information requested.");
            submissionInformationRequestedLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CompleteRequestForInformationAsync_WithoutAttachments_AddsSubmissionInformationRequestCompletedActivityLog()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);

            // Act - call the SUT method to complete the RFI
            var completeRfiData = new CompleteRequestForInformationDTO
            {
                Response = "Test response for RFI",
                IncludeAttachments = false
            };

            await _sut.CompleteRequestForInformationAsync(rfiId, completeRfiData);

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the SubmissionInformationRequestCompleted activity log
            var submissionInformationRequestCompletedLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.SubmissionInformationRequestCompleted).Subject;

            submissionInformationRequestCompletedLog.ShortDescription.Should().Be($"Information request with id '{rfiId}' completed.");
            submissionInformationRequestCompletedLog.EntityName.Should().Be(nameof(Submission));
        }

        [Test]
        public async Task CreateRFIDocumentAsync_WithUploadCompleteByClient_AddsSubmissionInformationRequestCompletedActivityLog()
        {
            // Arrange
            SetWorkContextUser(ClientUser);
            var testStartTime = GetTestStartTime();

            // First create an RFI with attachments
            var createRfiData = new CreateRFIDTO
            {
                Comments = "Test comments for RFI with attachments",
                DeadLine = DateTime.UtcNow.AddDays(14),
                IncludeAttachments = false // Create an active RFI
            };

            // Create the RFI
            var rfiId = await _sut.CreateRequestForInformationAsync(_submissionId, createRfiData);


            // Create a mock file for upload
            var fileContent = "This is a test file content"u8.ToArray();
            var formFile = new FormFile(
                baseStream: new MemoryStream(fileContent),
                baseStreamOffset: 0,
                length: fileContent.Length,
                name: "testFile",
                fileName: "test.txt"
            );

            // Create document data
            var documentData = new CreateRFIDocumentDTO
            {
                File = formFile,
                Description = "Test document",
                Type = DocumentType.Pdf,
                UploadComplete = true // This will complete the RFI
            };

            // Act - call the SUT method to upload document and complete the RFI
            await _sut.CreateRFIDocumentAsync(rfiId, documentData, false); // false = created by client user

            // Assert
            var activityLogRequest = new ListActivityLogRequest
            {
                Period = new Period { StartDate = testStartTime, EndDate = DateTime.UtcNow.AddMinutes(1) },
                EntityId = _submissionId
            };

            var activityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            // Verify that the activity log was created
            activityLogs.Should().NotBeNull();
            activityLogs.ActivityLogItems.Should().NotBeEmpty();

            // Find the SubmissionInformationRequestCompleted activity log
            var submissionInformationRequestCompletedLog = activityLogs.ActivityLogItems.Should().Contain(
                log => log.ActivityType == ActivityLogActivityTypes.SubmissionInformationRequestCompleted).Subject;

            submissionInformationRequestCompletedLog.ShortDescription.Should().Be($"Information request with id '{rfiId}' completed.");
            submissionInformationRequestCompletedLog.EntityName.Should().Be(nameof(Submission));

        }

        // Instead of clearing activity logs, we'll use a timestamp to filter logs
        private static DateTime GetTestStartTime()
        {
            return DateTime.UtcNow;
        }
    }
}