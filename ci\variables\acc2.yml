# Acceptance environment variables
variables:  
  # SQL Server configuration
  serverName: 'sqlsrv-pcp-prd2-weu.database.windows.net'
  databaseName: 'sqldb-pcp-acc'
  
  # Azure App Service configuration
  webAppName: 'app-pcp-api-prd2-weu'
  resourceGroup: 'rg-ttg-pcp-prd2-app-weu'
  slotName: 'acc'
  
  # Agent pool
  pool: 'TT PCP - WindowsAgents prd2'
  
  # Environment settings
  environment: 'UAT 2'
