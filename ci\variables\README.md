# Pipeline Variables Structure

This directory contains environment-specific and global variable files for Azure DevOps pipelines.

## File Structure

```
variables/
├── global.yml          # Global variables used across all environments
├── dev.yml            # Development environment variables
├── tst.yml            # Test environment variables
├── acc.yml            # Acceptance environment variables
├── prd.yml            # Production environment variables
└── README.md          # This documentation file
```

## Variable Files

### global.yml
Contains common variables used across all environments:
- Application naming conventions
- Runtime configurations (runtime stack, startup command, app type)
- Pipeline artifact name

## Usage in Pipelines

### Method 1: Using Variable Templates in Main Pipeline
```yaml
variables:
- template: variables/global.yml
- template: variables/dev.yml  # or tst.yml, acc.yml, prd.yml

stages:
- stage: Deploy
  jobs:
  - job: DeployJob
    steps:
    - task: SqlAzureDacpacDeployment@1
      inputs:
        ServerName: '$(serverName)'
        DatabaseName: '$(databaseName)'
```

### Method 2: Using Variable Templates in Deploy Template
```yaml
- template: deploy.yml
  parameters:
    stageName: DeployToDev
    dependsOn: Build
    condition: succeeded()
    variableTemplate: variables/dev.yml
```

### Method 3: Conditional Variable Loading
```yaml
variables:
- template: variables/global.yml
- ${{ if eq(parameters.targetEnvironment, 'dev') }}:
  - template: variables/dev.yml
- ${{ if eq(parameters.targetEnvironment, 'tst') }}:
  - template: variables/tst.yml
```

## Benefits

1. **Consistency**: Same variable names across all environments
2. **Maintainability**: Single place to update environment configurations
3. **Reusability**: Variable files can be imported by multiple pipelines
4. **Separation of Concerns**: Global vs environment-specific variables
5. **Reduced Duplication**: No need to repeat the same values across pipelines

## Adding New Environments

To add a new environment:

1. Create a new variable file (e.g., `staging.yml`)
2. Follow the same structure as existing environment files
3. Update pipelines to reference the new variable file
4. Update this README with the new environment

## Example: Data Correction Pipeline

See `../data-correction-pipeline.yml` for an example of how to use these variable files in a data correction pipeline that can target any environment.
