using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD
{
    /// <summary>
    /// Manager for exporting submissions.
    /// </summary>
    public interface IExportSubmissionsIRDGenerator
    {
        /// <summary>
        /// Exports the submissions to a file.
        /// </summary>
        /// <param name="request">The request containing the parameters for the search.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the submissions as a paged list.</returns>
        Task<ReportDownloadResponseDTO> ExportAsync(ExportSubmissionDTO request);
    }
}