﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a business entity with comprehensive financial, operational, and compliance information
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class BusinessSchema
    {
        /// <summary>
        /// Gets or sets the unique identifier for the business entity
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        #region Income

        /// <summary>
        /// Gets or sets the total gross income for the business
        /// </summary>
        [Required]
        [BsonElement("gross_income_total")]
        public decimal GrossIncomeTotal { get; set; }

        /// <summary>
        /// Gets or sets the net book values of activity-related assets
        /// </summary>
        [Required]
        [BsonElement("activity_netbook_values")]
        public decimal ActivityNetbookValues { get; set; }

        /// <summary>
        /// Gets or sets the description of equipment nature used in business operations
        /// </summary>
        [Required]
        [BsonElement("equipment_nature_description")]
        public string EquipmentNatureDescription { get; set; }

        #endregion

        #region Direction and Management

        /// <summary>
        /// Gets or sets whether the business management is located in Bahamas
        /// </summary>
        [Required]
        [BsonElement("management_in_bah")]
        public bool ManagementInBah { get; set; }

        /// <summary>
        /// Gets or sets the total number of board meetings held
        /// </summary>
        [Required]
        [BsonElement("number_of_board_meetings")]
        public int NumberOfBoardMeetings { get; set; }

        /// <summary>
        /// Gets or sets the number of board meetings held specifically in Bahamas
        /// </summary>
        [Required]
        [BsonElement("number_of_board_meetings_in_bahamas")]
        public int NumberOfBoardMeetingsInBahamas { get; set; }

        /// <summary>
        /// Gets or sets the quorum required for board meetings
        /// </summary>
        [Required]
        [BsonElement("board_meetings_quorum")]
        public int BoardMeetingsQuorum { get; set; }

        /// <summary>
        /// Gets or sets the quorum of directors required for decision making
        /// </summary>
        [Required]
        [BsonElement("quorum_of_directors")]
        public int QuorumOfDirectors { get; set; }

        /// <summary>
        /// Gets or sets whether meeting minutes are held in Bahamas
        /// </summary>
        [Required]
        [BsonElement("are_minuted_held_in_bah")]
        public bool AreMinutedHeldInBah { get; set; }

        /// <summary>
        /// Gets or sets the list of directors associated with the business
        /// </summary>
        [BsonElement("directors")]
        public List<DirectorSchema> Directors { get; set; }

        #endregion

        #region Expenditure

        /// <summary>
        /// Gets or sets the total expenditure of the business
        /// </summary>
        [Required]
        [BsonElement("total_expenditure")]
        public decimal TotalExpenditure { get; set; }

        /// <summary>
        /// Gets or sets the total expenditure specifically in Bahamas
        /// </summary>
        [Required]
        [BsonElement("total_expenditure_bah")]
        public decimal TotalExpenditureBah { get; set; }

        #endregion

        #region Employees

        /// <summary>
        /// Gets or sets the total number of employees in the business
        /// </summary>
        [Required]
        [BsonElement("full_total_employees")]
        public double FullTotalEmployees { get; set; }

        /// <summary>
        /// Gets or sets the number of employees engaged in relevant business activities
        /// </summary>
        [Required]
        [BsonElement("total_employees_engaged_relevant_activity")]
        public double TotalEmployeesEngagedRelevantActivity { get; set; }

        /// <summary>
        /// Gets or sets the number of employees physically present in Bahamas
        /// </summary>
        [Required]
        [BsonElement("total_employees_present_in_bahamas")]
        public int TotalEmployeesPresentInBahamas { get; set; }

        /// <summary>
        /// Gets or sets the list of employees associated with the business
        /// </summary>
        [BsonElement("employees")]
        public List<EmployeeSchema> Employees { get; set; }

        #endregion

        #region Premises

        /// <summary>
        /// Gets or sets whether the business owns premises in Bahamas
        /// </summary>
        [Required]
        [BsonElement("own_premises_in_bah")]
        public bool OwnPremisesInBah { get; set; }

        /// <summary>
        /// Gets or sets the list of premises associated with the business
        /// </summary>
        [BsonElement("premises")]
        public List<PremisesSchema> Premises { get; set; }

        #endregion

        #region CIGA (Core Income Generating Activities)

        /// <summary>
        /// Gets or sets whether the business has any Core Income Generating Activities
        /// </summary>
        [BsonElement("has_any_ciga")]
        [BsonIgnoreIfNull]
        public bool? HasAnyCiga { get; set; }

        /// <summary>
        /// Gets or sets whether any CIGA activities are outsourced
        /// </summary>
        [BsonElement("has_any_ciga_outsourced")]
        [BsonIgnoreIfNull]
        public bool? HasAnyCigaOutsourced { get; set; }

        /// <summary>
        /// Gets or sets the proportion of activities carried out by outsourcing
        /// </summary>
        [BsonElement("proportion_carried_by_outsourcing")]
        [BsonIgnoreIfNull]
        public decimal? ProportionCarriedByOutsourcing { get; set; }

        /// <summary>
        /// Gets or sets the total expenditure on outsourced activities in Bahamas
        /// </summary>
        [BsonElement("total_expenditure_outsourced_bah")]
        [BsonIgnoreIfNull]
        public decimal? TotalExpenditureOutsourcedBah { get; set; }

        /// <summary>
        /// Gets or sets the list of CIGA activities
        /// </summary>
        [BsonElement("ciga_activities")]
        public List<CigaActivitySchema> CigaActivities { get; set; }

        /// <summary>
        /// Gets or sets whether the entity complies with applicable laws
        /// </summary>
        [BsonElement("entity_applicable_laws")]
        [BsonIgnoreIfNull]
        public bool? EntityApplicableLaws { get; set; }

        /// <summary>
        /// Gets or sets the list of outsourcing providers
        /// </summary>
        [BsonElement("outsourcing_providers")]
        public List<OutsourcingProviderSchema> OutsourcingProviders { get; set; }

        #endregion

        #region Intellectual Properties Business

        /// <summary>
        /// Gets or sets whether the entity is classified as high risk
        /// </summary>
        [Required]
        [BsonElement("is_high_risk_entity")]
        public bool IsHighRiskEntity { get; set; }

        /// <summary>
        /// Gets or sets details about relevant intellectual property
        /// </summary>
        [BsonElement("relevant_ip")]
        [BsonIgnoreIfNull]
        public string RelevantIp { get; set; }

        /// <summary>
        /// Gets or sets information about intellectual property usage
        /// </summary>
        [BsonElement("intellectual_propery_use")]
        [BsonIgnoreIfNull]
        public string IntellectualProperyUse { get; set; }

        /// <summary>
        /// Gets or sets details about decision making for income generation
        /// </summary>
        [BsonElement("decision_generation_income")]
        [BsonIgnoreIfNull]
        public string DecisionGenerationIncome { get; set; }

        /// <summary>
        /// Gets or sets information about strategic business decisions
        /// </summary>
        [BsonElement("strategic_decisions")]
        [BsonIgnoreIfNull]
        public string StrategicDecisions { get; set; }

        /// <summary>
        /// Gets or sets the history of trading activities
        /// </summary>
        [BsonElement("history_trading_activities")]
        [BsonIgnoreIfNull]
        public string HistoryTradingActivities { get; set; }

        /// <summary>
        /// Gets or sets the gross income from royalties
        /// </summary>
        [BsonElement("gross_income_royalties")]
        [BsonIgnoreIfNull]
        public decimal? GrossIncomeRoyalties { get; set; }

        /// <summary>
        /// Gets or sets the gross income from gains
        /// </summary>
        [BsonElement("gross_income_gains")]
        [BsonIgnoreIfNull]
        public decimal? GrossIncomeGains { get; set; }

        /// <summary>
        /// Gets or sets the gross income from other sources
        /// </summary>
        [BsonElement("gross_income_others")]
        [BsonIgnoreIfNull]
        public decimal? GrossIncomeOthers { get; set; }

        /// <summary>
        /// Gets or sets detailed information about the business plan
        /// </summary>
        [BsonElement("business_plan_details")]
        [BsonIgnoreIfNull]
        public string BusinessPlanDetails { get; set; }

        /// <summary>
        /// Gets or sets evidence of decision making processes
        /// </summary>
        [BsonElement("decision_making_evidence")]
        [BsonIgnoreIfNull]
        public string DecisionMakingEvidence { get; set; }

        /// <summary>
        /// Gets or sets additional evidence details
        /// </summary>
        [BsonElement("other_evidence_details")]
        [BsonIgnoreIfNull]
        public string OtherEvidenceDetails { get; set; }

        /// <summary>
        /// Gets or sets files containing decision making evidence
        /// </summary>
        [BsonElement("decision_making_evidence_files")]
        public List<FileSchema> DecisionMakingEvidenceFiles { get; set; }

        /// <summary>
        /// Gets or sets files containing business plan documents
        /// </summary>
        [BsonElement("business_plan_files")]
        public List<FileSchema> BusinessPlanFiles { get; set; }

        /// <summary>
        /// Gets or sets files containing other supporting evidence
        /// </summary>
        [BsonElement("other_evidence_files")]
        public List<FileSchema> OtherEvidenceFiles { get; set; }

        #endregion
    }

    // Supporting classes
    /// <summary>
    /// Represents a director of the business entity
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class DirectorSchema
    {
        /// <summary>
        /// Gets or sets the director's full name
        /// </summary>
        [Required]
        [BsonElement("full_name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the director's position or title
        /// </summary>
        [BsonElement("relation_to_entity")]
        public string RelationToEntity { get; set; }

        /// <summary>
        /// Gets or sets the director's qualification
        /// </summary>
        [BsonElement("qualification")]
        public string Qualification { get; set; }

        /// <summary>
        /// Gets or sets whether the director is resident in Bahamas.
        /// </summary>
        [BsonElement("resident_in_bah")]
        public bool IsResidentInBahamas { get; set; }

        /// <summary>
        /// Gets or sets whether the director attended_board_meetings
        /// </summary>
        [BsonElement("attended_board_meetings")]
        public bool AttendedBoardMeetings { get; set; }

        /// <summary>
        /// Gets or sets whether the director is resident in Bahamas
        /// </summary>
        [BsonElement("physically_present_in_bah")]
        public bool? PhysicallyPresentInBah { get; set; }

        /// <summary>
        /// Gets or sets whether the director is resident in Bahamas
        /// </summary>
        [BsonElement("meeting_number")]
        public List<int> MeetingNumber { get; set; }

        /// <summary>
        /// Gets or sets whether the director is resident in Bahamas
        /// </summary>
        [BsonElement("years_of_experience")]
        public int? YearsOfExperience { get; set; }
    }

    /// <summary>
    /// Represents an employee of the business entity
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class EmployeeSchema
    {
        /// <summary>
        /// Gets or sets the unique identifier for the employee
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the employee's full name
        /// </summary>
        [Required]
        [BsonElement("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the employee's job title or position
        /// </summary>
        [BsonElement("qualification")]
        public string Qualification { get; set; }

        /// <summary>
        /// Gets or sets whether the director is resident in Bahamas
        /// </summary>
        [BsonElement("years_of_experience")]
        public int? YearsOfExperience { get; set; }

        /// <summary>
        /// Gets or sets the employee's employment type (full-time, part-time, contract)
        /// </summary>
        [BsonElement("contract_type")]
        public string ContractType { get; set; }
    }

    /// <summary>
    /// Represents premises used by the business entity
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class PremisesSchema
    {
        /// <summary>
        /// Gets or sets the address of the premises
        /// </summary>
        [BsonElement("address_line1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// Gets or sets the address of the premises
        /// </summary>
        [BsonElement("address_line")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// Gets or sets the address of the premises
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }
    }

    /// <summary>
    /// Represents a Core Income Generating Activity (CIGA)
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class CigaActivitySchema
    {
        /// <summary>
        /// Gets or sets the code of the CIGA activity
        /// </summary>
        [Required]
        [BsonElement("code")]
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the description of the CIGA activity
        /// </summary>
        [Required]
        [BsonElement("description")]
        public string Description { get; set; }
    }

    /// <summary>
    /// Represents an outsourcing service provider
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class OutsourcingProviderSchema
    {
        /// <summary>
        /// Gets or sets the entity_name of the outsourcing provider company
        /// </summary>
        [Required]
        [BsonElement("entity_name")]
        public string EntityName { get; set; }

        /// <summary>
        /// Gets or sets the resource_details of the outsourcing provider company
        /// </summary>
        [Required]
        [BsonElement("resource_details")]
        public string ResourceDetails { get; set; }

        /// <summary>
        /// Gets or sets the staff count of the outsourcing provider company
        /// </summary>
        [Required]
        [BsonElement("staff_count")]
        public long? StaffCount { get; set; }

        /// <summary>
        /// Gets or sets the name of the outsourcing provider company
        /// </summary>
        [Required]
        [BsonElement("monitoring_control")]
        public bool MonitoringControl { get; set; }

        /// <summary>
        /// Gets or sets the physical_address of the outsourcing provider company
        /// </summary>
        [Required]
        [BsonElement("physical_address")]
        public string PhysicalAddress { get; set; }

        /// <summary>
        /// Gets or sets the monitoring_control_explanation of the outsourcing provider company
        /// </summary>
        [Required]
        [BsonElement("monitoring_control_explanation")]
        public string MonitoringControlExplanation { get; set; }
    }
}
