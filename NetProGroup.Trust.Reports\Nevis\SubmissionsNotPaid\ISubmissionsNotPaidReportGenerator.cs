using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;

namespace NetProGroup.Trust.Reports.Nevis.SubmissionsNotPaid
{
    /// <summary>
    /// Interface for the submissions not paid report generator.
    /// </summary>
    public interface ISubmissionsNotPaidReportGenerator : ITransientService
    {
        /// <summary>
        /// Generates the payment report for today.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public Task<SubmissionsNotPaidReportOutput> GenerateSubmissionsNotPaidReportAsync();

        /// <summary>
        /// Generates the name of the report for today.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public string GenerateReportNameForSubmissionsNotPaidAsync();
    }
}