using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Tax residenty schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public sealed class AdditionalParentsSchema
    {
        /// <summary>
        /// Gets or sets the parent_type.
        /// </summary>
        [BsonElement("parent_type")]
        public string ParentType { get; set; }

        /// <summary>
        /// Gets or sets the parent_name.
        /// </summary>
        [BsonElement("parent_name")]
        public string ParentName { get; set; }

        /// <summary>
        /// Gets or sets the alternative_name.
        /// </summary>
        [BsonElement("alternative_name")]
        public string AlternativeName { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction.
        /// </summary>
        [BsonElement("jurisdiction")]
        public string Jurisdiction { get; set; }

        /// <summary>
        /// Gets or sets the incorporation_number.
        /// </summary>
        [BsonElement("incorporation_number")]
        public string IncorporationNumber { get; set; }

        /// <summary>
        /// Gets or sets the TIN.
        /// </summary>
        [BsonElement("TIN")]
        public string TIN { get; set; }
    }
}
