// <copyright file="BasicFinancialReportRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.Domain.Shared.Reports;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.BasicFinancialReport.Populators
{
    /// <summary>
    /// Populate a row for the basic financial report module report.
    /// </summary>
    public class BasicFinancialReportRowPopulator : LinePopulatorBase, IBasicFinancialReportRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Set the value if the submission is using the reporting tool
            SetCellValueAndStyle(worksheet, currentRow, 1, data.FormDocument.Attributes.FirstOrDefault(a =>
                    a.Key == WellKnownFormDocumentAttibuteKeys.UseAccountingTool)?.Value);

            // Set financial period end date
            SetCellValueAndStyle(worksheet, currentRow, 2, data.EndsAt.Value.ToString(WellKnownReportConstants.DateFormat));

            // Company Name
            SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Name);

            // Company Entity number
            SetCellValueAndStyle(worksheet, currentRow, 4, data.LegalEntity.Code);

            // Master client code
            SetCellValueAndStyle(worksheet, currentRow, 5, data.LegalEntity.MasterClient.Code);

            // Company incorporation number
            SetCellValueAndStyle(worksheet, currentRow, 6, data.LegalEntity.IncorporationNr);

            // Set the submission status
            SetCellValueAndStyle(worksheet, currentRow, 7, data.IsPaid ? "PAID" : data.Status.ToString());

            // Set the creation date
            SetCellValueAndStyle(worksheet, currentRow, 8, FormatDateAsLocalTime(data.CreatedAt, data));

            // Set the submitted date
            SetCellValueAndStyle(worksheet, currentRow, 9, FormatDateAsLocalTime(data.SubmittedAt, data));

            var paymentInformation = data.GetPaidPayment();

            if (paymentInformation != null)
            {
                // Set the payment date
                SetCellValueAndStyle(worksheet, currentRow, 10, FormatDateAsLocalTime(paymentInformation.PaidAt, data));
                // Set the payment reference
                SetCellValueAndStyle(worksheet, currentRow, 11, paymentInformation.Reference);
            }
        }
    }
}