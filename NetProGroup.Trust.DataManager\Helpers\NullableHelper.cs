// <copyright file="NullableHelper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

#nullable enable
namespace NetProGroup.Trust.DataManager.Helpers
{
    /// <summary>
    /// Helper class for nullable types.
    /// </summary>
    public static class NullableHelper
    {
        // Custom method to safely access a property of a potentially null object

        /// <summary>
        /// Safely access a property of a potentially null object.
        /// </summary>
        /// <param name="source">The source object.</param>
        /// <param name="selector">The selector for the property.</param>
        /// <typeparam name="TSource">The type of the source object.</typeparam>
        /// <typeparam name="TResult">The type of the result.</typeparam>
        /// <returns>result of the selector.</returns>
        public static TResult? SafeAccess<TSource, TResult>(TSource? source, Func<TSource, TResult> selector)
            where TSource : class
        {
            ArgumentNullException.ThrowIfNull(selector, nameof(selector));
            return source == null ? default(TResult?) : selector(source);
        }
    }
}