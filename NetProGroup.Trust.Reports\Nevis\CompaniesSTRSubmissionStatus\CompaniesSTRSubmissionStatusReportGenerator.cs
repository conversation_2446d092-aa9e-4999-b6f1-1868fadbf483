// <copyright file="CompaniesStrSubmissionStatusReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Nevis.CompaniesSTRSubmissionStatus.Populators;

namespace NetProGroup.Trust.Reports.Nevis.CompaniesSTRSubmissionStatus
{
    /// <summary>
    /// Generates a report of companies and for each financial year, whether they have a submission.
    /// </summary>
    public class CompaniesStrSubmissionStatusReportGenerator : ICompaniesStrSubmissionStatusReportGenerator
    {
        private const string TemplateName = "companies-str-submission-status";
        private const string ReportName = "companies-str-submission-status";

        private readonly ICompaniesStrSubmissionStatusRowPopulator _companiesStrSubmissionStatusRowPopulator;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IExcelTemplateService<LegalEntity> _excelTemplateService;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;
        private readonly IModulesRepository _modulesRepository;
        private readonly ISubmissionReportsDataManager _submissionReportsDataManager;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompaniesStrSubmissionStatusReportGenerator"/> class.
        /// </summary>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="submissionReportsDataManager">DataManager instance for submission reports.</param>
        /// <param name="companiesStrSubmissionStatusRowPopulator">The regular fee line populator.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="templateProvider">The template provider.</param>
        /// <param name="legalEntitiesDataManager">The legal entities data manager.</param>
        /// <param name="modulesRepository">The modules repository.</param>
        public CompaniesStrSubmissionStatusReportGenerator(
            IDateTimeProvider dateTimeProvider,
            ISubmissionReportsDataManager submissionReportsDataManager,
            ICompaniesStrSubmissionStatusRowPopulator companiesStrSubmissionStatusRowPopulator,
            IExcelTemplateService<LegalEntity> excelTemplateService,
            IReportTemplateProvider templateProvider,
            ILegalEntitiesDataManager legalEntitiesDataManager,
            IModulesRepository modulesRepository)
        {
            _dateTimeProvider = dateTimeProvider;
            _submissionReportsDataManager = submissionReportsDataManager;
            _companiesStrSubmissionStatusRowPopulator = companiesStrSubmissionStatusRowPopulator;
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
            _legalEntitiesDataManager = legalEntitiesDataManager;
            _modulesRepository = modulesRepository;
        }

        /// <inheritdoc />
        public async Task<ReportOutput> GenerateReportAsync()
        {
            var companies = await GetNevisCompanies();

            var allSubmissionYears = await GetSubmissionYears();

            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateCompanyExcelReport(workbook, companies, allSubmissionYears);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new ReportOutput(stream.ToArray());
        }

        /// <inheritdoc />
        public string GenerateReportNameForTodayAsync()
        {
            return $"{ReportName}-{_dateTimeProvider.NevisNow:yyyy-MM-dd}";
        }

        private async Task<IReadOnlyCollection<int>> GetSubmissionYears()
        {
            var strModule = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.SimplifiedTaxReturn);
            var allSubmissionYears = (await _submissionReportsDataManager.GetAllSubmissionYears(new AllSubmissionYearsRequest { ModuleId = strModule.Id })).Years;
            return allSubmissionYears;
        }

        /// <summary>
        /// Creates an Excel report for the given companies.
        /// </summary>
        /// <param name="workbook">The workbook to add the companies to.</param>
        /// <param name="companies">The companies to add.</param>
        /// <param name="allSubmissionYears">The years for which submissions could be available.</param>
        private void CreateCompanyExcelReport(XLWorkbook workbook, List<LegalEntity> companies, IReadOnlyCollection<int> allSubmissionYears)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per company
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };

            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_companiesStrSubmissionStatusRowPopulator);

            for (var i = 0; i < allSubmissionYears.Count; i++)
            {
                // If there is a next year, copy the current worksheet so that the blank template is available for the next year
                if (i + 1 < allSubmissionYears.Count)
                {
                    var nextYear = allSubmissionYears.ElementAt(i + 1);
                    workbook.Worksheet(i + 1).CopyTo(workbook, $"{nextYear}");
                }

                _excelTemplateService.ApplyTemplate(workbook, companies, templateConfig, i);
            }
        }

        private async Task<List<LegalEntity>> GetNevisCompanies()
        {
            // Get the companies with their submissions
            return (await _legalEntitiesDataManager.GetNevisCompaniesWithSubmissions()).ToList();
        }
    }
}