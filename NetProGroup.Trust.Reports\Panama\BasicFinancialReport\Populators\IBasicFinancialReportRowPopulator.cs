﻿// <copyright file="IBasicFinancialReportRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Panama.BasicFinancialReport.Populators
{
    /// <summary>
    /// Interface for the basic financial report submissions row populator.
    /// </summary>
    public interface IBasicFinancialReportRowPopulator : ITemplateRowPopulator<Submission>, ITransientService;
}