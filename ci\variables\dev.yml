# Development environment variables
variables:
  # SQL Server configuration
  serverName: 'sqlsrv-pcp-dev-eus2.database.windows.net'
  databaseName: 'sqldb-pcp-dev'
  
  # Azure App Service configuration
  webAppName: 'app-pcp-api-dev-eus2'
  resourceGroup: 'rg-npdev-pcp-app-eus2'
  slotName: 'dev'
  
  # Agent pool
  pool: 'TT PCP - WindowsAgents dev'
  
  # Environment settings
  environment: 'Development'
