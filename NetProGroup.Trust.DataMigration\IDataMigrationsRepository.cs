﻿// <copyright file="IDataMigrationsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Interface for the DataMigration repository.
    /// </summary>
    public interface IDataMigrationsRepository : IRepository<Domain.DataMigrations.DataMigration, Guid>, IRepositoryService
    {
        /// <summary>
        /// Gets the DbContext of the repository.
        /// </summary>
        DbContext DbContext { get; }
    }
}
