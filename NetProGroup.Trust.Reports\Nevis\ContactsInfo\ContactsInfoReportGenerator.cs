// <copyright file="ContactsInfoReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Nevis.ContactsInfo.Populators;

namespace NetProGroup.Trust.Reports.Nevis.ContactsInfo
{
    /// <summary>
    /// Generates a report of master client users with their associated master client codes and company counts.
    /// </summary>
    public class ContactsInfoReportGenerator : IContactsInfoReportGenerator
    {
        private const string TemplateName = "contacts-info";
        private const string ReportName = "contacts-info";

        private readonly ITemplateRowPopulator<ContactsInfoData> _contactsInfoReportRowPopulator;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IExcelTemplateService<ContactsInfoData> _excelTemplateService;
        private readonly IReportTemplateProvider _templateProvider;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ContactsInfoReportGenerator"/> class.
        /// </summary>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="contactsInfoReportRowPopulator">The contact info report row populator.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="templateProvider">The template provider.</param>
        /// <param name="legalEntitiesDataManager">The legal entities data manager.</param>
        public ContactsInfoReportGenerator(
            IDateTimeProvider dateTimeProvider,
            IContactsInfoReportRowPopulator contactsInfoReportRowPopulator,
            IExcelTemplateService<ContactsInfoData> excelTemplateService,
            IReportTemplateProvider templateProvider,
            ILegalEntitiesDataManager legalEntitiesDataManager)
        {
            _dateTimeProvider = dateTimeProvider;
            _contactsInfoReportRowPopulator = contactsInfoReportRowPopulator;
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
            _legalEntitiesDataManager = legalEntitiesDataManager;
        }

        /// <inheritdoc />
        public async Task<ReportOutput> GenerateReportAsync()
        {
            var users = await GetMasterClientUsers();
            users = users.Where(u => IsClientUser(u.User)).ToList();

            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateContactsInfoExcelReport(workbook, users);

            var modifiedStream = new MemoryStream();
            workbook.SaveAs(modifiedStream);

            return new ReportOutput(modifiedStream.ToArray());
        }

        /// <inheritdoc />
        public string GenerateReportNameForTodayAsync()
        {
            return $"{ReportName}-{_dateTimeProvider.NevisNow:yyyy-MM-dd}";
        }

        private static bool IsClientUser(ApplicationUser user)
        {
            if (user != null)
            {
                return user.ApplicationUserRoles.Any(aur => aur.RoleId == WellKnownRoleIds.Client);
            }

            return false;
        }

        /// <summary>
        /// Creates an Excel report for the given master client users.
        /// </summary>
        /// <param name="workbook">The workbook to add the users to.</param>
        /// <param name="users">The users to add.</param>
        private void CreateContactsInfoExcelReport(XLWorkbook workbook, List<ContactsInfoData> users)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per user
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };

            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_contactsInfoReportRowPopulator);
            _excelTemplateService.ApplyTemplate(workbook, users, templateConfig, 0);
        }

        private async Task<List<ContactsInfoData>> GetMasterClientUsers()
        {
            var legalEntities = await _legalEntitiesDataManager.GetNevisLegalEntitiesWithMasterClientUsersAsync();
            var data = legalEntities.SelectMany(le => le.MasterClient.MasterClientUsers)
                                    .GroupBy(mcu => mcu.User)
                                    .Select(group => new ContactsInfoData(
                                        group.Key,
                                        group.Select(mcu => mcu.MasterClient.Code),
                                        group.SelectMany(mcu => mcu.MasterClient.LegalEntities).Distinct().Count()))
                                    .OrderByDescending(tuple => tuple.TotalCompanies)
                                    .ToList();

            return data;
        }
    }
}
