// <copyright file="SubmissionDataReportBankAccountsRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportBankAccountsRowPopulator : LinePopulatorBase, ISubmissionDataReportBankAccountsRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the bnack details
            var bankDetails = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.CashBankAccounts, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (bankDetails.Count > 1)
            {
                // Group the properties by the index
                var banckDetailGroups = bankDetails.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.CashBankAccounts + ".")[1].Split(".")[0]);

                foreach (var group in banckDetailGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the account type
                    SetCellValueAndStyle(worksheet, currentRow, 4, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.AccountName, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the account description
                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.BankName, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the amount
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }
        }
    }
}