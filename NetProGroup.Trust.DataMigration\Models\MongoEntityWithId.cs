// <copyright file="MongoEntityWithId.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models;

/// <summary>
/// Represents a MongoDB entity with an ID.
/// </summary>
public class MongoEntityWithId
{
    /// <summary>
    /// Gets or sets the unique identifier for the entry.
    /// </summary>
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
}