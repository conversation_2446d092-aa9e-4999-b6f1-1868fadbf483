// <copyright file="DeclarationRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class DeclarationRowPopulator : LinePopulatorBase, IDeclarationRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);

            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = @"relevant-activity-declaration\.relevantActivities\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            // Retrieve the entity unique id
            SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

            // Retrieve the entity taxpayer id
            SetCellValueAndStyle(worksheet, currentRow, 2, GetValueOrDefault(form, FormKeys.EntityDetailsTin));

            // Retrieve if the physical business address is the same as the registered address
            SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.EntityDetailsSameAddress));

            // Retrieve the address line 1
            SetCellValueAndStyle(worksheet, currentRow, 4, GetValueOrDefault(form, FormKeys.EntityAddress));

            // Retrieve the address apt or unit
            SetCellValueAndStyle(worksheet, currentRow, 5, GetValueOrDefault(form, FormKeys.EntityAptUnit));

            // Retrieve the address country code
            SetCellValueAndStyle(worksheet, currentRow, 6, GetValueOrDefault(form, FormKeys.EntityCountry));

            // Retrieve if an application has been made and confirmed with the Ministry of Finance to change the financial period
            SetCellValueAndStyle(worksheet, currentRow, 7, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsFirstFinancialReport));

            // Retrieve the financial period
            SetCellValueAndStyle(worksheet, currentRow, 8, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsStartDate));

            SetCellValueAndStyle(worksheet, currentRow, 9, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

            // Retrieve if the entity is subject to reclassification
            SetCellValueAndStyle(worksheet, currentRow, 10, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsIsReclassifiedToPEH));


            int currentIndex = 0;
            var selectedRelevantActivities = new List<string>();

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var activityName = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 11, activityName);

                    // Store the relevant activity label
                    selectedRelevantActivities.Add(activityName);

                    // Check if the activity was carried for a partial part of the financial period
                    var carriedForOnlyPartOfFinancialPeriod = GetValueOrDefault(form, FormKeys.RelevantActivitiesIsPartialFinancialPeriod(index));

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 12, carriedForOnlyPartOfFinancialPeriod);

                    // Retrieve the activity period
                    var activityStartDate = GetValueOrDefault(form, FormKeys.RelevantActivitiesStartDate(index));

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 13, activityStartDate);

                    var activityEndDate = GetValueOrDefault(form, FormKeys.RelevantActivitiesEndDate(index));

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 14, activityEndDate);

                    currentIndex += 1;
                }
            }

            // Retrieve if the user is a 100% Bahamian
            SetCellValueAndStyle(worksheet, currentRow, 15, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationIsBahamianResident));

            // Retrieve if the user is an investment fund
            SetCellValueAndStyle(worksheet, currentRow, 16, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationIsInvestmentFund));

            // Retrieve the annual gross income
            SetCellValueAndStyle(worksheet, currentRow, 17, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationTotalAnnualGross));

            // Retrieve the entity is part of the MNE group
            SetCellValueAndStyle(worksheet, currentRow, 18, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationIsPartOfMneGroup));

            // Retrieve the MNE group name
            SetCellValueAndStyle(worksheet, currentRow, 19, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationNameOfNmeGroup));

            // Retrieve if the entity intend to make a tax residency claim outside Bahamas
            SetCellValueAndStyle(worksheet, currentRow, 20, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationIntendsToClaimTaxResidencyOutsideBahamas));

            // Retrieve the jurisdiction selected
            SetCellValueAndStyle(worksheet, currentRow, 21, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationTaxResidencyJurisdiction));

            // Retrieve the tax payer identification number
            SetCellValueAndStyle(worksheet, currentRow, 22, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationTaxPayerIdentificationNumber));

            // Retrieve if the entity has ultimate parent entity
            SetCellValueAndStyle(worksheet, currentRow, 23, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationHasUltimateParentEntity));

            // Retrieve if the entity has immediate parent entity
            SetCellValueAndStyle(worksheet, currentRow, 24, GetValueOrDefault(form, FormKeys.TaxPayerIdentificationHasImmediateParentEntity));

            int auxIndex = 0;

            // Retrieve the relevant activities details

            foreach (var relevantActivity in selectedRelevantActivities)
            {
                if (relevantActivity == WellKnownBahamasRelevantActivities.None)
                {
                    continue;
                }

                string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);
                
                // Retrieve the gross income
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 25, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTotalGrossIncome}"));

                //// Retrieve the net book values
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 26, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessNetBookValuesAssets}"));

                // Retrieve the asset description
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 27, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessAssetsDescriptionBahamas}"));

                // Retrieve if the activity is directed and managed in Bahamas
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 28, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessIsDirectedAndManagedInBahamas}"));

                // Retrieve the number of board meetings
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 29, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessNumberOfMeetings}"));

                // Retrieve the number of board meetings in Bahamas
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 30, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessNumberOfMeetingsInBahamas}"));

                // Retrieve the Quorum for board meetings
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 31, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessQuorumDirectors}"));

                // Retrieve if any of the meeting held in bahamas the quorum was physically present
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 32, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessQuorumPhysicallyPresent}"));

                // Retrieve if the minutes were kept in bahamas
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 33, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessAreMinutesKeptInBahamas}"));

                // Retrieve the total expenditure
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 34, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTotalExpenditureRelevantActivity}"));

                // Retrieve the total bahamas expenditure
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 35, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTotalExpenditureBahamas}"));

                // Retrieve the total of employees
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 36, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTotalEmployeesEntity}"));

                // Retrieve the total of employees for the relevant activity
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 37, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTotalEmployeesRelevantActivity}"));

                // Retrieve the total of employees for the relevant activity present in Bahamas
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 38, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTotalEmployeesBahamas}"));

                // Retrieve if there is any CIGA
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 39, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessHasCiga}"));

                // Retrieve the CIGA income
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 40, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessCigaOutsourcingProportion}"));

                // Retrieve the CIGA expenditure
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 41, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessBahamasOutsourcingExpenditure}"));

                // Retrieve if the company comply with Bahamas laws and regulations
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 42, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessIsCompliantWithBahamasLawsAndRegulations}"));

                // Retrieve if the company is a high-risk intellectual property
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 43, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessIsHighRiskIpEntity}"));

                // Retrieve the relevant IP asset
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 44, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessRelevantIpAsset}"));

                // Retrieve the IP explanation
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 45, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessIncomeGenerationExplanation}"));

                // Retrieve the employee responsibility
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 46, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessEmployeeResponsibility}"));

                // Retrieve the nature and history of the strategic decisions
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 47, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessStrategicDecisionsBahamas}"));

                // Retrieve the nature and history of the trading activities
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 48, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessTradingActivitiesBahamas}"));

                // Retrieve the gross income through royalties
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 49, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessGrossIncomeRoyalties}"));

                // Retrieve the gross income through sales
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 50, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessGrossIncomeSaleIpAsset}"));

                // Retrieve the gross income through others
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 51, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessGrossIncomeOtherSources}"));

                // Retrieve the detailed business plans
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 52, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessBusinessPlanExplanation}"));

                // Retrieve the concrete decision making evidence
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 53, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessDecisionMakingEvidenceExplanation}"));

                // Retrieve the other facts
                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 54, GetValueOrDefault(form, $"{relevantActivityKey}{FormKeys.BusinessAdditionalComplianceExplanation}"));

                auxIndex += 1;
            }

            // Retrieve the additionsl supporting comments
            SetCellValueAndStyle(worksheet, currentRow, 55, GetValueOrDefault(form, FormKeys.SupportingDetailsAdditionalComments));

            currentRow += auxIndex;
        }
    }
}