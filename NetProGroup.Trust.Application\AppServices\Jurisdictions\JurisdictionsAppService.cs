﻿// <copyright file="JurisdictionsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.DataManager.Jurisdictions;
using NetProGroup.Trust.DataManager.Jurisdictions.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Jurisdictions
{
    /// <summary>
    /// Application service.
    /// </summary>
    public class JurisdictionsAppService : IJurisdictionsAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IJurisdictionsDataManager _dataManager;
        private readonly IFormsDataManager _formsDataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionsAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="formsDataManager">The forms data manager.</param>
        /// <param name="securityManager">The security manager.</param>
        public JurisdictionsAppService(ILogger<JurisdictionsAppService> logger,
                                       IMapper mapper,
                                       IJurisdictionsDataManager dataManager,
                                       IFormsDataManager formsDataManager,
                                       ISecurityManager securityManager)
        {
            _logger = logger;
            _mapper = mapper;
            _dataManager = dataManager;
            _formsDataManager = formsDataManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc />
        public async Task<JurisdictionDTO> CreateJurisdictionAsync(CreateJurisdictionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireManagementUserAsync();

            return await _dataManager.CreateJurisdictionAsync(model);
        }

        /// <inheritdoc />
        public async Task<JurisdictionDTO> UpdateJurisdictionAsync(JurisdictionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireManagementAccessToJurisdictionAsync(model.Id);

            return await _dataManager.UpdateJurisdictionAsync(model);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<JurisdictionDTO>> GetJurisdictionsAsync(int pageNumber, int pageSize)
        {
            var jurisdictions = await _securityManager.GetAllJurisdictionsForManagementPermissionsAsync();

            var response = await _dataManager.ListJurisdictionsAsync(new ListJurisdictionsRequest
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                AuthorizedJurisdictionIDs = jurisdictions.Select(j => j.Id).ToList()
            });

            return response.JurisdictionItems;
        }

        /// <inheritdoc/>
        public async Task<ListFormTemplatesDTO> GetFormTemplatesByJurisdictionAsync(Guid jurisdictionId)
        {
            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var request = new DataManager.Forms.RequestResponses.ListFormTemplatesRequest
            {
                JurisdictionId = jurisdictionId,
                PageNumber = 1,
                PageSize = int.MaxValue
            };

            var response = await _formsDataManager.ListFormTemplatesAsync(request);

            return new ListFormTemplatesDTO { FormTemplates = response.FormTemplateItems };
        }
    }
}
