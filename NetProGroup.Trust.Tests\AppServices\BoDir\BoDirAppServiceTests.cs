﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.BoDir
{
    public class BoDirAppServiceTests : TestBase
    {
        private IBoDirAppService _boDirService;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private LegalEntity _nevisLegalEntity;
        private LegalEntity _bahamasLegalEntity;

        [SetUp]
        public async Task SetUp()
        {
            _boDirService = _server.Services.GetRequiredService<IBoDirAppService>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();

            await Seed();
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenProductionOfficeIsSet_ReturnsBoFromLegalEntityWithMatchingProductionOffice()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            // Create beneficial owners in different legal entities, which have different production offices
            var nevisBeneficialOwner = SeedCompleteBeneficialOwner(_nevisLegalEntity);
            var bahamasBeneficialOwner = SeedCompleteBeneficialOwner(_bahamasLegalEntity);

            // Search using production office filter (BoDirItemDTO.ProductionOffice is mapped from LegalEntity.ProductionOffice)
            var request = new SearchBoDirRequestDTO { ProductionOffice = ProductionOfficeType.TNEV };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Id.Should().Be(nevisBeneficialOwner.Id);
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenPositionIsSetToDirector_ReturnsDirectors()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity);
            var beneficialOwner = SeedCompleteBeneficialOwner(_nevisLegalEntity);

            var request = new SearchBoDirRequestDTO { Position = BoDirPosition.Director };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            var result = results.Should().ContainSingle().Subject;
            result.Id.Should().Be(director.Id);
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenPositionIsSetToBeneficialOwner_ReturnsBeneficialOwners()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity);
            var beneficialOwner = SeedCompleteBeneficialOwner(_nevisLegalEntity);

            var request = new SearchBoDirRequestDTO { Position = BoDirPosition.BeneficialOwner };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            var result = results.Should().ContainSingle().Subject;
            result.Id.Should().Be(beneficialOwner.Id);
        }

        [Test]
        [TestCase(BoDirSpecifics.NoBoDirInformation, "NoBoDirLegalEntity", TestName = "SearchBoDirsAsync_WhenSpecificsIsSetToNoBoDirInformation_ReturnsCompanyLLC")]
        [TestCase(BoDirSpecifics.BoDirInformation, "2", TestName = "SearchBoDirsAsync_WhenSpecificsIsSetToBoDirInformation_ReturnsDirectorNamed2")]
        [TestCase(BoDirSpecifics.MissingInformation, "3", TestName = "SearchBoDirsAsync_WhenSpecificsIsSetToMissingInformation_ReturnsDirectorNamed3")]

        public async Task SearchBoDirsAsync_WhenSpecificsIsSet_FiltersBySpecifics(BoDirSpecifics specificsToRequest, string expectedEntityName)
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var legalEntity = await SeedLegalEntity(JurisdictionNevisId, "Nevis2", "NoBoDirLegalEntity");

            // Setup legal entities and directors
            var completeDirector = SeedCompleteIndividualDirector(_nevisLegalEntity, "2");

            // Setup incomplete director
            var incompleteDirector = SeedCompleteIndividualDirector(_nevisLegalEntity, "3");
            ClearProperty(nameof(Director.AppointmentDate), incompleteDirector);
            await _legalEntitiesRepository.UpdateAsync(_nevisLegalEntity, true);

            var request = new SearchBoDirRequestDTO { Specifics = [specificsToRequest] };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            var result = results.Should().ContainSingle().Subject;
            result.Name.Should().Be(expectedEntityName);
        }

        [Test]
        [TestCase(BoDirDataStatus.Initial, LegalEntityRelationStatus.Initial, LegalEntityRelationStatus.Confirmed)]
        [TestCase(BoDirDataStatus.Confirmed, LegalEntityRelationStatus.Confirmed, LegalEntityRelationStatus.Initial)]
        [TestCase(BoDirDataStatus.Subsequent, LegalEntityRelationStatus.UpdateReceived, LegalEntityRelationStatus.Confirmed)]
        [TestCase(BoDirDataStatus.PendingUpdateRequest, LegalEntityRelationStatus.PendingUpdateRequest, LegalEntityRelationStatus.Confirmed)]
        [TestCase(BoDirDataStatus.Refreshed, LegalEntityRelationStatus.Refreshed, LegalEntityRelationStatus.Confirmed)]
        public async Task SearchBoDirsAsync_WhenDataStatusesIsSet_FiltersByDataStatuses(
            BoDirDataStatus filterStatus,
            LegalEntityRelationStatus expectedStatus,
            LegalEntityRelationStatus differentStatus)
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var expectedDirector = SeedCompleteIndividualDirector(_nevisLegalEntity, "1");
            var OtherDirector = SeedCompleteIndividualDirector(_nevisLegalEntity, "Director_with_different_status");

            // Set status for director and beneficial owner histories
            expectedDirector.DirectorHistories.First().Status = expectedStatus;
            OtherDirector.DirectorHistories.First().Status = differentStatus;

            await _legalEntitiesRepository.UpdateAsync(_nevisLegalEntity, true);

            var request = new SearchBoDirRequestDTO
            {
                DataStatuses = new List<BoDirDataStatus> { filterStatus }
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle(x => x.Id == expectedDirector.Id);
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenConfirmedDateRangeIsSet_FiltersByConfirmedDate()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var directorEarly = SeedCompleteIndividualDirector(_nevisLegalEntity, "Early Director");
            var directorLate = SeedCompleteIndividualDirector(_nevisLegalEntity, "Late Director");

            // Set confirmed dates for director histories
            var earlyConfirmedDate = DateTime.UtcNow.AddDays(-60);
            var lateConfirmedDate = DateTime.UtcNow.AddDays(-10);

            directorEarly.DirectorHistories.First().ConfirmedAt = earlyConfirmedDate;
            directorLate.DirectorHistories.First().ConfirmedAt = lateConfirmedDate;

            await _legalEntitiesRepository.UpdateAsync(_nevisLegalEntity, true);

            var request = new SearchBoDirRequestDTO
            {
                ConfirmedDateFrom = DateTime.UtcNow.AddDays(-20),
                ConfirmedDateTo = DateTime.UtcNow
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle()
                .Which.Name.Should().Be("Late Director");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsLegalEntityName_ReturnsDirectorOfThatEntity()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Director Nevis");
            var otherDirector = SeedCompleteIndividualDirector(_bahamasLegalEntity, "Director Bahamas");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = _nevisLegalEntity.Name
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Director Nevis");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsDirectorName_ReturnsMatchingDirector()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Unique Director Name");
            var otherDirector = SeedCompleteIndividualDirector(_nevisLegalEntity, "Other Director");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = "Unique Director Name"
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Unique Director Name");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsLegalEntityCode_ReturnsDirectorOfThatEntity()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Director Nevis");
            var otherDirector = SeedCompleteIndividualDirector(_bahamasLegalEntity, "Director Bahamas");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = _nevisLegalEntity.Code
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Director Nevis");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsProductionOffice_ReturnsDirectorOfThatOffice()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Director Nevis");
            var otherDirector = SeedCompleteIndividualDirector(_bahamasLegalEntity, "Director Bahamas");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = _nevisLegalEntity.ProductionOffice
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Director Nevis");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsReferralOffice_ReturnsDirectorOfThatOffice()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Director Nevis");
            var otherDirector = SeedCompleteIndividualDirector(_bahamasLegalEntity, "Director Bahamas");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = _nevisLegalEntity.ReferralOffice
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Director Nevis");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsLegacyCode_ReturnsDirectorOfThatEntity()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Director Nevis");
            var otherDirector = SeedCompleteIndividualDirector(_bahamasLegalEntity, "Director Bahamas");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = _nevisLegalEntity.LegacyCode
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Director Nevis");
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenProductionOfficeIsSetToTNEV_ReturnsBeneficialOwnerFromNevisLegalEntity()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            // Create beneficial owners in different legal entities, which have different production offices
            var nevisBeneficialOwner = SeedCompleteBeneficialOwner(_nevisLegalEntity);
            var bahamasBeneficialOwner = SeedCompleteBeneficialOwner(_bahamasLegalEntity);

            // Search using production office filter (BoDirItemDTO.ProductionOffice is mapped from LegalEntity.ProductionOffice)
            var request = new SearchBoDirRequestDTO { ProductionOffice = ProductionOfficeType.TNEV };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Id.Should().Be(nevisBeneficialOwner.Id);
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenPositionIsSetToDirector_ReturnsOnlyDirectors()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity);
            var beneficialOwner = SeedCompleteBeneficialOwner(_nevisLegalEntity);

            var request = new SearchBoDirRequestDTO { Position = BoDirPosition.Director };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            var result = results.Should().ContainSingle().Subject;
            result.Id.Should().Be(director.Id);
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenPositionIsSetToBeneficialOwner_ReturnsOnlyBeneficialOwners()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity);
            var beneficialOwner = SeedCompleteBeneficialOwner(_nevisLegalEntity);

            var request = new SearchBoDirRequestDTO { Position = BoDirPosition.BeneficialOwner };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            var result = results.Should().ContainSingle().Subject;
            result.Id.Should().Be(beneficialOwner.Id);
        }

        [Test]
        public async Task SearchBoDirsAsync_WhenConfirmedDateRangeIsSetToLast20Days_ReturnsDirectorWithRecentConfirmation()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var directorEarly = SeedCompleteIndividualDirector(_nevisLegalEntity, "Early Director");
            var directorLate = SeedCompleteIndividualDirector(_nevisLegalEntity, "Late Director");

            // Set confirmed dates for director histories
            var earlyConfirmedDate = DateTime.UtcNow.AddDays(-60);
            var lateConfirmedDate = DateTime.UtcNow.AddDays(-10);

            directorEarly.DirectorHistories.First().ConfirmedAt = earlyConfirmedDate;
            directorLate.DirectorHistories.First().ConfirmedAt = lateConfirmedDate;

            await _legalEntitiesRepository.UpdateAsync(_nevisLegalEntity, true);

            var request = new SearchBoDirRequestDTO
            {
                ConfirmedDateFrom = DateTime.UtcNow.AddDays(-20),
                ConfirmedDateTo = DateTime.UtcNow
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle()
                .Which.Name.Should().Be("Late Director");
        }


        [Test]
        public async Task SearchBoDirsAsync_WhenSearchTermIsDirectorName_ReturnsDirectorWithMatchingName()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var director = SeedCompleteIndividualDirector(_nevisLegalEntity, "Unique Director Name");
            var otherDirector = SeedCompleteIndividualDirector(_bahamasLegalEntity, "Other Director");

            var request = new SearchBoDirRequestDTO
            {
                SearchTerm = "Unique Director Name"
            };

            // Act
            var results = await _boDirService.SearchBoDirsAsync(request);

            // Assert
            results.Should().ContainSingle().Which.Name.Should().Be("Unique Director Name");
        }

        private Director SeedCompleteIndividualDirector(LegalEntity legalEntity, string name = "Complete Individual Director")
        {
            var director = BuildCompleteIndividualDirector(name);
            legalEntity.Directors.Add(director);

            _legalEntitiesRepository.Update(legalEntity, true);
            return director;
        }

        private static Director BuildCompleteIndividualDirector(string name) => new Director
        {
            Name = name,
            Code = "COMPLETE_DIRECTOR",
            IsIndividual = true,
            RelationType = "Director",
            AppointmentDate = DateTime.UtcNow.AddYears(-1),
            DateOfBirth = DateTime.UtcNow.AddYears(-30),
            CountryOfBirth = "United States",
            Nationality = "American",
            ResidentialAddress = "123 Main St, New York, NY",
            OfficerTypeName = "Director",
            DirectorHistories = new List<DirectorHistory>
                {
                    new DirectorHistory
                    {
                        Name = name,
                        Code = "D_COMPLETE",
                        Status = LegalEntityRelationStatus.Confirmed,
                        ConfirmedAt = DateTime.UtcNow.AddDays(-30),
                        CreatedAt = DateTime.UtcNow.AddDays(-60),
                        RelationType = "Director",
                        FileType = "Default",
                    }
                },
            FileType = "Default"
        };

        private BeneficialOwner SeedCompleteBeneficialOwner(LegalEntity legalEntity,
            string completeBeneficialOwner = "Complete Beneficial Owner")
        {
            var beneficialOwner = new BeneficialOwner
            {
                Name = completeBeneficialOwner,
                Code = "BO_COMPLETE",
                IsIndividual = true,
                OfficerTypeCode = "KNTP01",
                OfficerTypeName = "KNTP01",
                IncorporationNr = "BO-123456789",
                AppointmentDate = DateTime.UtcNow.AddYears(-1),
                DateOfBirth = DateTime.UtcNow.AddYears(-35),
                CountryOfBirth = "Canada",
                Nationality = "Canadian",
                ResidentialAddress = "789 Maple St, Toronto, ON",
                ServiceAddress = "321 Corporate Blvd, Toronto, ON",
                PlaceOfBirth = "Toronto",
                Country = "Canada",
                CountryOfFormation = "Canada",
                TIN = "***********",
                Address = "789 Maple St, Toronto, ON",
                BeneficialOwnerHistories = new List<BeneficialOwnerHistory>
                {
                    new BeneficialOwnerHistory
                    {
                        Name = completeBeneficialOwner,
                        Code = "BO_COMPLETE",
                        Status = LegalEntityRelationStatus.Confirmed,
                        ConfirmedAt = DateTime.UtcNow.AddDays(-20),
                        CreatedAt = DateTime.UtcNow.AddDays(-50),
                        FileType = "Default",
                        OfficerTypeCode = "KNTP01",
                        OfficerTypeName = "KNTP01"
                    }
                },
                FileType = "Default"
            };
            legalEntity.BeneficialOwners.Add(beneficialOwner);
            _legalEntitiesRepository.Update(legalEntity, true);
            return beneficialOwner;
        }

        private static void ClearProperty(string requiredPropertyName, Director director)
        {
            // Use reflection to set the required property to null
            var propertyInfo = typeof(Director).GetProperty(requiredPropertyName);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"Property '{requiredPropertyName}' does not exist on type 'Director'.");
            }
            // Set the property to null or empty string based on its type
            if (propertyInfo.PropertyType == typeof(string))
                propertyInfo.SetValue(director, string.Empty);
            else if (propertyInfo.PropertyType == typeof(DateTime?))
                propertyInfo.SetValue(director, null);
            else if (propertyInfo.PropertyType == typeof(int?))
                propertyInfo.SetValue(director, null);
            else if (propertyInfo.PropertyType == typeof(bool))
                propertyInfo.SetValue(director, false);
            else
                propertyInfo.SetValue(director, null);
        }

        private async Task Seed()
        {
            _nevisLegalEntity = await SeedLegalEntity(JurisdictionNevisId, "TNEV", $"LegalEntityName_Nevis Test Entity");
            _bahamasLegalEntity = await SeedLegalEntity(JurisdictionBahamasId, "TBAH", $"LegalEntityName_Bahamas Test Entity");
        }

        private async Task<LegalEntity> SeedLegalEntity(Guid jurisdictionId, string discriminator, string name)
        {
            var legalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = name,
                Code = $"LegalEntity_Code_{discriminator}_TEST",
                EntityType = LegalEntityType.Company,
                MasterClient = base._masterClient,
                JurisdictionId = jurisdictionId,
                EntityTypeName = LegalEntityTypes.IBC,
                ProductionOffice = $"ProductionOffice_{discriminator}",
                ReferralOffice = $"ReferralOffice_{discriminator}",
                LegacyCode = $"LegacyCode_LEG_{discriminator}",
                IncorporationNr = $"INC_{discriminator}_001",
                IncorporationDate = DateTime.UtcNow.AddYears(-1),
                BeneficialOwners = new List<BeneficialOwner>(),
                Directors = new List<Director>()
            };
            await _legalEntitiesRepository.InsertAsync(legalEntity, true);

            return legalEntity;
        }
    }
}
