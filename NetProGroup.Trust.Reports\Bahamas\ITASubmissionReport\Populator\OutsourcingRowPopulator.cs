// <copyright file="OutsourcingRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class OutsourcingRowPopulator : LinePopulatorBase, IOutsourcingRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);

            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = @"relevant-activity-declaration\.relevantActivities\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var relevantActivity = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Get outsourcing provider indexes for this relevant activity
                    var outsourcingProviderIndexes = GetOutsourcingProviderIndexes(form!.DataSet, relevantActivityKey);

                    List<int> GetOutsourcingProviderIndexes(Dictionary<string, string> dataSet, string activityKey)
                    {
                        var pattern = $@"{activityKey.Replace(".", "\\.", StringComparison.Ordinal)}\.outsourcingProviders\.(\d+)\.";
                        return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
                    }

                    foreach (var outsourcingProviderIndex in outsourcingProviderIndexes)
                    {
                        // Retrieve the entity unique id
                        SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                        // Retrieve the name
                        SetCellValueAndStyle(worksheet, currentRow, 2, relevantActivity);

                        SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                        // Retrieve the provider's name
                        var providerName = GetValueOrDefault(form, FormKeys.OutsourcingProvidersEntityName(relevantActivityKey, outsourcingProviderIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 4, providerName);

                        // Retrieve the details of resources deployed
                        var providerDetails = GetValueOrDefault(form, FormKeys.OutsourcingProvidersDetailsOfResources(relevantActivityKey, outsourcingProviderIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 5, providerDetails);

                        // Retrieve the number of staff
                        var numberOfStaff = GetValueOrDefault(form, FormKeys.OutsourcingProvidersNumberOfStaff(relevantActivityKey, outsourcingProviderIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 6, numberOfStaff);

                        // Retrieve if the entity is able to monitor and control the activity
                        var monitoringAndControl = GetValueOrDefault(form, FormKeys.OutsourcingProvidersMonitoringAndControl(relevantActivityKey, outsourcingProviderIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 7, monitoringAndControl);

                        // Retrieve the monitor and control explanation
                        var monitoringControlExplanation = GetValueOrDefault(form, FormKeys.OutsourcingProvidersMonitoringControlExplanation(relevantActivityKey, outsourcingProviderIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 8, monitoringControlExplanation);

                        // Retrieve the physical address
                        var physicalAddress = GetValueOrDefault(form, FormKeys.OutsourcingProvidersPhysicalAddress(relevantActivityKey, outsourcingProviderIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 9, physicalAddress);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}