// <copyright file="IContactsInfoReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Reports.Nevis.ContactsInfo
{
    /// <summary>
    /// Interface for generating contact information reports.
    /// </summary>
    public interface IContactsInfoReportGenerator : ITransientService
    {
        /// <summary>
        /// Generates a report containing contact information.
        /// </summary>
        /// <returns>The report output containing the generated report file.</returns>
        Task<ReportOutput> GenerateReportAsync();

        /// <summary>
        /// Generates a report name for today's date.
        /// </summary>
        /// <returns>The generated report name.</returns>
        string GenerateReportNameForTodayAsync();
    }
}
