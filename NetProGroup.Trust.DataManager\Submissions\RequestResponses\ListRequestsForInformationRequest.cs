// <copyright file="ListRequestsForInformationRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.Submissions.RequestResponses
{
    /// <summary>
    /// Represent the necessary data to filter submissions with created RFI requests.
    /// </summary>
    public class ListRequestsForInformationRequest : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities or masterclients.
        /// </summary>
        public string GeneralSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the id of the jurisdiction to get submissions for.
        /// </summary>
        public Guid? JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the id of the legalentity to get submissions for.
        /// </summary>
        public Guid? LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the id of the module to get submissions for.
        /// </summary>
        public Guid? ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted before a specific date.
        /// </summary>
        public DateTime? SubmittedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted after a specific date.
        /// </summary>
        public DateTime? SubmittedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission based on the company's incorporation before a specific date.
        /// </summary>
        public DateTime? CompanyIncorporatedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission based on the company's incorporation after a specific date.
        /// </summary>
        public DateTime? CompanyIncorporatedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the status of the request for information to filter by.
        /// </summary>
        public RequestForInformationStatus? Status { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the RFis to filter on are overdue.
        /// </summary>
        public bool? IsOverdue { get; set; }

        /// <summary>
        /// Gets or sets the ids of the jurisdictions to search the submissions for (via companies).
        /// </summary>
        public List<Guid> AuthorizedJurisdictionIDs { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the name of the field to sort on.
        /// </summary>
        [SortableColumns(
            nameof(ListSubmissionRFIDTO.LegalEntityName),
            nameof(ListSubmissionRFIDTO.LegalEntityCode),
            nameof(ListSubmissionRFIDTO.MasterClientCode),
            nameof(ListSubmissionRFIDTO.FinancialPeriodEndsAt),
            nameof(ListSubmissionRFIDTO.Status),
            nameof(ListSubmissionRFIDTO.ExportedAt),
            nameof(ListSubmissionRFIDTO.RFICreatedAt),
            nameof(ListSubmissionRFIDTO.RFIDeadLine),
            nameof(ListSubmissionRFIDTO.RFICompletedAt),
            nameof(ListSubmissionRFIDTO.RFILastReminderSentAt))]
        public override string SortBy { get; set; }

    }
}