﻿// <copyright file="GoLiveOptions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DataManager.Configurations
{
    /// <summary>
    /// Configuration for GoLive.
    /// </summary>
    public class GoLiveOptions
    {
        /// <summary>
        /// Gets the name of the section in the appsettings.
        /// </summary>
        public const string GoLive = "GoLive";

        /// <summary>
        /// Gets or sets the date for GoLive.
        /// </summary>
        public DateTime GoLiveDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to send the queued invitations.
        /// </summary>
        public bool SendQueuedInvitations { get; set; }
    }
}
