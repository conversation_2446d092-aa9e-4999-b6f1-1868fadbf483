// <copyright file="IAuthorizationFilterExpressionFactory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Submissions;
using System.Linq.Expressions;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Factory for creating predicates to filter entities based on authorization.
    /// Use these methods to only retrieve the data from the database the user is actually allowed to see.
    /// </summary>
    public interface IAuthorizationFilterExpressionFactory
    {
        /// <summary>
        /// Get the predicate to filter legal entities by the authorized jurisdiction IDs.
        /// </summary>
        /// <param name="request">The jurisdiction filtered request.</param>
        /// <returns>The legal entity jurisdiction filter predicate.</returns>
        Expression<Func<LegalEntity, bool>> GetLegalEntityJurisdictionFilterPredicate(IJurisdictionFilteredRequest request);

        /// <summary>
        /// Get the predicate to filter jurisdictions by the authorized jurisdiction IDs.
        /// </summary>
        /// <param name="request">The jurisdiction filtered request.</param>
        /// <returns>The jurisdiction filter predicate.</returns>
        Expression<Func<Jurisdiction, bool>> GetJurisdictionJurisdictionFilterPredicate(IJurisdictionFilteredRequest request);

        /// <summary>
        /// Gets the form template jurisdiction filter predicate.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>The form template jurisdiction filter predicate.</returns>
        Expression<Func<FormTemplate, bool>> GetFormTemplateJurisdictionFilterPredicate(IJurisdictionFilteredRequest request);

        /// <summary>
        /// Gets the master client jurisdiction filter predicate.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>The master client jurisdiction filter predicate.</returns>
        Expression<Func<MasterClient, bool>> GetMasterClientUserIdFilterPredicate(IUserIdFilteredRequest request);

        /// <summary>
        /// Get the predicate to filter invoices by the logged-in user's ID.
        /// </summary>
        /// <param name="request">The user ID filtered request.</param>
        /// <returns>The invoice user id filter predicate.</returns>
        Expression<Func<Invoice, bool>> GetInvoiceUserIdFilterPredicate(IUserIdFilteredRequest request);

        /// <summary>
        /// Get the predicate to filter payments by the logged-in user's ID.
        /// </summary>
        /// <param name="request">The user ID filtered request.</param>
        /// <returns>The payment user id filter predicate.</returns>
        Expression<Func<Domain.Payments.Payment, bool>> GetPaymentUserIdFilterPredicate(IUserIdFilteredRequest request);

        /// <summary>
        /// Get the predicate to filter submissions by the authorized jurisdiction IDs.
        /// </summary>
        /// <param name="request">The jurisdiction filtered request.</param>
        /// <returns>The submission filter predicate.</returns>
        Expression<Func<Submission, bool>> GetSubmissionJurisdictionFilterPredicate(IJurisdictionFilteredRequest request);

        /// <summary>
        /// Gets the request for information jurisdiction filter predicate.
        /// </summary>
        /// <param name="request">The jurisdiction filtered request.</param>
        /// <returns>The request for information jurisdiction filter predicate.</returns>
        Expression<Func<RequestForInformation, bool>> GetRequestForInformationJurisdictionFilterPredicate(IJurisdictionFilteredRequest request);
    }
}