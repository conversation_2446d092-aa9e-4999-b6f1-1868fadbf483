// <copyright file="FinancialReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Nevis.Financial.Populators;

namespace NetProGroup.Trust.Reports.Nevis.Financial
{
    /// <summary>
    ///  The financial report generator implementation.
    /// </summary>
    /// <remarks>
    /// This is a daily report that lists the submissions for Nevis that are not exported yet.
    /// </remarks>
    public class FinancialReportGenerator : IFinancialReportGenerator
    {
        private const string ReportName = "financial-report";
        private const string TemplateName = "financial-report";
        private const string Version = "v2";

        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IExcelTemplateService<Submission> _excelTemplateService;
        private readonly ILateFeeLinePopulator _lateFeeLinePopulator;
        private readonly IRegularFeeLinePopulator _regularFeeLinePopulator;
        private readonly ISubmissionReportsDataManager _submissionReportsDataManager;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="FinancialReportGenerator"/> class.
        /// </summary>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="submissionReportsDataManager">DataManager for submission reports.</param>
        /// <param name="regularFeeLinePopulator">The regular fee line populator.</param>
        /// <param name="lateFeeLinePopulator">The late fee line populator.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="templateProvider">The template provider.</param>
        public FinancialReportGenerator(
            IDateTimeProvider dateTimeProvider,
            ISubmissionReportsDataManager submissionReportsDataManager,
            IRegularFeeLinePopulator regularFeeLinePopulator,
            ILateFeeLinePopulator lateFeeLinePopulator,
            IExcelTemplateService<Submission> excelTemplateService,
            IReportTemplateProvider templateProvider)
        {
            _dateTimeProvider = dateTimeProvider;
            _submissionReportsDataManager = submissionReportsDataManager;
            _regularFeeLinePopulator = regularFeeLinePopulator;
            _lateFeeLinePopulator = lateFeeLinePopulator;
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
        }

        /// <inheritdoc />
        public async Task<FinancialReportOutput> GenerateTodayFinancialReportAsync()
        {
            var submissions = (await GetSubmissions()).ToList();
            if (submissions.Count == 0)
            {
                return new FinancialReportOutput(submissions);
            }

            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionExcelReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new FinancialReportOutput(stream.ToArray(), submissions);
        }

        /// <inheritdoc />
        public string GenerateReportNameForTodayAsync()
        {
            return $"{ReportName}-{_dateTimeProvider.Now:yyyy-MM-dd HH-mm-ss}-{Version}";
        }

        /// <inheritdoc />
        public async Task CompleteFinancialExport(IEnumerable<Submission> submissions, Guid reportId)
        {
            await _submissionReportsDataManager.CompleteFinancialExport(submissions, reportId);
        }

        /// <summary>
        /// Creates the default template configuration settings.
        /// </summary>
        /// <returns>A configured TemplateConfiguration object.</returns>
        private static TemplateConfiguration CreateTemplateConfiguration()
        {
            return new TemplateConfiguration
            {
                TemplateRowCount = 6, // We have 6 rows per submission
                HeaderRowCount = 0, // We have no header rows
                StartingRow = 7
            };
        }

        /// <summary>
        /// Creates an Excel report for the given submissions.
        /// </summary>
        /// <param name="workbook">The workbook to add the submissions to.</param>
        /// <param name="submissions">The submissions to add.</param>
        private void CreateSubmissionExcelReport(XLWorkbook workbook, IEnumerable<Submission> submissions)
        {
            var templateConfig = CreateTemplateConfiguration();

            // Register row populators
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_regularFeeLinePopulator);
            _excelTemplateService.RegisterRowPopulator(_lateFeeLinePopulator);

            // Apply template
            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig);
        }

        private async Task<IEnumerable<Submission>> GetSubmissions()
        {
            // Get the submissions for today an pending export
            return await _submissionReportsDataManager.GetSubmissionsForNevisFinancialReportExport();
        }
    }
}