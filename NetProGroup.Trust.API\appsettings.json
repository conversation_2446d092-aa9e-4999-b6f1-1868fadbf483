{"ApplicationInsights": {"EnableAdaptiveSampling": false}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "System": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks": "Debug", "Hangfire": "Warning", "NetProGroup.Trust.DataMigration": "Verbose"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} [{SourceContext}] {NewLine}{Exception}", "restrictedToMinimumLevel": "Warning"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "DataMigration": {"ProgressUpdateInterval": 100, "UseNewBrandingLimitDate": "2023-11-08T00:00:00Z", "UseDummyInitialSync": false, "Enabled": false, "StoreUnprocessedRecords": true, "JobLockRefreshMarginSeconds": 120, "IgnorePaymentWhenNoInvoiceNumber": true, "ActiveJurisdiction": "<PERSON><PERSON><PERSON>", "Jurisdictions": {"Nevis": {"MongoConnectionString": "tbd", "MongoDatabaseName": "tbd"}, "Bahamas": {"MongoConnectionString": "tbd", "MongoDatabaseName": "tbd", "StorageAccounts": [{"Key": "SourceFileStorageBahamas", "AccountName": "devstoreaccount1", "SharedKey": "", "DefaultContainer": "tbvi-substance-uploads"}]}}}, "ShowPII": false}