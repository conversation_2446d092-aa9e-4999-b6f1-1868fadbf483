using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a late payment fee configuration in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class LatePaymentFeeConfiguration
    {
        /// <summary>
        /// Gets or sets the description of the late payment fee.
        /// </summary>
        [BsonElement("description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether there is a late payment charge applicable.
        /// </summary>
        [BsonElement("latePaymentCharge")]
        public bool? LatePaymentCharge { get; set; }

        /// <summary>
        /// Gets or sets the start date of the late payment period.
        /// </summary>
        [BsonElement("latePeriodStart")]
        public DateTime LatePeriodStart { get; set; }

        /// <summary>
        /// Gets or sets the end date of the late payment period.
        /// </summary>
        [BsonElement("latePeriodEnd")]
        public DateTime LatePeriodEnd { get; set; }

        /// <summary>
        /// Gets or sets the financial year to which the late payment fee applies.
        /// </summary>
        [BsonElement("year")]
        public int Year { get; set; }

        /// <summary>
        /// Gets or sets the late payment fee amount.
        /// </summary>
        [BsonElement("fee")]
        public decimal Fee { get; set; }
    }
}
