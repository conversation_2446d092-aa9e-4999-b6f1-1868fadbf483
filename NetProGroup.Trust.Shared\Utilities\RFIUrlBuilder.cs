﻿// <copyright file="RFIUrlBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Tools;

namespace NetProGroup.Trust.Domain.Shared.Utilities
{
    /// <summary>
    /// Helper to build RFI URLs.
    /// </summary>
    public static class RFIUrlBuilder
    {
        /// <summary>
        /// Builds the RFI URL for a given legal entity and master client.
        /// </summary>
        /// <param name="portalUrl">The portal URL.</param>
        /// <param name="legalEntityId">The unique identifier of the legal entity related to an RFI.</param>
        /// <param name="masterClientId">The unique identifier of the master client related to an RFI.</param>
        /// <returns>The RFI url.</returns>
        public static Uri BuildRFIUrl(Uri portalUrl, Guid legalEntityId, Guid masterClientId)
        {
            Check.NotNull(portalUrl, nameof(portalUrl));
            var baseUrl = portalUrl.ToString().TrimEnd('/');
            var finalUrl = $"{baseUrl}/economic-substance/submissions?setCompanyId={legalEntityId}&setMasterClientId={masterClientId}";
            return new Uri(finalUrl);
        }
    }
}
