// <copyright file="SubmissionRFIDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represents a request for information entity.
    /// </summary>
    public class SubmissionRFIDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the id of the submission.
        /// </summary>
        public Guid SubmissionId { get; set; }

        /// <summary>
        /// Gets or sets the deadline date for the request for information.
        /// </summary>
        public DateTime DeadLine { get; set; }

        /// <summary>
        /// Gets or sets the comments for the request for information.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the request for information.
        /// </summary>
        public RequestForInformationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the reason for cancellation.
        /// </summary>
        public string CancellationReason { get; set; }

        /// <summary>
        /// Gets the collection with RequestForInformationDocument.
        /// </summary>
        /// <value>A collection of <see cref="SubmissionRFIDocumentDTO"/>.</value>
        public virtual ICollection<SubmissionRFIDocumentDTO> Documents { get; } = new List<SubmissionRFIDocumentDTO>();
    }
}