﻿// <copyright file="ISubmissionsIncludingDeletedRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Submissions
{
    /// <summary>
    /// Interface for the Submission repository that includes deleted submissions.
    /// </summary>
    public interface ISubmissionsIncludingDeletedRepository : IRepository<Submission, Guid>, IRepositoryService
    {
        /// <summary>
        /// Gets the DbContext of the repository.
        /// </summary>
        DbContext DbContext { get; }
    }
}
