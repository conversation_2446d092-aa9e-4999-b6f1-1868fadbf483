﻿// <copyright file="ViewPointSyncJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Exceptions;
using NetProGroup.Trust.DataManager.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Sync
{
    /// <summary>
    /// Scheduled job for syncing ViewPoint data.
    /// </summary>
    public class ViewPointSyncJob : JobBase<ViewPointSyncJobData>, IViewPointSyncJob
    {
        private static bool _busy;
        private readonly DataSyncOptions _dataSyncOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="ViewPointSyncJob"/> class.
        /// </summary>
        /// <param name="logger">The Logger.</param>
        /// <param name="serviceProvider">The Service Provider.</param>
        /// <param name="dataSyncOptions">The Data Sync Options.</param>
        /// <param name="options">The scheduling job settings options.</param>
        public ViewPointSyncJob(ILogger<ViewPointSyncJob> logger,
                                IServiceProvider serviceProvider,
                                IOptions<DataSyncOptions> dataSyncOptions,
                                IOptions<ScheduledJobSettings> options)
        : base(logger, serviceProvider, options)
        {
            ArgumentNullException.ThrowIfNull(dataSyncOptions, nameof(dataSyncOptions));
            _dataSyncOptions = dataSyncOptions.Value;
        }

        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid(ScheduledJobConsts.ViewPointSyncJobId);

        /// <inheritdoc/>
        public string ScheduledJobKey => "sync.viewpoint";

        /// <inheritdoc/>
        public string ScheduledJobName => "ViewPoint Sync";

        /// <inheritdoc />
        protected override async Task DoWorkAsync(ViewPointSyncJobData data, CancellationToken token = default)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));
            if (!_dataSyncOptions.Enabled)
            {
                Logger.LogWarning("Not running ViewPoint Sync because it is disabled by dataSyncOptions.");
                return;
            }

            if (_busy)
            {
                Logger.LogDebug("Not running ViewPoint Sync because it is in progress already.");
                return;
            }

            var jobLock = await AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                _busy = true;

                Logger.LogInformation("Starting VP Sync...");

                await SyncHelper.LockAsync();
                SyncHelper.JurisdictionCodes = _dataSyncOptions.JurisdictionCodes;

                if (SyncHelper.JurisdictionCodes.Count == 0)
                {
                    Logger.LogWarning("No JurisdictionCodes configured for DataSync");
                    return;
                }
                else
                {
                    Logger.LogInformation("DataSync is using JurisdictionCodes '{JurisdictionCodes}'", string.Join(',', SyncHelper.JurisdictionCodes));
                }

                var masterClientImport = ServiceProvider.GetRequiredService<Import.Interfaces.IMasterClientImport>();
                var companyImport = ServiceProvider.GetRequiredService<Import.Interfaces.ICompanyImport>();
                var beneficialOwnerImport = ServiceProvider.GetRequiredService<Import.Interfaces.IBeneficialOwnerImport>();
                var directorImport = ServiceProvider.GetRequiredService<Import.Interfaces.IDirectorImport>();

                // Execute sync in this order...
                Logger.LogInformation("Sync companies");
                var companyResult = await companyImport.SyncViewPointAsync(jobLock);
                Logger.LogInformation("Sync companies done. Changed: {ChangedCount}, Deleted: {DeletedCount}",
                    companyResult.UpdatedCount, companyResult.DeletedCount);
                SaveSyncProgress(data, "companies", SyncHelper.JurisdictionCodes, companyResult);

                Logger.LogInformation("Sync masterclients");
                var masterClientResult = await masterClientImport.SyncViewPointAsync(jobLock);
                Logger.LogInformation("Sync masterclients done. {ChangedCount}, Deleted: {DeletedCount}",
                    masterClientResult.UpdatedCount, masterClientResult.DeletedCount);
                SaveSyncProgress(data, "masterclients", SyncHelper.JurisdictionCodes, masterClientResult);

                Logger.LogInformation("Sync directors");
                var directorResult = await directorImport.SyncViewPointAsync(jobLock);
                Logger.LogInformation("Sync directors done. {ChangedCount}, Deleted: {DeletedCount}",
                    directorResult.UpdatedCount, directorResult.DeletedCount);
                SaveSyncProgress(data, "directors", SyncHelper.JurisdictionCodes, directorResult);

                Logger.LogInformation("Sync beneficialowners");
                var beneficialOwnerResult = await beneficialOwnerImport.SyncViewPointAsync(jobLock);
                Logger.LogInformation("Sync beneficialowners done. {ChangedCount}, Deleted: {DeletedCount}",
                    beneficialOwnerResult.UpdatedCount, beneficialOwnerResult.DeletedCount);
                SaveSyncProgress(data, "beneficialowners", SyncHelper.JurisdictionCodes, beneficialOwnerResult);

                await UpdateJurisdictions();

                Logger.LogInformation("VP Sync completed");
            }
            catch (LockNotFoundException)
            {
                Logger.LogError("The lock for ViewPoint Sync could no longer be found");
                jobLock = null;
                throw;
            }
            finally
            {
                _busy = false;

                if (jobLock != null)
                {
                    await ReleaseLockAsync(jobLock);
                }

                SyncHelper.Unlock();
            }
        }

        private static void SaveSyncProgress(ViewPointSyncJobData data, string name, List<string> jurisdictionCodes, SyncResult result)
        {
            if (data.SyncDetails == null)
            {
                data.SyncDetails = new Dictionary<string, SyncDetails>();
            }

            SyncDetails syncDetails;
            if (!data.SyncDetails.TryGetValue(name, out syncDetails))
            {
                syncDetails = new SyncDetails();
                data.SyncDetails.Add(name, syncDetails);
            }

            syncDetails.LastSuccessfulSync = DateTime.UtcNow;
            syncDetails.JurisdictionsUsed = jurisdictionCodes;
            syncDetails.UpdatedCount = result.UpdatedCount;
            syncDetails.DeletedCount = result.DeletedCount;
        }

        private async Task UpdateJurisdictions()
        {
            var jurisdictionsDataManager = ServiceProvider.GetRequiredService<IJurisdictionsDataManager>();
            var legalEntitiesRepository = ServiceProvider.GetRequiredService<ILegalEntitiesRepository>();
            foreach (var jurisdictionCode in SyncHelper.JurisdictionCodes.ToJurisdictionCodes())
            {
                var jurisdiction = await jurisdictionsDataManager.GetByCodeAsync(jurisdictionCode);

                var legalEntitiesSynced = await legalEntitiesRepository.AnyByConditionAsync(le => le.JurisdictionId == jurisdiction.Id);

                // Only set initial sync completed if there are actually legal entities in the synced set, just to be safe
                if (legalEntitiesSynced)
                {
                    await jurisdictionsDataManager.SetInitialSyncCompletedAsync(jurisdiction.Id);
                    Logger.LogInformation("Set initial sync completed for jurisdiction '{JurisdictionCode}'", jurisdictionCode);
                }
                else
                {
                    Logger.LogWarning("No legal entities to sync found for jurisdiction '{JurisdictionCode}'", jurisdictionCode);
                }
            }
        }
    }
}
