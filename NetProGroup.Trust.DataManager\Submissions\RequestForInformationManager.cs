// <copyright file="RequestForInformationManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Messaging.Tokens;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Documents.EFRepository;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Utilities;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.FormDocuments;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Shared.Permissions;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Manager for request for information events.
    /// </summary>
    public class RequestForInformationManager : IRequestForInformationManager
    {
        private readonly ILogger _logger;
        private readonly IWorkContext _workContext;
        private readonly IMapper _mapper;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;
        private readonly IAnnouncementDataManager _announcementDataManager;
        private readonly IDocumentManager _documentManager;
        private readonly IDocumentRepository _documentRepository;
        private readonly IRequestForInformationRepository _requestForInformationRepository;
        private readonly IRequestForInformationDocumentsRepository _requestForInformationDocumentsRepository;
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly ISubmissionsIncludingDeletedRepository _submissionsIncludingDeletedRepository;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IUserRepository _userRepository;
        private readonly TrustOfficeOptions _trustOfficeOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestForInformationManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="mapper">The mapper instance.</param>
        /// <param name="authorizationFilterExpressionFactory">Factory for authorization filter expressions.</param>
        /// <param name="announcementDataManager">An instance of IAnnouncementDataManager.</param>
        /// <param name="documentManager">An instance of IDocumentManager.</param>
        /// <param name="documentRepository">An instance of IDocumentRepository.</param>
        /// <param name="requestForInformationRepository">An instance of IRequestForInformationRepository.</param>
        /// <param name="requestForInformationDocumentsRepository">An instance of IRequestForInformationDocuentsRepository.</param>
        /// <param name="submissionsRepository">Repository for submissions.</param>
        /// <param name="submissionsIncludingDeletedRepository">Repository for submissions including deleted ones.</param>
        /// <param name="systemAuditManager">Manager for system audits.</param>
        /// <param name="userRepository">An instance of IUserRepository.</param>
        /// <param name="trustOfficeOptions">Configuration for TrustOffice.</param>
        public RequestForInformationManager(ILogger<RequestForInformationManager> logger,
                                  IWorkContext workContext,
                                  IMapper mapper,
                                  IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory,
                                  IAnnouncementDataManager announcementDataManager,
                                  IDocumentManager documentManager,
                                  IDocumentRepository documentRepository,
                                  IRequestForInformationRepository requestForInformationRepository,
                                  IRequestForInformationDocumentsRepository requestForInformationDocumentsRepository,
                                  ISubmissionsRepository submissionsRepository,
                                  ISubmissionsIncludingDeletedRepository submissionsIncludingDeletedRepository,
                                  ISystemAuditManager systemAuditManager,
                                  IUserRepository userRepository,
                                  IOptions<TrustOfficeOptions> trustOfficeOptions)
        {
            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));

            _logger = logger;
            _workContext = workContext;
            _mapper = mapper;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
            _announcementDataManager = announcementDataManager;
            _documentManager = documentManager;
            _documentRepository = documentRepository;
            _submissionsRepository = submissionsRepository;
            _submissionsIncludingDeletedRepository = submissionsIncludingDeletedRepository;
            _requestForInformationRepository = requestForInformationRepository;
            _requestForInformationDocumentsRepository = requestForInformationDocumentsRepository;
            _systemAuditManager = systemAuditManager;
            _userRepository = userRepository;
            _trustOfficeOptions = trustOfficeOptions.Value;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionRFIDTO>> ListRequestForInformationAsync(ListRequestsForInformationRequest request)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // Select the submissions
            var requestForInformationPredicate = GetRequestForInformationPredicate(request);

            var requestForInformations = await _requestForInformationRepository.FindByConditionAsPagedListMappedAsync<RequestForInformation, ListSubmissionRFIDTO>(
                requestForInformationPredicate,
                _mapper.ConfigurationProvider,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                options: o => o.Include(rfi => rfi.Submission),
                optionsMapped: q => ApplySorting(q, new SortingInfo() { SortBy = request.SortBy, SortOrder = request.SortOrder }));

            return requestForInformations;
        }

        /// <inheritdoc/>
        public async Task<RequestForInformationDTO> GetRequestForInformationByIdAsync(Guid requestForInformationId)
        {
            Check.NotDefaultOrNull<Guid>(requestForInformationId, nameof(requestForInformationId));

            var requestForInformation = await _requestForInformationRepository.GetByIdAsync(requestForInformationId, (q) =>
                   q.Include(s => s.Submission)
                    .ThenInclude(le => le.LegalEntity)
                    .ThenInclude(ju => ju.Jurisdiction)
                    .Include(s => s.Submission)
                    .ThenInclude(le => le.LegalEntity)
                    .ThenInclude(mc => mc.MasterClient)
                    .Include(s => s.CreatedByUser)
                    .Include(s => s.CompletedByUser)
                    .Include(s => s.RepliedByUser));
            var result = _mapper.Map<RequestForInformationDTO>(requestForInformation);

            return result;
        }

        /// <inheritdoc/>
        public async Task<Guid> CreateRequestForInformationAsync(Guid submissionId, CreateRFIDTO data)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Check the submission entity
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                submissionId,
                options: o => o.Include(s => s.RequestsForInformation));

            // Check if there is an active or draft request for information
            if (submission.RequestsForInformation.Any(
                rfi => rfi.Status == RequestForInformationStatus.Draft || rfi.Status == RequestForInformationStatus.Active))
            {
                throw new ConstraintException(
                    ApplicationErrors.REQUEST_FOR_INFORMATION_ALREADY_EXIST.ToErrorCode(),
                    $"A request for information already exists for the submission with id '{submissionId}'.");
            }

            // Create a new request for information
            var requestForInformation = new RequestForInformation()
            {
                SubmissionId = submissionId,
                DeadLine = data.DeadLine,
                Comments = data.Comments,
                Status = data.IncludeAttachments ? RequestForInformationStatus.Draft : RequestForInformationStatus.Active,
                CreatedBy = _workContext.IdentityUserId.Value
            };

            await _requestForInformationRepository.InsertAsync(requestForInformation, false);

            if (requestForInformation.Status == RequestForInformationStatus.Active)
            {
                // Set the submission status
                submission.Status = SubmissionStatus.InformationRequested;
                await _submissionsRepository.UpdateAsync(submission);

                // Notify the creation for the RFI
                await NotifyCreatedRequestForInformationAsync(requestForInformation);

                // Log the event
                await _systemAuditManager.AddSubmissionInformationRequestedActivityLogAsync(submission, requestForInformation, false);
            }

            await _requestForInformationRepository.SaveChangesAsync();

            return requestForInformation.Id;
        }

        /// <inheritdoc/>
        public async Task<SubmissionRFIDetailsDTO> GetRFISubmissionDetailsAsync(Guid submissionId)
        {
            // Check the submission entity
            var submission = await _submissionsIncludingDeletedRepository.CheckSubmissionByIdAsync(
                submissionId,
                options: o => o
                    .Include(s => s.RequestsForInformation)
                    .ThenInclude(rfi => rfi.Documents)
                    .ThenInclude(rfid => rfid.Document));

            var activityLogRequest = new ListActivityLogRequest()
            {
                Period = new Period { StartDate = DateTime.UtcNow.AddYears(-7), EndDate = DateTime.UtcNow },
                EntityId = submissionId,
                PageSize = int.MaxValue,
                ActivityType = ActivityLogActivityTypes.SubmissionFinancialPeriodChanged
            };

            var financialPeriodActivityLogs = await _systemAuditManager.ListActivityLogsAsync(activityLogRequest);

            var response = new SubmissionRFIDetailsDTO()
            {
                Id = submissionId,
                Status = submission.Status,
                RequestsForInformation = _mapper.Map<List<SubmissionRFIDTO>>(submission.RequestsForInformation.OrderByDescending(s => s.CreatedAt)),
                ActivityLogItems = financialPeriodActivityLogs.ActivityLogItems.ToList()
            };

            return response;
        }

        /// <inheritdoc/>
        public async Task CreateRFIDocumentAsync(Guid requestForInformationId, CreateRFIDocumentDTO data, bool createdByManagementUser)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _requestForInformationRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _requestForInformationRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                // Check the announcement entity
                var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

                var documentId = await _documentManager.CreateDocumentAsync(
                    (int)data.Type,
                    string.IsNullOrEmpty(data.Description) ? data.File.Name : data.Description,
                    data.File,
                    "default",
                    true,
                    DateTime.UtcNow.AddHours(24));

                // Creates the request for information document entity
                var requestForInformationDocument = new RequestForInformationDocument()
                {
                    RequestForInformationId = requestForInformationId,
                    DocumentId = documentId,
                    CreatedByManagement = createdByManagementUser
                };

                await _requestForInformationDocumentsRepository.InsertAsync(requestForInformationDocument, true);

                if (data.UploadComplete)
                {
                    // Check the submission entity
                    var submission = await _submissionsRepository.CheckSubmissionByIdAsync(requestForInformation.SubmissionId);

                    if (createdByManagementUser)
                    {
                        // Set the request for information status to Active
                        requestForInformation.Status = RequestForInformationStatus.Active;
                        await _requestForInformationRepository.UpdateAsync(requestForInformation, true);

                        // Set the submission status
                        submission.Status = SubmissionStatus.InformationRequested;
                        await _submissionsRepository.UpdateAsync(submission, true);

                        // Notify the creation for the RFI
                        await NotifyCreatedRequestForInformationAsync(requestForInformation);

                        // Log the event
                        await _systemAuditManager.AddSubmissionInformationRequestedActivityLogAsync(submission, requestForInformation, true);
                    }
                    else
                    {
                        requestForInformation.Status = RequestForInformationStatus.Completed;
                        await _requestForInformationRepository.UpdateAsync(requestForInformation, true);

                        // Restore the submission status
                        submission.Status = SubmissionStatus.Submitted;
                        await _submissionsRepository.UpdateAsync(submission, true);

                        // Notify the management user
                        await NotifyRFICompletedByClientAsync(requestForInformationId);

                        // Log the event and save changes
                        await _systemAuditManager.AddRequestForInformationCompletedActivityLogAsync(requestForInformation, submission, true);
                    }
                }

                if (handleTransaction)
                {
                    await dbTransaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <inheritdoc/>
        public async Task CancelRequestForInformationAsync(Guid requestForInformationId, string reason)
        {
            // Checks and validations
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            // Check the request for information completed status
            if (requestForInformation.Status == RequestForInformationStatus.Completed)
            {
                throw new ConstraintException(
                    ApplicationErrors.INVALID_REQUEST_FOR_INFORMATION_STATUS.ToErrorCode(),
                    $"The request for information with id '{requestForInformationId}' is already completed so it cannot be cancelled.");
            }

            // Check the request for information cancelled status
            if (requestForInformation.Status == RequestForInformationStatus.Cancelled)
            {
                throw new ConstraintException(
                    ApplicationErrors.INVALID_REQUEST_FOR_INFORMATION_STATUS.ToErrorCode(),
                    $"The request for information with id '{requestForInformationId}' is already cancelled.");
            }

            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(requestForInformation.SubmissionId);

            // Cancel the request for information
            requestForInformation.Status = RequestForInformationStatus.Cancelled;
            requestForInformation.CancellationReason = reason;
            await _requestForInformationRepository.UpdateAsync(requestForInformation);

            // Restore the submission status
            submission.Status = SubmissionStatus.Submitted;
            await _submissionsRepository.UpdateAsync(submission);

            // Log the event and save changes
            await _systemAuditManager.AddRequestForInformationCancelledActivityLogAsync(requestForInformation, submission, true);
        }

        /// <inheritdoc/>
        public async Task<SubmissionRFIDTO> GetRFIById(Guid requestForInformationId)
        {
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            return _mapper.Map<SubmissionRFIDTO>(requestForInformation);
        }

        /// <inheritdoc/>
        public async Task CompleteRequestForInformationAsync(Guid requestForInformationId, CompleteRequestForInformationDTO data)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var (requestForInformation, submission) = await ValidateRfiAndSubmissionAsync(requestForInformationId);

            if (!data.IncludeAttachments)
            {
                MarkRfiAsCompleted(requestForInformation, data.Response);
                await _requestForInformationRepository.UpdateAsync(requestForInformation);

                RestoreSubmission(submission);
                await _submissionsRepository.UpdateAsync(submission);

                await NotifyRFICompletedByClientAsync(requestForInformationId);
                await _systemAuditManager.AddRequestForInformationCompletedActivityLogAsync(requestForInformation, submission, true);
            }
            else
            {
                requestForInformation.Response = data.Response;
                requestForInformation.RepliedAt = DateTime.UtcNow;

                if (_workContext.IdentityUserId.HasValue)
                {
                    requestForInformation.RepliedBy = _workContext.IdentityUserId.Value;
                }

                await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
            }
        }

        /// <inheritdoc/>
        public async Task CompleteRequestForInformationFromManagementAsync(Guid requestForInformationId, CompleteRequestForInformationManagementDTO data)
        {
            Check.NotNull(data, nameof(data));

            var (requestForInformation, submission) = await ValidateRfiAndSubmissionAsync(requestForInformationId);

            requestForInformation.Remark = data.Remark;
            requestForInformation.SubmittedToRegulator = data.SubmittedToRegulator;
            requestForInformation.CompletedAt = DateTime.Now;

            MarkRfiAsCompleted(requestForInformation);

            RestoreSubmission(submission);

            await _submissionsRepository.UpdateAsync(submission, true);
            await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
        }

        /// <inheritdoc/>
        public async Task ProcessRFIsDueInOneWeekAsync()
        {
            // Retrieve the active RFI created that are due in one week.
            var requestsForInformationOneWeekDue = (await _requestForInformationRepository.FindByConditionAsync(
                rfi => rfi.Status == RequestForInformationStatus.Active &&
                rfi.ReminderType == RequestForInformationReminderType.Created &&
                rfi.DeadLine <= DateTime.UtcNow.AddDays(7))).ToList();

            foreach (var requestForInformation in requestsForInformationOneWeekDue)
            {
                try
                {
                    // Send the announcement
                    await NotifyRequestForInformationAsync(requestForInformation.Id, RequestForInformationReminderType.DueInOneWeek);
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    _logger.LogError(ex, "Failed to notify the request for information. RequestId: {RequestId}", requestForInformation.Id);
                }
            }
        }

        /// <inheritdoc/>
        public async Task ProcessRFIsDueInOneDayAsync()
        {
            // Retrieve the active RFI created that are due in one day
            var requestsForInformationOneDayDue = (await _requestForInformationRepository.FindByConditionAsync(
                rfi => rfi.Status == RequestForInformationStatus.Active &&
                rfi.ReminderType == RequestForInformationReminderType.DueInOneWeek &&
                rfi.DeadLine <= DateTime.UtcNow.AddDays(1))).ToList();

            foreach (var requestForInformation in requestsForInformationOneDayDue)
            {
                try
                {
                    // Send the announcement
                    await NotifyRequestForInformationAsync(requestForInformation.Id, RequestForInformationReminderType.DueInOneDay);
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    _logger.LogError(ex, "Failed to notify the request for information. RequestId: {RequestId}", requestForInformation.Id);
                }
            }
        }

        /// <inheritdoc/>
        public async Task ProcessRFIsThreeDaysOverdueAsync()
        {
            // Retrieve the active RFI created that are three days over due
            var requestsForInformationThreeDaysOverDue = (await _requestForInformationRepository.FindByConditionAsync(
                rfi => rfi.Status == RequestForInformationStatus.Active &&
                rfi.ReminderType == RequestForInformationReminderType.DueInOneDay &&
                rfi.DeadLine.AddDays(3) <= DateTime.UtcNow)).ToList();

            foreach (var requestForInformation in requestsForInformationThreeDaysOverDue)
            {
                try
                {
                    // Send the announcement
                    await NotifyRequestForInformationAsync(requestForInformation.Id, RequestForInformationReminderType.ThreeDaysOverDue);
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    _logger.LogError(ex, "Failed to notify the request for information. RequestId: {RequestId}", requestForInformation.Id);
                }
            }
        }

        /// <summary>
        /// Notifies the management user about the completion of the request for information by the client.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="reminderType">The type of reminder notification to send (Created, DueInOneWeek, DueInOneDay, ThreeDaysOverDue, or Completed).</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="BadRequestException">Thrown when the notification is already sent.</exception>
        /// <exception cref="NotFoundException">Thrown when the request for information is not found.</exception>
        public async Task NotifyRequestForInformationAsync(Guid requestForInformationId, RequestForInformationReminderType reminderType)
        {
            // Checks and validations
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            // Check the current reminder type of the RFI
            if (requestForInformation.ReminderType == reminderType)
            {
                throw new BadRequestException(
                    ApplicationErrors.REQUEST_FOR_INFORMATION_NOTIFICATION_ALREADY_SENT.ToErrorCode(),
                    $"The notification is already sent.");
            }

            // Check the reminder type
            switch (reminderType)
            {
                case RequestForInformationReminderType.Created:
                    await NotifyCreatedRequestForInformationAsync(requestForInformation);
                    break;
                case RequestForInformationReminderType.DueInOneWeek:
                    await NotifyDueInOneWeekRequestForInformationAsync(requestForInformationId);
                    break;
                case RequestForInformationReminderType.DueInOneDay:
                    await NotifyDueInOneDayRequestForInformationAsync(requestForInformationId);
                    break;
                case RequestForInformationReminderType.ThreeDaysOverDue:
                    await NotifyThreeDaysOverDueRequestForInformationAsync(requestForInformationId);
                    break;
            }
        }

        #region Private methods

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<RequestForInformation, bool>> GetRequestForInformationPredicate(ListRequestsForInformationRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            Expression<Func<RequestForInformation, bool>> predicate = rfi => true;

            // Combine with the submission predicate using the authorized jurisdiction ids.
            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                // Only one of them needs to contain the search term
                var text = filter.GeneralSearchTerm;
                predicate = predicate.And(rfi => rfi.Submission.LegalEntity.Name.Contains(text) ||
                                                         rfi.Submission.LegalEntity.MasterClient.Code.Contains(text) ||
                                                         rfi.Submission.LegalEntity.Code.Contains(text) ||
                                                         rfi.Submission.LegalEntity.LegacyCode.Contains(text));
            }

            // Validate client have authorization for jurisdiction
            predicate.And(requestForInformation => filter.AuthorizedJurisdictionIDs.Contains(requestForInformation.Submission.LegalEntity.Jurisdiction.Id));

            // Retrocompatibility: If both LegalEntityId and ModuleId are provided, filter by both
            if (filter.LegalEntityId.HasValue && filter.LegalEntityId != Guid.Empty)
            {
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.LegalEntityId == filter.LegalEntityId);
            }

            if (filter.JurisdictionId.HasValue && filter.JurisdictionId != Guid.Empty)
            {
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.LegalEntity.JurisdictionId == filter.JurisdictionId);
            }

            if (filter.ModuleId.HasValue && filter.ModuleId != Guid.Empty)
            {
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.ModuleId == filter.ModuleId);
            }

            if (filter.SubmittedAfterDate.HasValue)
            {
                var date = filter.SubmittedAfterDate.Value.Date;
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.SubmittedAt > date);
            }

            if (filter.SubmittedBeforeDate.HasValue)
            {
                var date = filter.SubmittedBeforeDate.Value.Date;
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.SubmittedAt < date);
            }

            if (filter.Status.HasValue)
            {
                var status = filter.Status.Value;
                predicate = predicate.And(requestForInformation => requestForInformation.Status == status);
            }

            if (filter.IsOverdue.HasValue && filter.IsOverdue.Value)
            {
                predicate = predicate.And(requestForInformation => requestForInformation.DeadLine < DateTime.UtcNow && !requestForInformation.CompletedAt.HasValue);
            }

            if (filter.CompanyIncorporatedAfterDate.HasValue)
            {
                var date = filter.CompanyIncorporatedAfterDate.Value.Date;
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.LegalEntity.IncorporationDate > date);
            }

            if (filter.CompanyIncorporatedBeforeDate.HasValue)
            {
                var date = filter.CompanyIncorporatedBeforeDate.Value.Date;
                predicate = predicate.And(requestForInformation => requestForInformation.Submission.LegalEntity.IncorporationDate < date);
            }

            return predicate;
        }

        /// <summary>
        /// Configure the sorting to an IQueryable.
        /// </summary>
        /// <param name="query">Th IQueryable of ListSubmissionRFIDTO.</param>
        /// <param name="sortingInfo">The sorting info as SortingInfo.</param>
        /// <returns>The sorted IQueryable.</returns>
        private static IQueryable<ListSubmissionRFIDTO> ApplySorting(IQueryable<ListSubmissionRFIDTO> query, SortingInfo sortingInfo)
        {
            if (sortingInfo == null)
            {
                return query;
            }

            sortingInfo = sortingInfo.Validate();

            var sortingColumns = new Dictionary<string, Expression<Func<ListSubmissionRFIDTO, object>>>()
            {
                {
                    nameof(ListSubmissionRFIDTO.Status), s =>
                    s.Status == SubmissionStatus.Paid ? 1 :
                    s.Status == SubmissionStatus.Submitted ? 2 :
                    s.Status == SubmissionStatus.InformationRequested ? 3 :
                    4
                },
            };
            Expression<Func<ListSubmissionRFIDTO, object>> defaultSort = s => s.RFICreatedAt;
            return query.SortBySpecification<ListSubmissionRFIDTO, ListRequestsForInformationDTO>(sortingInfo, defaultSort, sortingColumns);
        }

        /// <summary>
        /// Restores the status of a <see cref="Submission"/> to <see cref="SubmissionStatus.Submitted"/>.
        /// </summary>
        /// <param name="submission">The <see cref="Submission"/> entity to update.</param>
        private static void RestoreSubmission(Submission submission)
        {
            submission.Status = SubmissionStatus.Submitted;
        }

        /// <summary>
        /// Marks a Request for Information (RFI) as completed and optionally assigns a response.
        /// </summary>
        /// <param name="request">The <see cref="RequestForInformation"/> entity to update.</param>
        /// <param name="response">
        /// Optional response text to associate with the RFI.
        /// If null or empty, only the status will be updated.
        /// </param>
        private void MarkRfiAsCompleted(RequestForInformation request, string response = null)
        {
            request.Status = RequestForInformationStatus.Completed;

            if (!string.IsNullOrWhiteSpace(response))
            {
                request.Response = response;

                if (_workContext.IdentityUserId.HasValue)
                {
                    request.RepliedBy = _workContext.IdentityUserId.Value;
                }

                request.RepliedAt = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Creates an announcement for the created request for information.
        /// </summary>
        /// <param name="requestForInformation">The request for information entity.</param>
        private async Task NotifyCreatedRequestForInformationAsync(RequestForInformation requestForInformation)
        {
            // Retrieve the submission entity
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                requestForInformation.SubmissionId,
                options: o => o.Include(s => s.LegalEntity.Jurisdiction));

            var tokens = new TokenList();
            var tokenizer = new Tokenizer();

            // Check for the jurisdiction to perform jurisdiction specific validations
            switch (submission.LegalEntity.Jurisdiction.Code)
            {
                case JurisdictionCodes.Bahamas:

                    tokens = new TokenList();
                    tokenizer = new Tokenizer();

                    var rFIUrl = RFIUrlBuilder.BuildRFIUrl(new Uri(_trustOfficeOptions.ClientPortalUrl), submission.LegalEntityId, submission.LegalEntity.MasterClientId);
                    tokens.Add("companyName", submission.LegalEntity.Name);
                    tokens.Add("financialPeriodEndDate", submission.EndsAt.Value.ToString(WellKnownDataConstants.DateFormat));
                    tokens.Add("irdUrl", rFIUrl.ToString());
                    tokens.Add("rfiDeadLine", requestForInformation.DeadLine.ToString(WellKnownDataConstants.DateFormat));

                    // Setup the announcement data
                    var createBahamasRfiData = new CreateUpdateAnnouncementDTO()
                    {
                        Subject = WellKnownAnnouncementSubjects.BAHCreatedRFI,
                        EmailSubject = WellKnownAnnouncementSubjects.BAHCreatedRFI,
                        Body = tokenizer.Replace(WellKnownRFIMessages.BAHCreatedRFI, tokens, true),
                        IncludeAttachments = false,
                        SendNow = true,
                        LegalEntityIds = new List<Guid>()
                        {
                            submission.LegalEntityId
                        }
                    };

                    await _announcementDataManager.CreateUpdateAnnouncementAsync(createBahamasRfiData);

                    break;
                case JurisdictionCodes.Nevis:
                    tokens = new TokenList();
                    tokenizer = new Tokenizer();

                    tokens.Add("companyName", submission.LegalEntity.Name);
                    tokens.Add("financialYear", submission.FinancialYear.ToString());
                    tokens.Add("irdUrl", _trustOfficeOptions.ClientPortalUrl);
                    tokens.Add("rfiDeadLine", requestForInformation.DeadLine.ToString(WellKnownDataConstants.DateFormat));

                    // Setup the announcement data
                    var createSTRRfiData = new CreateUpdateAnnouncementDTO()
                    {
                        Subject = WellKnownAnnouncementSubjects.STRCreatedRFI,
                        EmailSubject = WellKnownAnnouncementSubjects.STRCreatedRFI,
                        Body = tokenizer.Replace(WellKnownRFIMessages.STRCreatedRFI, tokens, true),
                        IncludeAttachments = false,
                        SendNow = true,
                        LegalEntityIds = new List<Guid>()
                        {
                            submission.LegalEntityId
                        }
                    };

                    await _announcementDataManager.CreateUpdateAnnouncementAsync(createSTRRfiData);

                    break;
                default:
                    break;
                    // TODO
                    //throw new ConstraintException(
                    //    ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                    //    $"The jurisdiction '{submission.LegalEntity.Jurisdiction.Code}' is not configured.");
            }

            // Update the request for information entity
            requestForInformation.LastRemindedAt = DateTime.UtcNow;
            requestForInformation.ReminderType = RequestForInformationReminderType.Created;

            // Log the notification event
            await _systemAuditManager.AddRfiCreatedNotificationActivityLogAsync(requestForInformation, submission, false);

            await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
        }

        /// <summary>
        /// Creates an announcement for a RFI that is due in one week.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        private async Task NotifyDueInOneWeekRequestForInformationAsync(Guid requestForInformationId)
        {
            // Checks and validations
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            // Check the RFI reminder type
            if (requestForInformation.ReminderType != RequestForInformationReminderType.Created)
            {
                throw new BadRequestException(
                    ApplicationErrors.REQUEST_FOR_INFORMATION_INVALID_REMINDER_TYPE.ToErrorCode(),
                    $"The Request for information with id '{requestForInformationId}' has an invalid reminder type.");
            }

            // Retrieve the submission entity
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                requestForInformation.SubmissionId,
                options: o => o.Include(s => s.LegalEntity.Jurisdiction));

            // Check for the jurisdiction to perform jurisdiction specific validations
            switch (submission.LegalEntity.Jurisdiction.Code)
            {
                case JurisdictionCodes.Bahamas:
                    var tokens = new TokenList();
                    var tokenizer = new Tokenizer();

                    tokens.Add("companyName", submission.LegalEntity.Name);
                    tokens.Add("financialPeriodEndDate", submission.EndsAt.Value.ToString(WellKnownDataConstants.DateFormat));
                    tokens.Add("irdUrl", _trustOfficeOptions.ClientPortalUrl);
                    tokens.Add("rfiDeadLine", requestForInformation.DeadLine.ToString(WellKnownDataConstants.DateFormat));

                    // Setup the announcement data
                    var createBahamasRfiData = new CreateUpdateAnnouncementDTO()
                    {
                        Subject = WellKnownAnnouncementSubjects.BAHRFIDueOneWeek,
                        EmailSubject = WellKnownAnnouncementSubjects.BAHRFIDueOneWeek,
                        Body = tokenizer.Replace(WellKnownRFIMessages.BAHRFIDueOneWeek, tokens, true),
                        IncludeAttachments = false,
                        SendNow = true,
                        LegalEntityIds = new List<Guid>()
                        {
                            submission.LegalEntityId
                        }
                    };

                    await _announcementDataManager.CreateUpdateAnnouncementAsync(createBahamasRfiData);

                    break;
                default:
                    break;
                // TODO
                    //throw new ConstraintException(
                    //    ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                    //    $"The jurisdiction '{submission.LegalEntity.Jurisdiction.Code}' is not configured.");
            }

            // Update the request for information entity
            requestForInformation.LastRemindedAt = DateTime.UtcNow;
            requestForInformation.ReminderType = RequestForInformationReminderType.DueInOneWeek;

            // Log the notification event
            await _systemAuditManager.AddRfiDueInOneWeekNotificationActivityLogAsync(requestForInformation, submission, false);

            await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
        }

        /// <summary>
        /// Creates an announcement for a RFI that is due in one day.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        private async Task NotifyDueInOneDayRequestForInformationAsync(Guid requestForInformationId)
        {
            // Checks and validations
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            // Check the RFI reminder type
            if (requestForInformation.ReminderType != RequestForInformationReminderType.DueInOneWeek)
            {
                throw new BadRequestException(
                    ApplicationErrors.REQUEST_FOR_INFORMATION_INVALID_REMINDER_TYPE.ToErrorCode(),
                    $"The Request for information with id '{requestForInformationId}' has an invalid reminder type.");
            }

            // Retrieve the submission entity
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                requestForInformation.SubmissionId,
                options: o => o.Include(s => s.LegalEntity.Jurisdiction));

            // Check for the jurisdiction to perform jurisdiction specific validations
            switch (submission.LegalEntity.Jurisdiction.Code)
            {
                case JurisdictionCodes.Bahamas:

                    var tokens = new TokenList();
                    var tokenizer = new Tokenizer();

                    tokens.Add("companyName", submission.LegalEntity.Name);
                    tokens.Add("financialPeriodEndDate", submission.EndsAt.Value.ToString(WellKnownDataConstants.DateFormat));
                    tokens.Add("irdUrl", _trustOfficeOptions.ClientPortalUrl);
                    tokens.Add("rfiDeadLine", requestForInformation.DeadLine.ToString(WellKnownDataConstants.DateFormat));

                    // Setup the announcement data
                    var createBahamasRfiData = new CreateUpdateAnnouncementDTO()
                    {
                        Subject = WellKnownAnnouncementSubjects.BAHRFIDueOneDay,
                        EmailSubject = WellKnownAnnouncementSubjects.BAHRFIDueOneDay,
                        Body = tokenizer.Replace(WellKnownRFIMessages.BAHRFIDueOneDay, tokens, true),
                        IncludeAttachments = false,
                        SendNow = true,
                        LegalEntityIds = new List<Guid>()
                        {
                            submission.LegalEntityId
                        }
                    };

                    await _announcementDataManager.CreateUpdateAnnouncementAsync(createBahamasRfiData);

                    break;
                default:
                    break;
                // TODO
                    //throw new ConstraintException(
                    //    ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                    //    $"The jurisdiction '{submission.LegalEntity.Jurisdiction.Code}' is not configured.");
            }

            // Update the request for information entity
            requestForInformation.LastRemindedAt = DateTime.UtcNow;
            requestForInformation.ReminderType = RequestForInformationReminderType.DueInOneDay;

            // Log the notification event
            await _systemAuditManager.AddRfiDueInOneDayNotificationActivityLogAsync(requestForInformation, submission, false);

            await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
        }

        /// <summary>
        /// Creates an announcement for a RFI that is three days over due.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        private async Task NotifyThreeDaysOverDueRequestForInformationAsync(Guid requestForInformationId)
        {
            // Checks and validations
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            // Check the RFI reminder type
            if (requestForInformation.ReminderType != RequestForInformationReminderType.DueInOneDay)
            {
                throw new BadRequestException(
                    ApplicationErrors.REQUEST_FOR_INFORMATION_INVALID_REMINDER_TYPE.ToErrorCode(),
                    $"The Request for information with id '{requestForInformationId}' has an invalid reminder type.");
            }

            // Retrieve the submission entity
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                requestForInformation.SubmissionId,
                options: o => o.Include(s => s.LegalEntity.Jurisdiction));

            // Check for the jurisdiction to perform jurisdiction specific validations
            switch (submission.LegalEntity.Jurisdiction.Code)
            {
                case JurisdictionCodes.Bahamas:

                    var tokens = new TokenList();
                    var tokenizer = new Tokenizer();

                    tokens.Add("companyName", submission.LegalEntity.Name);
                    tokens.Add("financialPeriodEndDate", submission.EndsAt.Value.ToString(WellKnownDataConstants.DateFormat));
                    tokens.Add("irdUrl", _trustOfficeOptions.ClientPortalUrl);
                    tokens.Add("rfiDeadLine", requestForInformation.DeadLine.ToString(WellKnownDataConstants.DateFormat));

                    // Setup the announcement data
                    var createBahamasRfiData = new CreateUpdateAnnouncementDTO()
                    {
                        Subject = WellKnownAnnouncementSubjects.BAHRFIThreeDaysOverDue,
                        EmailSubject = WellKnownAnnouncementSubjects.BAHRFIThreeDaysOverDue,
                        Body = tokenizer.Replace(WellKnownRFIMessages.BAHRFIThreeDaysOverDue, tokens, true),
                        IncludeAttachments = false,
                        SendNow = true,
                        LegalEntityIds = new List<Guid>()
                        {
                            submission.LegalEntityId
                        }
                    };

                    await _announcementDataManager.CreateUpdateAnnouncementAsync(createBahamasRfiData);

                    break;
                default:
                    break;
                // TODO
                    //throw new ConstraintException(
                    //    ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                    //    $"The jurisdiction '{submission.LegalEntity.Jurisdiction.Code}' is not configured.");
            }

            // Update the request for information entity
            requestForInformation.LastRemindedAt = DateTime.UtcNow;
            requestForInformation.ReminderType = RequestForInformationReminderType.ThreeDaysOverDue;

            // Log the notification event
            await _systemAuditManager.AddRfiThreeDaysOverdueNotificationActivityLogAsync(requestForInformation, submission, false);

            await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
        }

        /// <summary>
        /// Creates an announcement for a RFI that has been completed by the client.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        private async Task NotifyRFICompletedByClientAsync(Guid requestForInformationId)
        {
            // Checks and validations
            var requestForInformation = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);

            // Retrieve the management user responsible for creating the RFI
            var user = await _userRepository.FindByUserByPredicateAsync(u => u.Id == requestForInformation.CreatedBy);

            // Retrieve the submission entity
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                requestForInformation.SubmissionId,
                options: o => o.Include(s => s.LegalEntity.Jurisdiction));

            var tokens = new TokenList();
            var tokenizer = new Tokenizer();

            tokens.Add("userFullName", user.GetDisplayName());
            tokens.Add("companyCode", submission.LegalEntity.Code);
            tokens.Add("irdUrl", _trustOfficeOptions.ClientPortalUrl);
            tokens.Add("rfiDeadLine", requestForInformation.DeadLine.ToString(WellKnownDataConstants.DateFormat));

            // Setup the announcement data
            var createBahamasRfiData = new CreateUpdateAnnouncementDTO()
            {
                Subject = tokenizer.Replace(WellKnownAnnouncementSubjects.RFICompleted, tokens, true),
                EmailSubject = tokenizer.Replace(WellKnownAnnouncementSubjects.RFICompleted, tokens, true),
                Body = tokenizer.Replace(WellKnownRFIMessages.RFICompleted, tokens, true),
                IncludeAttachments = false,
                SendNow = true,
                LegalEntityIds = new List<Guid>()
                {
                    submission.LegalEntityId
                }
            };

            await _announcementDataManager.CreateUpdateAnnouncementAsync(createBahamasRfiData);

            // Update the request for information entity
            requestForInformation.LastRemindedAt = DateTime.UtcNow;
            requestForInformation.ReminderType = RequestForInformationReminderType.Completed;

            await _systemAuditManager.AddRfiCompletedNotificationActivityLogAsync(requestForInformation, submission);

            await _requestForInformationRepository.UpdateAsync(requestForInformation, true);
        }

        /// <summary>
        /// Validates that the Request for Information (RFI) and its associated Submission exist,
        /// and ensures that the RFI has not already been completed.
        /// </summary>
        /// <param name="requestForInformationId">The unique identifier of the Request for Information to validate.</param>
        /// <returns>
        /// A tuple containing the <see cref="RequestForInformation"/> and its associated <see cref="Submission"/>.
        /// </returns>
        /// <exception cref="PreconditionFailedException">
        /// Thrown if the RFI already has a status of <see cref="RequestForInformationStatus.Completed"/>.
        /// </exception>
        private async Task<(RequestForInformation request, Submission submission)> ValidateRfiAndSubmissionAsync(Guid requestForInformationId)
        {
            var request = await _requestForInformationRepository.CheckRequestForInformationByIdAsync(requestForInformationId);
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(request.SubmissionId);

            if (request.Status == RequestForInformationStatus.Completed)
            {
                throw new PreconditionFailedException(
                    ApplicationErrors.INVALID_REQUEST_FOR_INFORMATION_STATUS.ToErrorCode(),
                    $"The request for information with id '{requestForInformationId}' is already completed.");
            }

            return (request, submission);
        }
    }
    #endregion
}