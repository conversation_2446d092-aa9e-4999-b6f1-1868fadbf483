// <copyright file="ISimplifiedTaxReturnRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Nevis.SimplifiedTaxReturn.Populators
{
    /// <summary>
    /// Interface for the simplified tax return row populator.
    /// </summary>
    public interface ISimplifiedTaxReturnRowPopulator : ITemplateRowPopulator<SubmissionNevisReportDTO>, ITransientService;
}