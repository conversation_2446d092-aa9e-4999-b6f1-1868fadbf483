﻿using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Panama.BasicFinancialReport.Populators
{

    /// <summary>
    /// Interface for the regular fee line populator.
    /// </summary>
    public interface IRegularFeeLinePopulator : ITemplateRowPopulator<Submission>, ITransientService;

}
