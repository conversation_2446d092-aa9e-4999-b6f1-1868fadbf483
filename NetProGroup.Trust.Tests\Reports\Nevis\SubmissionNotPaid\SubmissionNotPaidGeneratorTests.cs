﻿using ClosedXML.Excel;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.Nevis.SubmissionsNotPaid;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports.Nevis.SubmissionNotPaid
{
    [TestFixture()]
    public class SubmissionNotPaidGeneratorTests : TestBase
    {
        private ISubmissionsIncludingDeletedRepository _submissionsRepository;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private LegalEntity _legalEntity1;
        private LegalEntity _legalEntity2;

        [SetUp]
        public async Task Setup()
        {
            _submissionsRepository = _server.Services.GetService<ISubmissionsIncludingDeletedRepository>();
            _legalEntitiesRepository = _server.Services.GetService<ILegalEntitiesRepository>();

            await Seed();
        }

        public async Task Seed()
        {
            _legalEntity1 = new LegalEntity
            {
                MasterClientId = _masterClient.Id,
                Name = "Company Test 1",
                Code = "E-LL 1",
                EntityType = LegalEntityType.Company,
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                JurisdictionId = JurisdictionNevisId
            };
            _legalEntity2 = new LegalEntity
            {
                MasterClientId = _masterClient.Id,
                Name = "Company Test 2",
                Code = "E-LL 2",
                EntityType = LegalEntityType.Company,
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                JurisdictionId = JurisdictionNevisId
            };

            LegalEntity[] legalEntities =
            [
                _legalEntity1,
                _legalEntity2
            ];

            foreach (var legalEntity in legalEntities)
            {
                await _legalEntitiesRepository.InsertAsync(legalEntity, true);
            }
        }


        [Test]
        public async Task GenerateSubmissionsNotPaidReportAsync_ShouldOnlyIncludeEntitiesWithSubmissions()
        {
            // Arrange
            var legalEntityWithoutSubmission = _legalEntity1;
            var legalEntityWithActiveSubmission = _legalEntity2;
            var activeSubmission = await CreateSubmission(legalEntityWithActiveSubmission.Id, 2023, submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc));

            // Act
            var result = await GetSystemUnderTest().GenerateSubmissionsNotPaidReportAsync();

            // Assert
            result.Should().NotBeNull();

            using XLWorkbook resultWorkbook = new XLWorkbook(new MemoryStream(result.FileContent));
            AssertCellValue(2023, 1, legalEntityWithActiveSubmission.Name,
                "The legal entity name should be correctly populated in the report.", resultWorkbook);
            AssertRowCount(2023, 2, resultWorkbook, "Worksheet for year 2023 should have 2 rows (1 header + 1 data).");
        }

        [Test]
        public async Task GenerateSubmissionsNotPaidReportAsync_ShouldOnlyIncludeEntitiesWithNonDeletedSubmissions()
        {
            // Arrange
            var legalEntityWithDeletedSubmission = _legalEntity1;
            var deletedSubmission = await CreateSubmission(legalEntityWithDeletedSubmission.Id, 2023, isDeleted: true, submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc));
            var legalEntityWithActiveSubmission = _legalEntity2;
            var activeSubmission = await CreateSubmission(legalEntityWithActiveSubmission.Id, 2023, submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc));

            // Act
            var result = await GetSystemUnderTest().GenerateSubmissionsNotPaidReportAsync();

            // Assert
            result.Should().NotBeNull();

            using XLWorkbook resultWorkbook = new XLWorkbook(new MemoryStream(result.FileContent));
            AssertRowCount(2023, 2, resultWorkbook, "Worksheet for year 2023 should have 2 rows (1 header + 1 data).");
            AssertCellValue(2023, 1, legalEntityWithActiveSubmission.Name,
                "The legal entity name should be correctly populated in the report.", resultWorkbook);
        }

        [Test]
        public async Task GenerateSubmissionsNotPaidReportAsync_EntityWithOnlyDeletedSubmissions_DoesNotIncludeEntity()
        {
            // Arrange
            var legalEntityWithDeletedSubmission = _legalEntity1;
            var deletedSubmission = await CreateSubmission(legalEntityWithDeletedSubmission.Id, 2023, isDeleted: true);

            // Act
            var result = await GetSystemUnderTest().GenerateSubmissionsNotPaidReportAsync();

            // Assert
            result.Should().NotBeNull();

            AssertRowCount(2023, 1,
                new XLWorkbook(new MemoryStream(result.FileContent)), "Worksheet for year 2023 should exist but be empty because the only submission is deleted.");
        }

        [Test]
        public async Task GenerateSubmissionsNotPaidReportAsync_EntityWitDeletedAndNonDeletedSubmissions_IncludesOnlyNonDeletedSubmission()
        {
            // Arrange
            var legalEntity = _legalEntity1;
            var deletedSubmission = await CreateSubmission(legalEntity.Id, 2023, submitterEmail: "deletedEmail", isDeleted: true, submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc));
            var activeSubmission = await CreateSubmission(legalEntity.Id, 2023, submitterEmail: "non-deleted email", submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc));

            // Act
            var result = await GetSystemUnderTest().GenerateSubmissionsNotPaidReportAsync();

            // Assert
            result.Should().NotBeNull();

            using XLWorkbook resultWorkbook = new XLWorkbook(new MemoryStream(result.FileContent));
            AssertCellValue(2023, 5, "non-deleted email", "Only the non-deleted submission should be included.", resultWorkbook);
            AssertRowCount(2023, 2, resultWorkbook, "Worksheet for year 2023 should have 2 rows (1 header + 1 data).");
        }

        [Test]
        public async Task GenerateSubmissionsNotPaidReportAsync_EntityWithPaidAndDeletedNonPaidSubmissions_IncludesNoSubmissions()
        {
            // Arrange
            var legalEntity = _legalEntity1;
            var deletedSubmission = await CreateSubmission(legalEntity.Id, 2023, submitterEmail: "deletedEmail", isDeleted: true, submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc), isPaid: true);
            var activeSubmission = await CreateSubmission(legalEntity.Id, 2023, submitterEmail: "non-deleted email", submittedAt: new DateTime(2024, 01, 11, 0, 0, 0, DateTimeKind.Utc), isPaid: true);
            
            // Act
            var result = await GetSystemUnderTest().GenerateSubmissionsNotPaidReportAsync();

            // Assert
            result.Should().NotBeNull();

            using XLWorkbook resultWorkbook = new XLWorkbook(new MemoryStream(result.FileContent));
            AssertRowCount(2023, 1, resultWorkbook, "Worksheet for year 2023 should have 1 header row.");
        }

        private ISubmissionsNotPaidReportGenerator GetSystemUnderTest()
        {
            return _server.Services.CreateScope().ServiceProvider.GetRequiredService<ISubmissionsNotPaidReportGenerator>();
        }

        private async Task<Submission> CreateSubmission(Guid legalEntity1Id, int? financialYear, string submitterEmail = null, bool isDeleted = false, SubmissionStatus status = SubmissionStatus.Draft, DateTime? submittedAt = null, bool isPaid = false)
        {
            var submission = new Submission()
            {
                LegalEntityId = legalEntity1Id,
                Status = status,
                Name = "Test Submission",
                Layout = "Test",
                ReportId = "TestReportId2",
                FinancialYear = financialYear,
                IsDeleted = isDeleted,
                ModuleId = base.ModuleStrId,
                SubmittedAt = submittedAt,
                IsPaid = isPaid,
            };
            submission?.Attributes.SetAttributeValue<string>(SubmissionAttributeKeys.SubmittedByEmail, submitterEmail);

            await _submissionsRepository.InsertAsync(submission, true);
            return submission;
        }

        /// <summary>
        /// Helper method to assert a cell value in the Excel report.
        /// </summary>
        /// <param name="financialYear">The financial year worksheet to check.</param>
        /// <param name="columnIndex">The column index (1-based).</param>
        /// <param name="expectedValue">The expected value.</param>
        /// <param name="message">The assertion message.</param>
        /// <param name="resultWorkbook"></param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private void AssertCellValue(int financialYear,
            int columnIndex,
            string expectedValue,
            string message,
            XLWorkbook resultWorkbook)
        {
            var worksheet = resultWorkbook.Worksheet(financialYear.ToString());
            var cellValue = worksheet.Cell(2, columnIndex).Value.ToString();
            cellValue.Should().Be(expectedValue, message);
        }

        private void AssertRowCount(int financialYear,
            int expectedRowCount,
            XLWorkbook resultWorkbook,
            string message = null)
        {
            var worksheet = resultWorkbook.Worksheet(financialYear.ToString());
            var lastUsedRow = worksheet.LastRowUsed()?.RowNumber() ?? 0;
            lastUsedRow.Should().Be(expectedRowCount, message);
        }
    }
}
