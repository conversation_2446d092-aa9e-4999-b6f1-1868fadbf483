parameters:
# Target environment (dev, tst, acc, prd)
- name: targetEnvironment
  type: string
  default: 'dev'
# Data correction script name
- name: scriptName
  type: string
  default: ''
# Service connection
- name: serviceConnection
  type: string

jobs:
- deployment: RunDataCorrectionScript
  displayName: 'Run Data Correction Script - ${{ parameters.targetEnvironment }}'
  variables:
  - name: serviceConnection
    value: ${{ parameters.serviceConnection }}
  - template: variables/global.yml
  - template: variables/${{ parameters.targetEnvironment }}.yml
  pool:
    name: ${{ variables.pool }}
  environment:
    name: ${{ variables.environment }}
  strategy:
    runOnce:
      deploy:
        steps:
        - checkout: self
        - task: PowerShell@2
          displayName: 'Log Restore Point Timestamp'
          inputs:
            targetType: 'inline'
            script: |
              $RestorePointTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
              
              Write-Host "RESTORE POINT TIMESTAMP: $RestorePointTime"
              Write-Host "Use this timestamp for point-in-time restore if rollback is needed"
              Write-Host "Database: $(databaseName)"
              Write-Host "Server: $(serverName)"
              Write-Host "Script: ${{ parameters.scriptName }}"
              Write-Host "Environment: ${{ parameters.targetEnvironment }}"

        - task: SqlAzureDacpacDeployment@1
          displayName: 'Execute Data Correction Script: ${{ parameters.scriptName }}'
          inputs:
            azureSubscription: ${{ variables.serviceConnection }}
            AuthenticationType: servicePrincipal
            ServerName: '$(serverName)'
            DatabaseName: '$(databaseName)'
            deployType: SqlTask
            SqlFile: '$(Build.SourcesDirectory)/NetProGroup.Trust.Repository/SQL/DataCorrections/${{ parameters.scriptName }}'

        - task: PowerShell@2
          displayName: 'Log Data Correction Execution'
          inputs:
            targetType: 'inline'
            script: |
              Write-Host "Data correction script '${{ parameters.scriptName }}' executed successfully on environment '${{ parameters.targetEnvironment }}'"
              Write-Host "Database: $(databaseName)"
              Write-Host "Server: $(serverName)"
              Write-Host "Service Connection: ${{ variables.serviceConnection }}"
