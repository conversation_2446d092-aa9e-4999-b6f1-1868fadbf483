using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual
{
    /// <summary>
    /// Interface for exporting 2023 STR submissions.
    /// </summary>
    public interface IExport2023SubmissionsIRDGenerator : IExportYearSubmissionsIRDGenerator, IScopedService;


    /// <summary>
    /// Manager for exporting 2023 submissions.
    /// </summary>
    public class Export2023SubmissionsIRDGenerator : BaseExportSubmissionsIRDGenerator, IExport2023SubmissionsIRDGenerator
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Export2023SubmissionsIRDGenerator"/> class.
        /// </summary>
        /// <param name="logger">the logger.</param>
        /// <param name="templateProvider">template provider.</param>
        /// <param name="submissionsManager">The submissions manager.</param>
        public Export2023SubmissionsIRDGenerator(ILogger<BaseExportSubmissionsIRDGenerator> logger,
            IReportTemplateProvider templateProvider, ISubmissionReportsDataManager submissionsManager)
            : base(logger, templateProvider, submissionsManager)
        {
        }

        /// <inheritdoc />
        protected override int Year { get => 2023; }

        /// <inheritdoc />
        protected override string ExportModule { get => ModuleKeyConsts.SimplifiedTaxReturn; }

        /// <inheritdoc />
        protected override string ExportJurisdiction { get => JurisdictionCodes.Nevis; }

        /// <inheritdoc/>
        public override async Task<ReportDownloadResponseDTO> ExportAsync(ExportSubmissionDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var template = await GetTemplateContentAsync();

            // Modify the file in memory with ClosedXML
            using var workbook = new XLWorkbook(template);
            var submissions = await GetSubmissions(request);

            ModifyWorkbook(workbook, submissions);

            // Save modified workbook to a new FileStream stream
            var modifiedStream = new MemoryStream();
            workbook.SaveAs(modifiedStream);
            return CreateResponse(modifiedStream);
        }

        private void ModifyWorkbook(XLWorkbook workbook, List<Submission> submissions)
        {
            ProcessSubmissionsTab(workbook, submissions, form =>
            {
                var values = new List<XLCellValue>();
                var nonTaxResident = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentNonTaxResident, "false"));
                var isPartOfMneGroup = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNEIsPartOfMNEGroup, "false"));
                var requiresCbeReport = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNERequiresCbCReport, "false"));
                var recordsKeptAtRegisteredOffice = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAddressRecordsKeptAtRegisteredOffice, "false"));

                // Question No 1 (Yes/No)
                values.Add("");

                // Question No 1.1 (Yes/No)
                values.Add(nonTaxResident
                    ? "True"
                    : "False");

                // Question No 1.2 (Yes/No)
                values.Add(nonTaxResident
                    ? ""
                    : GetCountryOrNoCountry(GetValueOrDefault(form, FormKeys.TaxResidentResidentCountry)));

                // Question No 2 (Yes/No)
                values.Add((!nonTaxResident)
                    ? recordsKeptAtRegisteredOffice
                        ? "True"
                        : "False"
                    : "");

                // Question No 2.1 (Yes/No) 
                XLCellValue corporateAddress = $"{(recordsKeptAtRegisteredOffice ?
                    GetValueOrDefault(form, "corporate-address.nevisAddress")
                    : GetValueOrDefault(form, "corporate-address.recordsPlaceAddress"))}";
                values.Add(!nonTaxResident
                    ? corporateAddress
                    : "");

                // Question No 3 (Yes/No)
                values.Add(!nonTaxResident
                    ? isPartOfMneGroup
                        ? "True"
                        : "False"
                    : "");

                // Question No 3.1 (Yes/No)
                values.Add(
                    !nonTaxResident && isPartOfMneGroup
                        ? requiresCbeReport ? "True" : "False"
                        : "");

                return values;
            });
        }
    }
}
