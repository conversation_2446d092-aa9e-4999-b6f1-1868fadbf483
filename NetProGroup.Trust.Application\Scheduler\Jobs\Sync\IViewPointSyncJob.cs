﻿// <copyright file="IViewPointSyncJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Sync
{
    /// <summary>
    /// Scheduled job for syncing ViewPoint data.
    /// </summary>
    public interface IViewPointSyncJob : ICronJob, ITransientService;
}