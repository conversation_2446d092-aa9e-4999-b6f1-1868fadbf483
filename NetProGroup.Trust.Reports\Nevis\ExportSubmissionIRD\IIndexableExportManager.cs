namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD
{
    /// <summary>
    /// Interface for export managers that can be indexed.
    /// </summary>
    public interface IIndexableExportManager
    {
        /// <summary>
        /// Gets the index name for the export.
        /// </summary>
        /// <returns>The index name for the export.</returns>
        public abstract string GetIndexName();
    }
}