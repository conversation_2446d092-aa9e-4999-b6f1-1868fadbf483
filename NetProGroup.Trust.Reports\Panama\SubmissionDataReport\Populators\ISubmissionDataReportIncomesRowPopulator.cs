// <copyright file="ISubmissionDataReportIncomesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Interface for the submission data report row populator.
    /// </summary>
    public interface ISubmissionDataReportIncomesRowPopulator : ITemplateRowPopulator<Submission>, ITransientService;
}