﻿// <copyright file="TemplateNotFoundException.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Locks.Models;

namespace NetProGroup.Trust.DataManager.Exceptions
{
    /// <summary>
    /// An exception to throw when the lock was not found.
    /// </summary>
    public class TemplateNotFoundException : Exception
    {
        private LockDTO _lockDTO;

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateNotFoundException"/> class.
        /// </summary>
        public TemplateNotFoundException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateNotFoundException"/> class.
        /// </summary>
        /// <param name="lockDTO">The lock that could not be found.</param>
        public TemplateNotFoundException(LockDTO lockDTO)
        {
            _lockDTO = lockDTO;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateNotFoundException"/> class.
        /// </summary>
        /// <param name="message">The message for the exception.</param>
        public TemplateNotFoundException(string message)
            : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateNotFoundException"/> class.
        /// </summary>
        /// <param name="message">The message for the exception.</param>
        /// <param name="lockDTO">The lock that could not be found.</param>
        public TemplateNotFoundException(string message, LockDTO lockDTO)
            : base(message)
        {
            _lockDTO = lockDTO;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateNotFoundException"/> class.
        /// </summary>
        /// <param name="messageFormat">The message format for the exception.</param>
        /// <param name="args">The formatting arguments.</param>
        public TemplateNotFoundException(string messageFormat, params object[] args)
            : base(string.Format(messageFormat, args))
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateNotFoundException"/> class.
        /// </summary>
        /// <param name="message">The message for the exception.</param>
        /// <param name="innerException">The inner exception.</param>
        public TemplateNotFoundException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        /// <summary>
        /// Gets the non existing lock.
        /// </summary>
        public LockDTO NonExistingLock => _lockDTO;
    }
}