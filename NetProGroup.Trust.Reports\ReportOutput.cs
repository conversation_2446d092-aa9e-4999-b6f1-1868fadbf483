namespace NetProGroup.Trust.Reports
{
    /// <summary>
    /// Output data for reports.
    /// </summary>
    public class ReportOutput
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ReportOutput"/> class.
        /// </summary>
        /// <param name="fileContent">The file content.</param>
        public ReportOutput(byte[] fileContent)
        {
            FileContent = fileContent;
        }

        /// <summary>
        /// Gets the file content.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Performance", "CA1819:Properties should not return arrays", Justification = "By design")]
        public byte[] FileContent { get; }
    }
}