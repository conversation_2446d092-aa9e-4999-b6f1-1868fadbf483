// <copyright file="CompaniesStrSubmissionStatusReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.ApplicationInsights;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.AppServices.Reports.Nevis.CompaniesStrSubmissionStatusReports;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <summary>
    /// Report job implementation for generating reports of companies without submissions.
    /// </summary>
    public class CompaniesStrSubmissionStatusReportJob(
        ILogger<CompaniesStrSubmissionStatusReportJob> logger,
        IServiceProvider serviceProvider)
        : JobBase<object>(logger, serviceProvider), ICompaniesWithoutSubmissionsReportJob
    {
        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid("{B2D45E7F-8A12-4C89-B6E3-1F2A9D5B0C8D}");

        /// <inheritdoc/>
        public string ScheduledJobKey => "reports.nevis.companies-str-submission-status";

        /// <inheritdoc/>
        public string ScheduledJobName => "Companies STR Submission Status Report Job - Nevis";

        /// <inheritdoc/>
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            var jobLock = await base.AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                var companiesWithoutSubmissionsReportService = ServiceProvider.GetRequiredService<ICompaniesStrSubmissionStatusReportService>();

                Logger.LogInformation("Starting report generation for {JobName}", ScheduledJobName);
                await companiesWithoutSubmissionsReportService.GenerateReportAsync(jobLock);
                Logger.LogInformation("Finished report generation for {JobName}", ScheduledJobName);
            }
            finally
            {
                await ReleaseLockAsync(jobLock);
            }
        }
    }
}
