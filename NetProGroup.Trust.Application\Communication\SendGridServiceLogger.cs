﻿// <copyright file="SendGridServiceLogger.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.SendGrid;
using NetProGroup.Framework.Services.Communication.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;

namespace NetProGroup.Trust.Application.Communication
{
    /// <summary>
    /// A logger for the SendGrid service that logs email sending operations.
    /// </summary>
    internal sealed class SendGridServiceLogger : ISendGridService
    {
        private readonly SendGridService _sendGridServiceImplementation;
        private readonly ILogger<SendGridService> _logger;
        private readonly IApplicationInsightsDependencyTracker _dependencyTracker;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendGridServiceLogger"/> class.
        /// </summary>
        /// <param name="sendGridServiceImplementation">The SendGrid service implementation.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="dependencyTracker">The dependency tracker for app insights.</param>
        public SendGridServiceLogger(
            SendGridService sendGridServiceImplementation,
            ILogger<SendGridService> logger,
            IApplicationInsightsDependencyTracker dependencyTracker)
        {
            _sendGridServiceImplementation = Check.NotNull(sendGridServiceImplementation, nameof(sendGridServiceImplementation));
            _logger = Check.NotNull(logger, nameof(logger));
            _dependencyTracker = Check.NotNull(dependencyTracker, nameof(dependencyTracker));
        }

        /// <inheritdoc />
        public async Task<bool> SendEmailAsync(EmailInfo emailInfo, string toEmail, string userName)
        {
            return await _dependencyTracker.TrackDependencyAsync(
                "SendGrid.SendEmail",
                () =>
                {
                    _logger.LogInformation("Sending email {EmailProperties}", new
                    {
                        toEmail,
                        userName,
                        emailInfo.Subject,
                        emailInfo.ToUserId
                    });

                    return _sendGridServiceImplementation.SendEmailAsync(emailInfo, toEmail, userName);
                },
                "Email",
                "SendGrid",
                $"To: {toEmail}, Subject: {emailInfo.Subject}");
        }

        /// <inheritdoc />
        public async Task<bool> SendEmailAsync(QueuedMessageDTO message)
        {
            return await _dependencyTracker.TrackDependencyAsync(
                "SendGrid.SendEmail",
                () =>
                {
                    _logger.LogInformation("Sending email {EmailProperties}", new
                    {
                        message.To,
                        message.ToName,
                        message.Subject
                    });

                    return _sendGridServiceImplementation.SendEmailAsync(message);
                },
                "Email",
                "SendGrid",
                $"To: {message.To}, Subject: {message.Subject}");
        }
    }
}