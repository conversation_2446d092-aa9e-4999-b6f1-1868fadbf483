using NetProGroup.Trust.Shared.FormDocuments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NetProGroup.Trust.Reports.Bahamas.Helpers
{
    /// <summary>
    /// Setup helper method for Bahamas reports.
    /// </summary>
    public static class BahamasReportHelperMethods
    {
        /// <summary>
        /// Retrieves the relevant activity key based on the name.
        /// </summary>
        /// <param name="relevantActivity">The relevant activity name as WellKnownBahamasRelevantActivities.</param>
        /// <returns>The relevant activity key as WellKnownBahamasRelevantActivityKeys.</returns>
        public static string RetrieveRelevantActivityKey(string relevantActivity)
        {
            return relevantActivity switch
            {
                WellKnownBahamasRelevantActivities.HoldingBusiness => WellKnownFormDocumentAttibuteKeys.HoldingBusiness,
                WellKnownBahamasRelevantActivities.FinanceLeasingBusiness => WellKnownFormDocumentAttibuteKeys.FinanceLeasingBusiness,
                WellKnownBahamasRelevantActivities.BankingBusiness => WellKnownFormDocumentAttibuteKeys.BankingBusiness,
                WellKnownBahamasRelevantActivities.InsuranceBusiness => WellKnownFormDocumentAttibuteKeys.InsuranceBusiness,
                WellKnownBahamasRelevantActivities.FundManagementBusiness => WellKnownFormDocumentAttibuteKeys.FundManagementBusiness,
                WellKnownBahamasRelevantActivities.HeadquartersBusiness => WellKnownFormDocumentAttibuteKeys.HeadquartersBusiness,
                WellKnownBahamasRelevantActivities.ShippingBusiness => WellKnownFormDocumentAttibuteKeys.ShippingBusiness,
                WellKnownBahamasRelevantActivities.IntellectualPropertyBusiness => WellKnownFormDocumentAttibuteKeys.IntellectualPropertyBusiness,
                WellKnownBahamasRelevantActivities.DistributionAndServiceCentreBusiness => WellKnownFormDocumentAttibuteKeys.DistributionAndServiceCentreBusiness,
                WellKnownBahamasRelevantActivities.None => WellKnownFormDocumentAttibuteKeys.None,

                _ => throw new ArgumentOutOfRangeException(nameof(relevantActivity), relevantActivity, null)
            };
        }
    }
}