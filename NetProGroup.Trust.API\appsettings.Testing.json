{
  // Identifies the API to authenticate incoming authentication requests
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "30350f35-feb0-4bbc-877f-43ff406a41e5",
    "ClientId": "bdcf01f6-a196-478c-b778-7cbf57f19e5f",
    "AllowWebApiToBeAuthorizedByACL": true,
    "Audience": "api://bdcf01f6-a196-478c-b778-7cbf57f19e5f"
  },
  "AppRegistration": {
    "TenantId": "30350f35-feb0-4bbc-877f-43ff406a41e5",
    "ClientId": "bdcf01f6-a196-478c-b778-7cbf57f19e5f",
    "ClientSecret": "****************************************"
  },
  "ExternalId": {
    "ClientId": "6a269e8d-51f6-4be3-af09-e612426016a5",
    "TenantId": "4053da93-8216-46fd-a82a-a32155693958"
  },
  "BlobStorage": {
    "AccountName": "sadevpcpeus2",
    "ContainerName": "documents"
  },
  "Azure": {
    "MSGraph": {
      "AD": {
        // Use defaults from AppRegistration
        "Scopes": ".default"
      }
    }
  },
  "Smtp": {
    "host": "smtp.sendgrid.net",
    "port": 587,
    "username": "apikey",
    "password": "*********************************************************************",
    "fromEmail": "<EMAIL>",
    "nameEmail": "NetPro Dev"
  },
  // These settings are for the trust office
  "TrustOffice": {
    "EmailDomain": "netprogroup.com",
    "ProductionOfficeEmailSuffix": "noreply",
    "ClientPortalUrl": "https://clientfilings.netprodevelopment.com ",
    "InvitationWhiteList": [
      "*@netprogroup.com"
    ],
    "AllowedDomains": "netprogroup.com,netprogroupnv.onmicrosoft.com",
    "RecipientOverride": {
      "ProductionOffice": "<EMAIL>",
      "Invitation": "<EMAIL>",
      "Announcement": "<EMAIL>"
    },
    "SendInvitationToUserEnabled": false
  },
  "DataMigration": {
    "Enabled": true,
    "ProgressUpdateInterval": 10,
    "ActiveJurisdiction": "Bahamas",
    "CountryOverrides": {
      "KNA": "St. Kitts and Nevis"
    },
    "Jurisdictions": {
      "Bahamas": {
        "MongoConnectionString": "",
        "MongoDatabaseName": "",
        "StorageAccounts": [
          {
            "Key": "SourceFileStorageBahamas",
            "AccountName": "",
            "DefaultContainer": ""
          }
        ]
      }
    }
  },
  "DataSync": {
    "Enabled": true,
    "JurisdictionCodes": [
      "KN", // Saint Kitts & Nevis
      "BS", // Bahamas
      "PA" // Panama
    ]
  },
  "FeatureFlags": {
    "Announcements": true
  },
  "ScheduledJobs": {
    "reports.nevis.contacts-info": {
      "CronExpression": "0 0 * * *",
      "Enabled": true
    },
    "reports.nevis.companies-str-submission-status": {
      "CronExpression": "0 0 * * *",
      "Enabled": true
    },
    "sync.viewpoint": {
      "CronExpression": "/20 * * * *",
      "Enabled": true
    },
    "reports.panama.basic-financial-report": {
      "CronExpression": "0 0 * * *",
      "Enabled": false
    },
    "submissions.scheduled": {
      "CronExpression": "0 0 * * *",
      "Enabled": false
    },
    "reports.nevis.financial": {
      "CronExpression": "0 0 * * *",
      "Enabled": true
    },
    "reports.nevis.submissions-not-paid": {
      "CronExpression": "0 0 * * *",
      "Enabled": true
    }
  }
}
