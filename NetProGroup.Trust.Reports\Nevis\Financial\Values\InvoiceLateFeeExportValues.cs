namespace NetProGroup.Trust.Reports.Nevis.Financial.Values
{
    /// <summary>
    /// Represents the late fee for invoices.
    /// </summary>
    public static class InvoiceLateFeeExportValues
    {
        /// <summary>
        /// The invoice type.
        /// </summary>
        public static string InvoiceType => "DR_N";

        /// <summary>
        /// The matter code associated with the invoice.
        /// </summary>
        public static string MatterCode => "GE";

        /// <summary>
        /// The currency code for the invoice.
        /// </summary>
        public static string CurCode => "USD";

        /// <summary>
        /// The exchange rate for the invoice.
        /// </summary>
        public static string Exchange => "1";

        /// <summary>
        /// The payment terms for the invoice.
        /// </summary>
        public static string PayTerm => "O3";

        /// <summary>
        /// The service code for the invoice.
        /// </summary>
        public static string ServCode => "PSTRPEN";

        /// <summary>
        /// The description of the invoice.
        /// </summary>
        public static string Description => "STR late Penalty charge";

        /// <summary>
        /// The local currency code for the invoice.
        /// </summary>
        public static string LCurCode => "USD";

        /// <summary>
        /// The local exchange rate for the invoice.
        /// </summary>
        public static string LExchange => "1";

        /// <summary>
        /// The number of units for the invoice.
        /// </summary>
        public static string NrUnits => "1";

        /// <summary>
        /// The VAT percentage for the invoice.
        /// </summary>
        public static string VATPerc => "0";

        /// <summary>
        /// The tax code for the invoice.
        /// </summary>
        public static string TaxCode => "NAP";
    }
}