// <copyright file="QuorumOfDirectorsRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class QuorumOfDirectorsRowPopulator : LinePopulatorBase, IQuorumOfDirectorsRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.RelevantActivities).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.RelevantActivities)[1].Split(".")[0]);

            foreach (var relevantActivityGroup in relevantActivityGroups)
            {
                // Check if the activity was selected
                var isSelected = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Selected);

                if (isSelected == "true")
                {
                    // Retrieve the name
                    var relevantActivity = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Retrieve the relevant activity data
                    var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                    // Retrieve the created directors
                    var directors = relevantActivityData.GetAttributesWithKey(WellKnownFormDocumentAttibuteKeys.Directors).ToList();

                    // Group the directors
                    var directorGroups = directors.GroupBy(a => a.Key.Split(relevantActivityKey + WellKnownFormDocumentAttibuteKeys.Directors)[1].Split(".")[0]);

                    foreach (var directorGroup in directorGroups)
                    {
                        // Retrieve the entity unique id
                        var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                        SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                        // Retrieve the name
                        var activityName = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                        SetCellValueAndStyle(worksheet, currentRow, 2, activityName);

                        var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                        SetCellValueAndStyle(worksheet, currentRow, 3, financialPeriodEndDate);

                        // Retrieve the meeting numbers
                        var meetingNumbers = directorGroup.ToList().GetAttributesWithKey(WellKnownFormDocumentAttibuteKeys.MeetingNumber);

                        SetCellValueAndStyle(worksheet, currentRow, 4, String.Join(", ", meetingNumbers?.Select(a => a.Value).ToList()));

                        // Retrieve the director name
                        var directorName = directorGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Name);

                        SetCellValueAndStyle(worksheet, currentRow, 5, directorName);

                        // Retrieve if the director was physically present in the meeting
                        var physicallyPresentInBahamas = directorGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.PhysicallyPresentInBahamas);

                        SetCellValueAndStyle(worksheet, currentRow, 6, physicallyPresentInBahamas);

                        // Retrieve the director qualification
                        var qualification = directorGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Qualification);

                        SetCellValueAndStyle(worksheet, currentRow, 7, qualification);

                        // Retrieve the years of experience
                        var yearsOfExperience = directorGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.YearsOfExperience);

                        SetCellValueAndStyle(worksheet, currentRow, 8, yearsOfExperience);

                        currentRow += 1;
                    }
                }
            }

        }
    }
}