trigger:
  branches:
    include:
      - main
      - development
  tags:
    exclude:
      - '*'

pool:
  vmImage: ubuntu-latest

variables:
  isDevelopmentBranch: ${{ eq(variables['Build.SourceBranch'], 'refs/heads/development') }}
  isMainBranch: ${{ eq(variables['Build.SourceBranch'], 'refs/heads/main') }}
  # Variables for CICD pipeline trigger
  cicdPipelineName: API - CICD
  azureDevOpsOrg: https://dev.azure.com/netprogroup
  azureDevOpsProject: Trident Trust - Private Client Portal

jobs:
  - job: BumpVersion
    displayName: 'Bump version'
    steps:
      - checkout: self
        clean: true
        persistCredentials: true
        fetchDepth: 0
        displayName: 'Checkout repository'

      - task: NodeTool@0
        inputs:
          versionSpec: '20.x'
          checkLatest: true
        displayName: 'Install Node.js'

      - script: |
          npm install --include=dev
        displayName: 'Install dependencies'

      - script: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure Build Pipeline"
        displayName: 'Set git user'

      - task: Npm@1
        displayName: 'Bump version with alpha tag'
        # condition: eq(variables.isDevelopmentBranch, true)
        condition: false # Disable for now, as this was causing merge conflicts
        inputs:
          command: 'custom'
          workingDir: '.'
          customCommand: 'run release -- --prerelease alpha'

      - task: Npm@1
        displayName: 'Bump version'
        condition: eq(variables.isMainBranch, true)
        inputs:
          command: 'custom'
          workingDir: '.'
          customCommand: 'run release'

      - script: |
          tag=$(git describe --tags --abbrev=0)
          echo "##vso[task.setvariable variable=tag]$tag"

          git push --follow-tags origin HEAD:$(Build.SourceBranch)
        displayName: 'Push changes with tags'

      # Trigger CICD pipeline with the appropriate source
      - pwsh:  |
          echo "Triggering CICD pipeline for branch: $env:branch"

          # Use Azure DevOps REST API to trigger the CICD pipeline
          az pipelines run --name "$(cicdPipelineName)" --branch "$env:branch" --organization "$(azureDevOpsOrg)" --project "$(azureDevOpsProject)"

          if($LASTEXITCODE -ne 0) {
            throw "Failed to trigger CICD pipeline."
          } else {
            Write-Host "CICD pipeline triggered successfully."
          }
        displayName: 'Trigger CICD pipeline'
        env:
          AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
          ${{ if eq(variables.isDevelopmentBranch, true) }}:
            branch: $(Build.SourceBranch)
          ${{ else }}:
            branch: refs/tags/$(Tag)
