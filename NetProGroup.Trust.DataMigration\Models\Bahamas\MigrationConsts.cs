namespace NetProGroup.Trust.DataMigration.Models.Bahamas;

/// <summary>
/// Contains constant values and collections used in the migration process.
/// </summary>
public static class MigrationConsts
{
    /// <summary>
    /// A set of valid business activities used in the migration process.
    /// </summary>
    public static readonly IReadOnlySet<string> ValidBusinessActivities = new HashSet<string>
    {
        "Banking Business",
        "Insurance Business",
        "Fund Management Business",
        "Finance and Leasing Business",
        "Headquarters Business",
        "Shipping Business",
        "Holding Business",
        "Intellectual Property Business",
        "Distribution and Service Centre Business"
    };

    /// <summary>
    /// Constants for Nevis Invoice Configurations.
    /// </summary>
    internal static class NevisInvoiceConfigurations
    {
        /// <summary>
        /// The collection name for Nevis Invoice Configurations.
        /// </summary>
        public const string CollectionName = "nevisinvoiceconfigurations";

        /// <summary>
        /// The display name for Nevis Invoice Configurations.
        /// </summary>
        public const string DisplayName = "Nevis Invoice Configurations";
    }

    /// <summary>
    /// Constants for Entries.
    /// </summary>
    internal static class Entries
    {
        /// <summary>
        /// The collection name for Entries.
        /// </summary>
        public const string CollectionName = "entries";

        /// <summary>
        /// The display name for Entries.
        /// </summary>
        public const string DisplayName = "Submissions";
    }
    
    /// <summary>
    /// Bahamas entry status constants.
    /// </summary>
    internal static class EntryStatus
    {
        /// <summary>
        /// Submitted.
        /// </summary>
        public const string Submitted = "SUBMITTED";

        /// <summary>
        /// Paid.
        /// </summary>
        public const string Paid = "PAID";

        /// <summary>
        /// Saved.
        /// </summary>
        public const string Saved = "SAVED";

        /// <summary>
        /// Re-Open.
        /// </summary>
        public const string ReOpen = "RE-OPEN";

        /// <summary>
        /// Information request.
        /// </summary>
        public const string InformationRequest = "INFORMATION REQUEST";
    }

    /// <summary>
    /// Constants for Companies.
    /// </summary>
    internal static class Companies
    {
        /// <summary>
        /// The collection name for Companies.
        /// </summary>
        public const string CollectionName = "companies";

        /// <summary>
        /// The display name for Companies.
        /// </summary>
        public const string DisplayName = "Companies";
    }

    /// <summary>
    /// Constants for Messages.
    /// </summary>
    internal static class Messages
    {
        /// <summary>
        /// The collection name for Messages.
        /// </summary>
        public const string CollectionName = "messages";

        /// <summary>
        /// The display name for Messages.
        /// </summary>
        public const string DisplayName = "Messages";
    }
}
