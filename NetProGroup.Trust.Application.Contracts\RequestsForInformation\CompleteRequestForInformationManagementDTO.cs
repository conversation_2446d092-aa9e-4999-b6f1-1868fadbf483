﻿// <copyright file="CompleteRequestForInformationManagementDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Tools;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represents the data transfer object (DTO) used to complete a request for information (RFI) as a management operation.
    /// </summary>
    public class CompleteRequestForInformationManagementDTO
    {
        /// <summary>
        /// Gets or sets the date and time when the submission was sent to the regulator.
        /// </summary>
        public DateTime SubmittedToRegulator { get; set; }

        /// <summary>
        /// Gets or sets a remark or comment associated with the object.
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Validates the current object's state to ensure all required properties are set and meet the expected
        /// conditions.
        /// </summary>
        /// <remarks>This method checks that the <see cref="SubmittedToRegulator"/> property is not the
        /// default value or null,  and that the <see cref="Remark"/> property is not null, empty, or consists only of
        /// whitespace. If any validation fails, an exception is thrown.</remarks>
        public void Validate()
        {
            Check.NotDefaultOrNull<DateTime>(SubmittedToRegulator, nameof(SubmittedToRegulator));
            Check.NotNullOrWhiteSpace(Remark, nameof(Remark));
        }
    }
}
