﻿// <copyright file="ICommonSubmissionsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Modules;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Interface for the common methods for a SubmissionsDataManager.
    /// </summary>
    public interface ICommonSubmissionsDataManager
    {
        /// <summary>
        /// Lists the submissions for a specific legal entity and module. No search parameters.
        /// </summary>
        /// <remarks>
        /// The purpose of the method is to be used by the client portal.
        /// </remarks>
        /// <param name="request">The request with the parameters.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(ListSubmissionsRequest request);

        /// <summary>
        /// Searches for the submissions for a module with extra search criteria.
        /// </summary>
        /// <remarks>
        /// The purpose of the method is to be used by the management portal.
        /// </remarks>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsAsync(SearchSubmissionsRequest request);

        /// <summary>
        /// Gets just the submission without any other data. No check on permission.
        /// </summary>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId);

        /// <summary>
        /// Starts a new submission.
        /// </summary>
        /// <param name="model">The necessary data used to start a submission as StartSubmissionDTO.</param>
        /// <param name="jurisdiction">The jurisdiction entity.</param>
        /// <param name="module">The module entity.</param>
        /// <returns>The created submission as SubmissionDTO.</returns>
        Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model, Jurisdiction jurisdiction, Module module);

        /// <summary>
        /// Update the general information for a submission entity.
        /// </summary>
        /// <remarks>
        /// Used to check and update the basic configuration for submissions.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="model">The data used to update the general information for the submission.</param>
        /// <param name="saveChanges">Indicates whther the changes must be saved.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, UpdateSubmissionInformationDTO model, bool saveChanges = false);

        /// <summary>
        /// Submits the submission. This will make the submission final.
        /// </summary>
        /// <param name="model">The model for submitting ther submission.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model);

        /// <summary>
        /// Reopens the submission by creating a new revision in draft status.
        /// </summary>
        /// <param name="model">The model for re-opening ther submission.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model);

        /// <summary>
        /// Lists the submissions that are scheduled for submission.
        /// </summary>
        /// <param name="scheduledDate">The optional date that the submission is scheduled for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<List<ListSubmissionDTO>> ListScheduledSubmissionsAsync(DateTime? scheduledDate);

        /// <summary>
        /// Does the actual submission when it was scheduled.
        /// </summary>
        /// <param name="submissionId">The id of the submission.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SubmitScheduledSubmissionAsync(Guid submissionId);

        /// <summary>
        /// Gets the specific submission.
        /// </summary>
        /// <param name="submissionId">The submission id.</param>
        /// <param name="includeFormDocument">True to include document.</param>
        /// <returns>The submission.</returns>
        Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, bool includeFormDocument);

        /// <summary>
        /// Gets the specific submission revision.
        /// </summary>
        /// <param name="submissionId">The submission id.</param>
        /// <param name="revisionId">The revision id.</param>
        /// <param name="includeFormDocument">True to include document.</param>
        /// <returns>The submission.</returns>
        Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, Guid revisionId, bool includeFormDocument);
    }
}
