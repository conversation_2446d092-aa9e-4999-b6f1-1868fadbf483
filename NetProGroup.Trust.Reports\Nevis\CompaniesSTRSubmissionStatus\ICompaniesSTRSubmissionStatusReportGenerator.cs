// <copyright file="ICompaniesStrSubmissionStatusReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Reports.Nevis.CompaniesSTRSubmissionStatus
{
    /// <summary>
    /// Interface for companies without submissions report generator.
    /// </summary>
    public interface ICompaniesStrSubmissionStatusReportGenerator : ITransientService
    {
        /// <summary>
        /// Generates a report of companies without submissions.
        /// </summary>
        /// <returns>A <see cref="Task{ReportOutput}"/> representing the asynchronous operation.</returns>
        Task<ReportOutput> GenerateReportAsync();

        /// <summary>
        /// Generates the report name for today's report.
        /// </summary>
        /// <returns>The report name.</returns>
        string GenerateReportNameForTodayAsync();
    }
}
