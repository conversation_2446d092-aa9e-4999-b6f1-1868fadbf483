// <copyright file="DirectionRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class DirectionRowPopulator : LinePopulatorBase, IDirectionRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);

            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = @"relevant-activity-declaration\.relevantActivities\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var relevantActivity = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Get director indexes for this relevant activity
                    var directorIndexes = GetDirectorIndexes(form!.DataSet, relevantActivityKey);

                    List<int> GetDirectorIndexes(Dictionary<string, string> dataSet, string activityKey)
                    {
                        var pattern = $@"{activityKey.Replace(".", "\\.", StringComparison.Ordinal)}{WellKnownFormDocumentAttibuteKeys.Directors.Replace(".", "\\.", StringComparison.Ordinal)}(\d+)\.";
                        return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
                    }

                    foreach (var directorIndex in directorIndexes)
                    {
                        // Retrieve the entity unique id
                        SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                        // Retrieve the name
                        SetCellValueAndStyle(worksheet, currentRow, 2, relevantActivity);

                        SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                        // Retrieve the director name
                        var directorName = GetValueOrDefault(form, FormKeys.DirectorsName(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 4, directorName);

                        // Retrieve if the director is resident in Bahamas
                        var isResidentInBahamas = GetValueOrDefault(form, FormKeys.DirectorsIsResidentInBahamas(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 5, isResidentInBahamas);

                        // Retrieve the director relation with the entity
                        var relationToEntity = GetValueOrDefault(form, FormKeys.DirectorsRelationToEntity(relevantActivityKey, directorIndex));

                        SetCellValueAndStyle(worksheet, currentRow, 6, relationToEntity);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}