using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents an address in the old database.
    /// </summary>
#pragma warning disable CA1724 // The type name Line conflicts in whole or in part with the namespace name
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class Address
    {
        /// <summary>
        /// Gets or sets the first line of the address.
        /// </summary>
        [BsonElement("address_1")]
        public string Address1 { get; set; }

        /// <summary>
        /// Gets or sets the second line of the address.
        /// </summary>
        [BsonElement("address_2")]
        public string Address2 { get; set; }

        /// <summary>
        /// Gets or sets the city.
        /// </summary>
        [BsonElement("city")]
        public string City { get; set; }

        /// <summary>
        /// Gets or sets the ZIP code.
        /// </summary>
        [BsonElement("zip")]
        public string Zip { get; set; }

        /// <summary>
        /// Gets or sets the country.
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the address is in St. Kitts.
        /// </summary>
        [BsonElement("is_stKiss")]
        public bool IsStKitts { get; set; }

        /// <summary>
        /// Gets or sets the company classification.
        /// </summary>
        [BsonElement("companyClassification")]
        public string CompanyClassification { get; set; }

        /// <summary>
        /// Gets or sets the St. Kitts address information.
        /// </summary>
        [BsonElement("address_stKiss")]
        public AddressStKitts AddressStKitts { get; set; }
    }
#pragma warning restore CA1724 // The type name Line conflicts in whole or in part with the namespace name

    /// <summary>
    /// Represents a St. Kitts address in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class AddressStKitts
    {
        /// <summary>
        /// Gets or sets the first line of the St. Kitts address.
        /// </summary>
        [BsonElement("address_1")]
        public string Address1 { get; set; }

        /// <summary>
        /// Gets or sets the second line of the St. Kitts address.
        /// </summary>
        [BsonElement("address_2")]
        public string Address2 { get; set; }

        /// <summary>
        /// Gets or sets the city of the St. Kitts address.
        /// </summary>
        [BsonElement("city")]
        public string City { get; set; }

        /// <summary>
        /// Gets or sets the ZIP code of the St. Kitts address.
        /// </summary>
        [BsonElement("zip")]
        public string Zip { get; set; }

        /// <summary>
        /// Gets or sets the country of the St. Kitts address.
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }
    }
}
