using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a client company in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class ClientCompany
    {
        /// <summary>
        /// Gets or sets the name of the contact in the client company.
        /// </summary>
        [BsonElement("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the position of the contact in the client company.
        /// </summary>
        [BsonElement("position")]
        public string Position { get; set; }

        /// <summary>
        /// Gets or sets the first line of the address.
        /// </summary>
        [BsonElement("address_1")]
        public string Address1 { get; set; }

        /// <summary>
        /// Gets or sets the second line of the address.
        /// </summary>
        [BsonElement("address_2")]
        public string Address2 { get; set; }

        /// <summary>
        /// Gets or sets the city.
        /// </summary>
        [BsonElement("city")]
        public string City { get; set; }

        /// <summary>
        /// Gets or sets the ZIP code.
        /// </summary>
        [BsonElement("zip")]
        public string Zip { get; set; }

        /// <summary>
        /// Gets or sets the country.
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the telephone number.
        /// </summary>
        [BsonElement("telephone")]
        public string Telephone { get; set; }

        /// <summary>
        /// Gets or sets the fax number.
        /// </summary>
        [BsonElement("fax")]
        public string Fax { get; set; }

        /// <summary>
        /// Gets or sets the email address.
        /// </summary>
        [BsonElement("email")]
        public string Email { get; set; }
    }
}
