using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Factory
{
    /// <summary>
    /// Factory responsible for creating instances of export managers based on the year.
    /// </summary>
    public class ExportSubmissionsFactory : IExportSubmissionsFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private Dictionary<string, Type> _exporterMappings;

        /// <summary>
        /// Initializes a new instance of the <see cref="ExportSubmissionsFactory"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider used to resolve export manager instances.</param>
        public ExportSubmissionsFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Creates an export manager instance based on the year specified in the request.
        /// </summary>
        /// <param name="request">The export submission request containing the year and other information.</param>
        /// <returns>An instance of <see cref="IExportSubmissionsIRDGenerator"/> for the specified year.</returns>
        /// <exception cref="NotSupportedException">Thrown if the specified year is not supported.</exception>
        public IExportSubmissionsIRDGenerator CreateExportGenerator(ExportSubmissionDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            if (_exporterMappings == null)
            {
                // Initialize exporter mappings dictionary
                _exporterMappings = GenerateExporterMappings();
            }

            // Check if the exporter for the specified year exists in the dictionary
            if (_exporterMappings.TryGetValue(GetIndexName(request).ToUpperInvariant(), out var managerType))
            {
                return (IExportSubmissionsIRDGenerator)_serviceProvider.GetRequiredService(managerType);
            }

            throw new NotSupportedException($"Tax submission export for year {request.FinancialYear} is not supported.");
        }

        private static string GetIndexName(ExportSubmissionDTO request)
        {
            // For now, this approach is fine, but we can make something more elaborated in the future
            return $"{request.FinancialYear}_{request.Module}_{request.Jurisdiction}";
        }

        /// <summary>
        /// Generates the exporter mappings.
        /// </summary>
        /// <returns>A dictionary with the exporter mappings.</returns>
        /// <remarks>
        /// This method is used to generate the exporter mappings at runtime.
        /// </remarks>
        /// <returns>Returns a dictionary with the exporter mappings.</returns>
        private Dictionary<string, Type> GenerateExporterMappings()
        {
            var mappings = new Dictionary<string, Type>(StringComparer.CurrentCultureIgnoreCase);

            // Get all types that implement IExportYearSubmissionsIRDGenerator
            var yearlyExportType = typeof(IExportYearSubmissionsIRDGenerator);
            var interfacesType = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => yearlyExportType.IsAssignableFrom(t) && t.IsInterface && t != yearlyExportType);

            foreach (var interfaceType in interfacesType)
            {
                var yearExporter = (IExportYearSubmissionsIRDGenerator)_serviceProvider.GetRequiredService(interfaceType);
                mappings.Add(yearExporter.GetIndexName(), interfaceType);
            }

            return mappings;
        }
    }
}