﻿// <copyright file="Setting.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.EFAuditing;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;

namespace NetProGroup.Trust.Domain.Settings
{
    /// <summary>
    /// Represents a setting entity in the database.
    /// </summary>
    public class Setting : StampedEntity<Guid>, IAuditableEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Setting"/> class with the specified parameters.
        /// </summary>
        /// <param name="id">The unique identifier for the setting.</param>
        /// <param name="key">The key to reference the setting.</param>
        /// <param name="name">The name of the setting (informational purposes).</param>
        /// <param name="jurisdictionId">Optional id of a jurisdiction.</param>
        /// <param name="masterClientId">Optional id of a masterclient.</param>
        /// <param name="legalEntityId">Optional id of a legal entity (company).</param>
        public Setting(Guid id, string key, string name, Guid? jurisdictionId = null, Guid? masterClientId = null, Guid? legalEntityId = null)
        {
            Check.NotDefaultOrNull<Guid>(id, nameof(id));
            Check.NotNullOrWhiteSpace(key, nameof(key));
            Check.NotNullOrWhiteSpace(name, nameof(name));

            Id = id;
            Key = key;
            Name = name;

            JurisdictionId = jurisdictionId;
            MasterClientId = masterClientId;
            LegalEntityId = legalEntityId;

            ValidateReference();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Setting"/> class.
        /// Private constructor to restrict instantiation.
        /// </summary>
        private Setting()
        {
        }

        /// <summary>
        /// Gets or sets the key of the setting so it can be referenced textual.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the name of the setting for informational purposes.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the string representation of the value of the setting.
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the id of the jurisdiction in case teh setting is for a whole jurisdiction.
        /// </summary>
        public Guid? JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the Jurisdiction.
        /// </summary>
        public virtual Jurisdiction Jurisdiction { get; set; }

        /// <summary>
        /// Gets or sets the id of the masterclient in case the setting is for a whole masterclient.
        /// </summary>
        public Guid? MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the MasterClient.
        /// </summary>
        public virtual MasterClient MasterClient { get; set; }

        /// <summary>
        /// Gets or sets the id of the company in case the setting is for a single company.
        /// </summary>
        public Guid? LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the LegalEntity.
        /// </summary>
        public virtual LegalEntity LegalEntity { get; set; }

        /// <summary>
        /// Validates that only a single reference is set.
        /// </summary>
        /// <exception cref="ArgumentException">Throws an argument exception when the count is 1, this happens when only one ID value is set.</exception>
        public void ValidateReference()
        {
            var count = 0;
            if (JurisdictionId.HasValue)
            {
                count++;
            }

            if (MasterClientId.HasValue)
            {
                count++;
            }

            if (LegalEntityId.HasValue)
            {
                count++;
            }

            if (count != 1)
            {
                throw new ArgumentException("Only a single reference is allowed/required");
            }
        }
    }
}
