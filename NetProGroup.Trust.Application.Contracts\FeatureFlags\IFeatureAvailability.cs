﻿// <copyright file="IFeatureAvailability.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.FeatureFlags.Enum;

namespace NetProGroup.Trust.Application.Announcements
{
    /// <summary>
    /// Interface for checking feature availability based on feature flags.
    /// </summary>
    public interface IFeatureAvailability : ISingletonService
    {
        /// <summary>
        /// Checks if a specific feature is enabled based on the feature flags.
        /// </summary>
        /// <param name="flag">The feature flag to check.</param>
        /// <returns>bool.</returns>
        bool IsFeatureEnabled(FeatureFlag flag);
    }
}