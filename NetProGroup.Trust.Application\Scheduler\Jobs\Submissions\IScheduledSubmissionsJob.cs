﻿// <copyright file="IScheduledSubmissionsJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Submissions
{
    /// <summary>
    /// Scheduled job submitting scheduled submissions.
    /// </summary>
    public interface IScheduledSubmissionsJob : ICronJob, ITransientService;
}