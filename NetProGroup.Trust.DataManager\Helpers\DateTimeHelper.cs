﻿// <copyright file="DateTimeHelper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DataManager.Helpers
{
    /// <summary>
    /// Helper methods for DateTime.
    /// </summary>
    public static class DateTimeHelper
    {
        /// <summary>
        /// Checks if 2 periods have an overlap.
        /// </summary>
        /// <param name="start1">Start of the first period.</param>
        /// <param name="end1">End of the first period.</param>
        /// <param name="start2">Start of the second period.</param>
        /// <param name="end2">End of the second period.</param>
        /// <param name="includeBoundaries">Denotes whether there is an overlap if a startdat matches an enddate.</param>
        /// <returns>True if there is an overlap.</returns>
        public static bool HasOverlap(DateTime start1, DateTime end1, DateTime start2, DateTime end2, bool includeBoundaries = true)
        {
            if (includeBoundaries)
            {
                return start1 <= end2 && start2 <= end1;
            }
            else
            {
                return start1 < end2 && start2 < end1;
            }
        }
    }
}
