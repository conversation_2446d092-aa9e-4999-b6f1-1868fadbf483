using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents an entry in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class Entry : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the legacy company code.
        /// </summary>
        [BsonElement("company")]
        public string Company { get; set; }

        /// <summary>
        /// Gets or sets the company code.
        /// </summary>
        [BsonElement("code")]
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the status of the entry (e.g., "SUBMITTED", "PAID").
        /// </summary>
        [BsonElement("status")]
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the period year for the entry.
        /// </summary>
        [BsonElement("period_year")]
        public string PeriodYear { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to use new branding.
        /// </summary>
        [BsonElement("use_new_branding")]
        public bool? UseNewBranding { get; set; }

        /// <summary>
        /// Gets or sets the version of the entry.
        /// </summary>
        [BsonElement("version")]
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the entry.
        /// </summary>
        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last updated date of the entry.
        /// </summary>
        [BsonElement("updatedAt")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets or sets the user who created the entry.
        /// </summary>
        [BsonElement("created_by")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the user who submitted the entry.
        /// </summary>
        [BsonElement("submitted_by")]
        public string SubmittedBy { get; set; }

        /// <summary>
        /// Gets or sets the submission date of the entry.
        /// </summary>
        [BsonElement("submitted_at")]
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the submission date of the entry.
        /// </summary>
        [BsonElement("initial_submit_date")]
        public DateTime? InitialSubmitDate { get; set; }

        /// <summary>
        /// Gets or sets the export date of the entry.
        /// </summary>
        [BsonElement("exported_at")]
        public DateTime? ExportedAt { get; set; }

        /// <summary>
        /// Gets or sets the user who exported the entry.
        /// </summary>
        [BsonElement("exported_by")]
        public string ExportedBy { get; set; }

        /// <summary>
        /// Gets or sets the invoice number.
        /// </summary>
        [BsonElement("invoice_number")]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Gets or sets the invoice export date.
        /// </summary>
        [BsonElement("invoice_export_date")]
        public DateTime? InvoiceExportDate { get; set; }

        /// <summary>
        /// Gets or sets the company data.
        /// </summary>
        [BsonElement("company_data")]
        public CompanyData CompanyData { get; set; }

        /// <summary>
        /// Gets or sets the entity details.
        /// </summary>
        [BsonElement("entity_details")]
        public EntityDetailsSchema EntityDetails { get; set; }

        /// <summary>
        /// Gets or sets the financial_period_details.
        /// </summary>
        [BsonElement("financial_period_details")]
        public FinancialPeriodDetailsSchema FinancialPeriodDetails { get; set; }

        /// <summary>
        /// Gets or sets the tax_residency.
        /// </summary>
        [BsonElement("tax_residency")]
        public TaxResidencySchema TaxResidency { get; set; }

        /// <summary>
        /// Gets or sets the relevant_activities.
        /// </summary>
        [BsonElement("relevant_activities")]
        public RelevantActivitiesSchema RelevantActivities { get; set; }

        /// <summary>
        /// Gets or sets the banking business.
        /// </summary>
        [BsonElement("banking_business")]
        public BusinessSchema BankingBusiness { get; set; }

        /// <summary>
        /// Gets or sets the insurance business.
        /// </summary>
        [BsonElement("insurance_business")]
        public BusinessSchema InsuranceBusiness { get; set; }

        /// <summary>
        /// Gets or sets the fund management business.
        /// </summary>
        [BsonElement("fund_management_business")]
        public BusinessSchema FundManagementBusiness { get; set; }

        /// <summary>
        /// Gets or sets the finance leasing business.
        /// </summary>
        [BsonElement("finance_leasing_business")]
        public BusinessSchema FinanceLeasingBusiness { get; set; }

        /// <summary>
        /// Gets or sets the headquarters business.
        /// </summary>
        [BsonElement("headquarters_business")]
        public BusinessSchema HeadquartersBusiness { get; set; }

        /// <summary>
        /// Gets or sets the shipping business.
        /// </summary>
        [BsonElement("shipping_business")]
        public BusinessSchema ShippingBusiness { get; set; }

        /// <summary>
        /// Gets or sets the holding business.
        /// </summary>
        [BsonElement("holding_business")]
        public BusinessSchema HoldingBusiness { get; set; }

        /// <summary>
        /// Gets or sets the intellectual property business.
        /// </summary>
        [BsonElement("intellectual_property_business")]
        public BusinessSchema IntellectualPropertyBusiness { get; set; }

        /// <summary>
        /// Gets or sets the service centre business.
        /// </summary>
        [BsonElement("service_centre_business")]
        public BusinessSchema ServiceCentreBusiness { get; set; }

        /// <summary>
        /// Gets or sets the supporting details information.
        /// </summary>
        [BsonElement("supporting_details")]
        public SupportingDetailsSchema SupportingDetails { get; set; }

        /// <summary>
        /// Gets or sets the confirmation information.
        /// </summary>
        [BsonElement("confirmation")]
        public ConfirmationSchema Confirmation { get; set; }

        /// <summary>
        /// Gets or sets the payment information.
        /// </summary>
        [BsonElement("payment")]
        public PaymentSchema Payment { get; set; }

        /// <summary>
        /// Gets or sets the reopened information.
        /// </summary>
        [BsonElement("reopened")]
        public DetailsSchema<ReopenedSchema> Reopened { get; set; }

        /// <summary>
        /// Gets or sets the requested information.
        /// </summary>
        [BsonElement("requested_information")]
        public DetailsSchema<RequestedInformationSchema> RequestedInformation { get; set; }

        /// <summary>
        /// Gets or sets the requested information.
        /// </summary>
        [BsonElement("client_returned_information")]
        public DetailsSchema<ClientReturnedInformationSchema> ClientReturnedInformation { get; set; }

        /// <summary>
        /// Gets or sets the financial_period_changes.
        /// </summary>
        [BsonElement("financial_period_changes")]
        public DetailsSchema<FinancialPeriodChangesSchema> FinancialPeriodChanges { get; set; }
    }
}