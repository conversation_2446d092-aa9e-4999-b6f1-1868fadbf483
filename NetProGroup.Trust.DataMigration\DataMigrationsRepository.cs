﻿// <copyright file="DataMigrationsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Repository;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Repository for DataMigrations.
    /// </summary>
    public class DataMigrationsRepository : RepositoryBase<TrustDbContext, Domain.DataMigrations.DataMigration, Guid>, IDataMigrationsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DataMigrationsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public DataMigrationsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IDataMigrationsRepository.DbContext => base.DbContext;
    }
}
