﻿// <copyright file="ScheduledJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Tools;
using System.Text.Json;

namespace NetProGroup.Trust.Domain.Scheduling
{
    /// <summary>
    /// Represents a ScheduledJob entity in the database.
    /// </summary>
    public class ScheduledJob : Entity<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduledJob"/> class with the specified parameters.
        /// </summary>
        /// <param name="id">The unique identifier for the ScheduledJob.</param>
        /// <param name="key">The key to reference the ScheduledJob.</param>
        /// <param name="name">The name of the ScheduledJob.</param>
        public ScheduledJob(Guid id, string key, string name)
        {
            Check.NotDefaultOrNull<Guid>(id, nameof(id));
            Check.NotNullOrWhiteSpace(key, nameof(key));
            Check.NotNullOrWhiteSpace(name, nameof(name));
            Id = id;
            Key = key;
            Name = name;

            IsActive = false;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduledJob"/> class.
        /// Private constructor to restrict instantiation.
        /// </summary>
        private ScheduledJob()
        {
        }

        /// <summary>
        /// Gets or sets the name of the scheduled job.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the key of the scheduled job so it can be referenced textual.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the scheduled job is active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the scheduled job must run on next scan.
        /// </summary>
        public bool Trigger { get; set; }

        /// <summary>
        /// Gets or sets data for the job.
        /// </summary>
#pragma warning disable CA1721 // Property names should not match get methods
        public string Data { get; protected set; }
#pragma warning restore CA1721 // Property names should not match get methods

        /// <summary>
        /// Gets or sets state data for the job.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the date/time that the job was last run.
        /// </summary>
        public DateTime? LastRunAt { get; set; }

        /// <summary>
        /// Deserializes the data for the job.
        /// </summary>
        /// <typeparam name="TData">Target type to conver to.</typeparam>
        /// <returns>Default or a typed representation of this.Data.</returns>
        public TData GetData<TData>()
        {
            return string.IsNullOrEmpty(Data) ? default : JsonSerializer.Deserialize<TData>(Data);
        }

        /// <summary>
        /// Sets the data for the job.
        /// </summary>
        /// <typeparam name="TData">Typed object to serialize to JSON.</typeparam>
        /// <param name="data">The data to serialize.</param>
        public void SetData<TData>(TData data)
        {
            Data = JsonSerializer.Serialize(data);
        }
    }
}
