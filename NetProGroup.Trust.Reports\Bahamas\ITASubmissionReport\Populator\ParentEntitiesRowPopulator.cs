// <copyright file="ParentEntitiesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class ParentEntitiesRowPopulator : LinePopulatorBase, IParentEntitiesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the tax payer id data
            var taxPayerIdData = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification).ToList();

            // Retrieve the created inmediate parent entities
            var inmediateParentEntities = taxPayerIdData.GetAttributesWithKey(WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities).ToList();

            // Group the providers
            var inmediateParentEntityGroups = inmediateParentEntities.GroupBy(
                a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification + WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities)[1].Split(".")[0]);

            foreach (var inmediateParentEntityGroup in inmediateParentEntityGroups)
            {
                // Retrieve the entity unique id
                var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                // Retrieve the financial period end date
                var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                SetCellValueAndStyle(worksheet, currentRow, 2, financialPeriodEndDate);

                SetCellValueAndStyle(worksheet, currentRow, 3, "Inmediate");

                // Retrieve the entity name
                var entityName = inmediateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Name);

                SetCellValueAndStyle(worksheet, currentRow, 4, entityName);

                // Retrieve the alternative name
                var alternativeName = inmediateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AlternativeName);

                SetCellValueAndStyle(worksheet, currentRow, 5, alternativeName);

                // Retrieve the jurisdiction
                var jurisdiction = inmediateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.JurisdictionOfFormation);

                SetCellValueAndStyle(worksheet, currentRow, 6, jurisdiction);

                // Retrieve the incorporation number
                var incorporationNumber = inmediateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.IncorporationNumber);

                SetCellValueAndStyle(worksheet, currentRow, 7, incorporationNumber);

                // Retrieve the tax payer id number
                var taxpayerIdentificationNumber = inmediateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TaxpayerIdentificationNumber);

                SetCellValueAndStyle(worksheet, currentRow, 8, taxpayerIdentificationNumber);

                currentRow += 1;
            }

            // Retrieve the created ultimate parent entities
            var ultimateParentEntities = taxPayerIdData.GetAttributesWithKey(WellKnownFormDocumentAttibuteKeys.UltimateParentEntities).ToList();

            // Group the providers
            var ultimateParentEntityGroups = ultimateParentEntities.GroupBy(
                a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification + WellKnownFormDocumentAttibuteKeys.UltimateParentEntities)[1].Split(".")[0]);

            foreach (var ultimateParentEntityGroup in ultimateParentEntityGroups)
            {
                // Retrieve the entity unique id
                var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                // Retrieve the financial period end date
                var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                SetCellValueAndStyle(worksheet, currentRow, 2, financialPeriodEndDate);

                SetCellValueAndStyle(worksheet, currentRow, 3, "Ultimate");

                // Retrieve the entity name
                var entityName = ultimateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Name);

                SetCellValueAndStyle(worksheet, currentRow, 4, entityName);

                // Retrieve the alternative name
                var alternativeName = ultimateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AlternativeName);

                SetCellValueAndStyle(worksheet, currentRow, 5, alternativeName);

                // Retrieve the jurisdiction
                var jurisdiction = ultimateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.JurisdictionOfFormation);

                SetCellValueAndStyle(worksheet, currentRow, 6, jurisdiction);

                // Retrieve the incorporation number
                var incorporationNumber = ultimateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.IncorporationNumber);

                SetCellValueAndStyle(worksheet, currentRow, 7, incorporationNumber);

                // Retrieve the tax payer id number
                var taxpayerIdentificationNumber = ultimateParentEntityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TaxpayerIdentificationNumber);

                SetCellValueAndStyle(worksheet, currentRow, 8, taxpayerIdentificationNumber);

                currentRow += 1;
            }
        }
    }
}