// <copyright file="ParentEntitiesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class ParentEntitiesRowPopulator : LinePopulatorBase, IParentEntitiesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;

            // Get immediate parent entity indexes
            var immediateParentEntityIndexes = GetImmediateParentEntityIndexes(form!.DataSet);

            List<int> GetImmediateParentEntityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = $@"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification.Replace(".", "\\.", StringComparison.Ordinal)}{WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities.Replace(".", "\\.", StringComparison.Ordinal)}(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var immediateParentEntityIndex in immediateParentEntityIndexes)
            {
                // Retrieve the entity unique id
                SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                // Retrieve the financial period end date
                SetCellValueAndStyle(worksheet, currentRow, 2, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                SetCellValueAndStyle(worksheet, currentRow, 3, "Immediate");

                // Retrieve the entity name
                var entityName = GetValueOrDefault(form, FormKeys.ImmediateParentEntitiesName(immediateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 4, entityName);

                // Retrieve the alternative name
                var alternativeName = GetValueOrDefault(form, FormKeys.ImmediateParentEntitiesAlternativeName(immediateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 5, alternativeName);

                // Retrieve the jurisdiction
                var jurisdiction = GetValueOrDefault(form, FormKeys.ImmediateParentEntitiesJurisdictionOfFormation(immediateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 6, jurisdiction);

                // Retrieve the incorporation number
                var incorporationNumber = GetValueOrDefault(form, FormKeys.ImmediateParentEntitiesIncorporationNumber(immediateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 7, incorporationNumber);

                // Retrieve the tax payer id number
                var taxpayerIdentificationNumber = GetValueOrDefault(form, FormKeys.ImmediateParentEntitiesTaxpayerIdentificationNumber(immediateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 8, taxpayerIdentificationNumber);

                currentRow += 1;
            }

            // Get ultimate parent entity indexes
            var ultimateParentEntityIndexes = GetUltimateParentEntityIndexes(form!.DataSet);

            List<int> GetUltimateParentEntityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = $@"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification.Replace(".", "\\.", StringComparison.Ordinal)}{WellKnownFormDocumentAttibuteKeys.UltimateParentEntities.Replace(".", "\\.", StringComparison.Ordinal)}(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            foreach (var ultimateParentEntityIndex in ultimateParentEntityIndexes)
            {
                // Retrieve the entity unique id
                SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

                // Retrieve the financial period end date
                SetCellValueAndStyle(worksheet, currentRow, 2, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

                SetCellValueAndStyle(worksheet, currentRow, 3, "Ultimate");

                // Retrieve the entity name
                var entityName = GetValueOrDefault(form, FormKeys.UltimateParentEntitiesName(ultimateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 4, entityName);

                // Retrieve the alternative name
                var alternativeName = GetValueOrDefault(form, FormKeys.UltimateParentEntitiesAlternativeName(ultimateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 5, alternativeName);

                // Retrieve the jurisdiction
                var jurisdiction = GetValueOrDefault(form, FormKeys.UltimateParentEntitiesJurisdictionOfFormation(ultimateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 6, jurisdiction);

                // Retrieve the incorporation number
                var incorporationNumber = GetValueOrDefault(form, FormKeys.UltimateParentEntitiesIncorporationNumber(ultimateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 7, incorporationNumber);

                // Retrieve the tax payer id number
                var taxpayerIdentificationNumber = GetValueOrDefault(form, FormKeys.UltimateParentEntitiesTaxpayerIdentificationNumber(ultimateParentEntityIndex));

                SetCellValueAndStyle(worksheet, currentRow, 8, taxpayerIdentificationNumber);

                currentRow += 1;
            }
        }
    }
}