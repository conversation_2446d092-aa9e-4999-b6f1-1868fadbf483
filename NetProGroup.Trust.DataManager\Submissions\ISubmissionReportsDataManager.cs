﻿// <copyright file="ISubmissionReportsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Interface for the SubmissionReportsDataManager.
    /// </summary>
    /// <remarks>
    /// This exposes only methods for reporting on submissions.
    /// </remarks>
    public interface ISubmissionReportsDataManager : IScopedService
    {
        /// <summary>
        /// Gets the submissions to export to IRD (Nevis).
        /// </summary>
        /// <remarks>
        /// The submissions should be of the same financial year.
        /// </remarks>
        /// <param name="submissionIds">The list of submission ids to export.</param>
        /// <param name="financialYear">The financial year to export for.</param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result is an enumerable collection of <see cref="Submission"/>
        /// objects to be included in the financial report export.
        /// </returns>
        Task<IEnumerable<Submission>> GetSubmissionForNevisIRDReportExportAsync(IEnumerable<Guid> submissionIds, int financialYear);

        /// <summary>
        /// Asynchronously retrieves a list of invoice submissions for use in financial report exports (Nevis).
        /// </summary>
        /// <remarks>
        /// This method is intended to fetch all invoice submissions required for generating or exporting financial reports.
        /// The submissions may include detailed invoice data to support reporting needs.
        /// </remarks>
        /// <returns>
        /// A task representing the asynchronous operation. The task result is an enumerable collection of <see cref="Submission"/>
        /// objects to be included in the financial report export.
        /// </returns>
        Task<IEnumerable<Submission>> GetSubmissionsForNevisFinancialReportExport();

        /// <summary>
        /// Asynchronously retrieves a list of invoice submissions for use in basic financial report exports (Panama).
        /// </summary>
        /// <remarks>
        /// This method is intended to fetch all invoice submissions required for generating or exporting financial reports.
        /// It'll select the submissions in which the client has selected to use the accounting records tool.
        /// The submissions may include detailed invoice data to support reporting needs.
        /// </remarks>
        /// <returns>
        /// A task representing the asynchronous operation. The task result is an enumerable collection of <see cref="Submission"/>
        /// objects to be included in the financial report export.
        /// </returns>
        Task<IEnumerable<Submission>> GetSubmissionsForPanamaBasicFinancialReportExport();

        /// <summary>
        /// Asynchronously retrieves a list of submissions for report exports (Bahamas).
        /// </summary>
        /// <remarks>
        /// This method is intended to fetch all submissions required for generating or exporting submissions.
        /// It'll select the submissions in which the client has selected to use the accounting records tool.
        /// The submissions may include detailed invoice data to support reporting needs.
        /// </remarks>
        /// <returns>
        /// A task representing the asynchronous operation. The task result is an enumerable collection of <see cref="Submission"/>
        /// objects to be included in the financial report export.
        /// </returns>
        Task<IEnumerable<Submission>> GetSubmissionsForBahamasExport();

        /// <summary>
        /// Completes the financial export for the given submissions.
        /// </summary>
        /// <param name="submissions">The submissions to complete.</param>
        /// <param name="reportId">The id of the report to link to.</param>
        /// <returns>The completed submissions.</returns>
        Task CompleteFinancialExport(IEnumerable<Submission> submissions, Guid reportId);

        /// <summary>
        /// Gets the list of years that can be selected for new submission.
        /// </summary>
        /// <param name="request">The request with parameters.</param>
        /// <returns>AllSubmissionYearsDTO.</returns>
        Task<AllSubmissionYearsDTO> GetAllSubmissionYears(AllSubmissionYearsRequest request);
    }
}