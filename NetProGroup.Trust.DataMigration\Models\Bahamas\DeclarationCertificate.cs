using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a declaration certificate in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class DeclarationCertificate
    {
        /// <summary>
        /// Gets or sets a value indicating whether the first declaration check is marked.
        /// </summary>
        [BsonElement("is_check_1")]
        public bool IsCheck1 { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the second declaration check is marked.
        /// </summary>
        [BsonElement("is_check_2")]
        public bool IsCheck2 { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the third declaration check is marked.
        /// </summary>
        [BsonElement("is_check_3")]
        public bool IsCheck3 { get; set; }

        /// <summary>
        /// Gets or sets the date of the declaration.
        /// </summary>
        [BsonElement("date")]
        public DateTime Date { get; set; }

        /// <summary>
        /// Gets or sets the first line of the address.
        /// </summary>
        [BsonElement("address1")]
        public string Address1 { get; set; }

        /// <summary>
        /// Gets or sets the second line of the address.
        /// </summary>
        [BsonElement("address2")]
        public string Address2 { get; set; }

        /// <summary>
        /// Gets or sets the city.
        /// </summary>
        [BsonElement("city")]
        public string City { get; set; }

        /// <summary>
        /// Gets or sets the ZIP code.
        /// </summary>
        [BsonElement("zip")]
        public string Zip { get; set; }

        /// <summary>
        /// Gets or sets the country.
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the relation of the declarant to the entity.
        /// </summary>
        [BsonElement("relation")]
        public string Relation { get; set; }

        /// <summary>
        /// Gets or sets the name of the person making the declaration.
        /// </summary>
        [BsonElement("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the declaration is made on the declarant's own behalf.
        /// </summary>
        [BsonElement("ownBehalf")]
        public string OwnBehalf { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the declarant is an officer of the entity.
        /// </summary>
        [BsonElement("officer")]
        public string Officer { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the declarant is an attorney for the entity.
        /// </summary>
        [BsonElement("attorney")]
        public string Attorney { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the declarant is a trustee of the entity.
        /// </summary>
        [BsonElement("trustee")]
        public string Trustee { get; set; }

        /// <summary>
        /// Gets or sets the specific entity on whose behalf the declaration is made, if applicable.
        /// </summary>
        [BsonElement("entity_specified")]
        public string EntitySpecified { get; set; }
    }
}
