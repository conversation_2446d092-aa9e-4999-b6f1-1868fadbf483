﻿// <copyright file="FormKeys.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Domain.Shared.Settings
{
    /// <summary>
    /// Represents the various keys for forms.
    /// </summary>
    public static class FormKeys
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented
#pragma warning disable SA1202 // Elements should be ordered by access
#pragma warning disable CA1034 // Nested types should not be visible
#pragma warning disable SA1201 // Elements should appear in the correct order

        // Prior submission
        private const string PriorSubmissionPrefix = "prior-submission";
        public const string PriorSubmissionExists = $"{PriorSubmissionPrefix}.exists";
        public const string PriorSubmissionFinancialPeriodEndDate = $"{PriorSubmissionPrefix}.financial-period.enddate";

        // ITA keys
        private const string ITAPrefix = "ita";
        public const string ITAHasDates = $"{ITAPrefix}.hasdate";
        public const string ITABefore2019 = $"{ITAPrefix}.before-2019";
        public const string ITAApprovedStartDate = $"{ITAPrefix}.approved-start-date";
        public const string ITAApprovedEndDate = $"{ITAPrefix}.approved-end-date";

        // Legal Entity Keys
        private const string LegalEntityPrefix = "legal-entity-data";
        public const string CompanyName = $"{LegalEntityPrefix}.name";
        public const string CompanyCode = $"{LegalEntityPrefix}.code";
        public const string CompanyAddress = $"{LegalEntityPrefix}.address";
        public const string CompanyIncorporationNumber = $"{LegalEntityPrefix}.incorporationNr";
        public const string CompanyIncorporationDate = $"{LegalEntityPrefix}.incorporationdate";
        public const string CompanyMasterClientCode = $"{LegalEntityPrefix}.masterClientCode";
        public const string CompanyReferralOffice = $"{LegalEntityPrefix}.referralOffice";
        public const string CompanyStrSubmissionFee = $"{LegalEntityPrefix}.strSubmissionFee";
        public const string CompanyStrSubmissionLatePaymentFeeExempt = $"{LegalEntityPrefix}.strSubmissionLatePaymentFeeExempt";
        public const string CompanyIsActive = $"{LegalEntityPrefix}.isActive";

        public const string EntityDetailsTin = "entity-details.entityTin";
        public const string EntityDetailsEntityId = "entity-details.entityId";
        public const string EntityDetailsSameAddress = "entity-details.sameAddress";
        public const string EntityDetailsBusinessAddress = "entity-details.streetNumberNameCity";
        public const string EntityAddress = "entity-details.streetNumberNameCity";
        public const string EntityAptUnit = "entity-details.aptUnit";
        public const string EntityCountry = "entity-details.country";

        public const string TaxPayerIdentificationTotalAnnualGrossCurrency = "tax-payer-identification.entityGrossTotalAnnualIncomeCurrency";
        public const string TaxPayerIdentificationTotalAnnualGross = "tax-payer-identification.entityGrossTotalAnnualIncomeAmount";
        public const string TaxPayerIdentificationNameOfNmeGroup = "tax-payer-identification.mneGroupName";
        public const string TaxPayerIdentificationIsBahamianResident = "tax-payer-identification.isBahamianResident";
        public const string TaxPayerIdentificationIsInvestmentFund = "tax-payer-identification.isInvestmentFund";
        public const string TaxPayerIdentificationIsPartOfMneGroup = "tax-payer-identification.isPartOfMneGroup";
        public const string TaxPayerIdentificationIntendsToClaimTaxResidencyOutsideBahamas = "tax-payer-identification.intendsToClaimTaxResidencyOutsideBahamas";
        public const string TaxPayerIdentificationTaxResidencyJurisdiction = "tax-payer-identification.taxResidencyJurisdiction";
        public const string TaxPayerIdentificationTaxPayerIdentificationNumber = "tax-payer-identification.taxPayerIdentificationNumber";
        public const string TaxPayerIdentificationHasUltimateParentEntity = "tax-payer-identification.hasUltimateParentEntity";
        public const string TaxPayerIdentificationHasImmediateParentEntity = "tax-payer-identification.hasImmediateParentEntity";

        public const string FinancialPeriodDetailsStartDate = "financial-period.startDate";
        public const string FinancialPeriodDetailsEndDate = "financial-period.endDate";
        public const string FinancialPeriodDetailsHasFinancialPeriodChanged = "financial-period.has-financial-perdiod-changed";
        public const string FinancialPeriodDetailsHasReclassification = "financial-period.has-reclasification";
        public const string FinancialPeriodDetailsHasOtas = "financial-period.has-otas";
        public const string FinancialPeriodDetailsHasOtasReceipt = "financial-period.has-otas-receipt";
        public const string FinancialPeriodDetailsFirstFinancialReport = "financial-period.firstFinancialReport";
        public const string FinancialPeriodDetailsIsReclassifiedToPEH = "financial-period.isReclassifiedToPEH";

        // Address of Head Office Keys
        private const string HeadOfficePrefix = "address-of-head-office";
        public const string HeadOfficeAddress1 = $"{HeadOfficePrefix}.address1";
        public const string HeadOfficeAddress2 = $"{HeadOfficePrefix}.address2";
        public const string HeadOfficeCity = $"{HeadOfficePrefix}.city";
        public const string HeadOfficeZipCode = $"{HeadOfficePrefix}.zipCode";
        public const string HeadOfficeCountry = $"{HeadOfficePrefix}.country";
        public const string HeadOfficeIsAddressInNevisDifferent = $"{HeadOfficePrefix}.isAddressInNevisDifferent";
        public const string HeadOfficeCompanyClassification = $"{HeadOfficePrefix}.companyClassification";
        public const string HeadOfficeNevisAddress1 = $"{HeadOfficePrefix}.nevisAddress1";
        public const string HeadOfficeNevisAddress2 = $"{HeadOfficePrefix}.nevisAddress2";
        public const string HeadOfficeNevisCity = $"{HeadOfficePrefix}.nevisCity";
        public const string HeadOfficeNevisZipCode = $"{HeadOfficePrefix}.nevisZipCode";
        public const string HeadOfficeNevisCountry = $"{HeadOfficePrefix}.nevisCountry";

        // Contact Information Keys
        private const string ContactInformationPrefix = "contact-information";
        public const string ContactName = $"{ContactInformationPrefix}.name";
        public const string ContactPosition = $"{ContactInformationPrefix}.position";
        public const string ContactAddress1 = $"{ContactInformationPrefix}.address1";
        public const string ContactAddress2 = $"{ContactInformationPrefix}.address2";
        public const string ContactZipCode = $"{ContactInformationPrefix}.zipCode";
        public const string ContactCountry = $"{ContactInformationPrefix}.country";
        public const string ContactCity = $"{ContactInformationPrefix}.city";
        public const string ContactTelephoneNumber = $"{ContactInformationPrefix}.telephone.number";
        public const string ContactTelephoneCountryCode = $"{ContactInformationPrefix}.telephone.countryCode";
        public const string ContactTelephonePrefix = $"{ContactInformationPrefix}.telephone.prefix";
        public const string ContactFaxNumber = $"{ContactInformationPrefix}.fax.number";
        public const string ContactFaxCountryCode = $"{ContactInformationPrefix}.fax.countryCode";
        public const string ContactFaxPrefix = $"{ContactInformationPrefix}.fax.prefix";
        public const string ContactEmail = $"{ContactInformationPrefix}.email";
        public const string CompanyRepresentativeName = $"{ContactInformationPrefix}.companyRepresentativeName";
        public const string CompanyRepresentativeTelephoneNumber = $"{ContactInformationPrefix}.companyRepresentativeTelephone.number";
        public const string CompanyRepresentativeTelephoneCountryCode = $"{ContactInformationPrefix}.companyRepresentativeTelephone.countryCode";
        public const string CompanyRepresentativeTelephonePrefix = $"{ContactInformationPrefix}.companyRepresentativeTelephone.prefix";
        public const string CompanyRepresentativeFaxNumber = $"{ContactInformationPrefix}.companyRepresentativeFax.number";
        public const string CompanyRepresentativeFaxCountryCode = $"{ContactInformationPrefix}.companyRepresentativeFax.countryCode";
        public const string CompanyRepresentativeFaxPrefix = $"{ContactInformationPrefix}.companyRepresentativeFax.prefix";
        public const string CompanyRepresentativeEmail = $"{ContactInformationPrefix}.companyRepresentativeEmail";

        private const string TaxResidentPrefix = "tax-resident";
        public const string TaxResidentIncorporatedBefore2019 = $"{TaxResidentPrefix}.incorporatedBefore2019";
        public const string TaxResidentNonTaxResident = $"{TaxResidentPrefix}.nonTaxResident";
        public const string TaxResidentResidentCountry = $"{TaxResidentPrefix}.residentCountry";

        public const string TaxResidencyResidentInBha = "tax-payer-identification.isBahamianResident";
        public const string TaxResidencyIsInvestmentFund = "tax-payer-identification.tax-payer-identification.isInvestmentFund";
        public const string TaxResidencyClaimTaxResidencyOutsideBahamas = "tax-payer-identification.tax-payer-identification.intendsToClaimTaxResidencyOutsideBahamas";
        public const string TaxResidencyEntityJurisdiction = "tax-payer-identification.tax-payer-identification.taxResidencyJurisdiction";
        public const string TaxResidencyForeignTaxIdNumber = "tax-payer-identification.tax-payer-identification.tax-payer-identification.taxPayerIdentificationNumber";
        public const string TaxResidencyHasUltimateParents = "tax-payer-identification.tax-payer-identification.hasUltimateParentEntity";
        public const string TaxResidencyHasImmediateParents = "tax-payer-identification.tax-payer-identification.hasImmediateParentEntity";
        public const string TaxResidencyUltimateParents = "tax-payer-identification.ultimateParentEntities";
        public const string TaxResidencyImmediateParents = "tax-payer-identification.immediateParentEntities";

        public const string RelevantActivitiesRelevantActivityDeclaration = "relevant-activity-declaration";
        public const string RelevantActivitiesPrefix = "relevant-activity-declaration.relevantActivities.";

        public const string BusinessPrefixBanking = "banking-business";
        public const string BusinessPrefixInsurance = "insurance-business";
        public const string BusinessPrefixFundmanagement = "fund-management-business";
        public const string BusinessPrefixFinancialLeasing = "finance-leasing-business";
        public const string BusinessPrefixHeadquarters = "headquarters-business";
        public const string BusinessPrefixShipping = "shipping-business";
        public const string BusinessPrefixHolding = "holding-business";
        public const string BusinessPrefixIntellectualProperty = "intellectual-property-business";
        public const string BusinessPrefixServiceCentre = "service-centre-business";

        public const string BusinessTotalGrossIncome = "totalGrossIncome";
        public const string BusinessNetBookValuesAssets = "netBookValuesAssets";
        public const string BusinessAssetsDescriptionBahamas = "assetsDescriptionBahamas";
        public const string BusinessIsDirectedAndManagedInBahamas = "isDirectedAndManagedInBahamas";
        public const string BusinessNumberOfMeetings = "numberOfMeetings";
        public const string BusinessNumberOfMeetingsInBahamas = "numberOfMeetingsInBahamas";
        public const string BusinessQuorumDirectors = "quorumDirectors";
        public const string BusinessQuorumPhysicallyPresent = "quorumPhysicallyPresent";
        public const string BusinessAreMinutesKeptInBahamas = "areMinutesKeptInBahamas";
        public const string BusinessTotalExpenditureRelevantActivity = "totalExpenditureRelevantActivity";
        public const string BusinessTotalExpenditureBahamas = "totalExpenditureBahamas";
        public const string BusinessTotalEmployeesEntity = "totalEmployeesEntity";
        public const string BusinessTotalEmployeesRelevantActivity = "totalEmployeesRelevantActivity";
        public const string BusinessTotalEmployeesBahamas = "totalEmployeesBahamas";
        public const string BusinessBahamasPremisesOwnership = "bahamasPremisesOwnership";
        public const string BusinessHasCiga = "hasCiga";
        public const string BusinessIsCigaOutsourced = "isCigaOutsourced";
        public const string BusinessCigaOutsourcingProportion = "cigaOutsourcingProportion";
        public const string BusinessBahamasOutsourcingExpenditure = "bahamasOutsourcingExpenditure";
        public const string BusinessIsCompliantWithBahamasLawsAndRegulations = "isCompliantWithBahamasLawsAndRegulations";
        public const string BusinessIsHighRiskIpEntity = "isHighRiskIpEntity";
        public const string BusinessRelevantIpAsset = "relevantIpAsset";
        public const string BusinessIncomeGenerationExplanation = "incomeGenerationExplanation";
        public const string BusinessEmployeeResponsibility = "employeeResponsibility";
        public const string BusinessStrategicDecisionsBahamas = "strategicDecisionsBahamas";
        public const string BusinessTradingActivitiesBahamas = "tradingActivitiesBahamas";
        public const string BusinessGrossIncomeRoyalties = "grossIncomeRoyalties";
        public const string BusinessGrossIncomeSaleIpAsset = "grossIncomeSaleIpAsset";
        public const string BusinessGrossIncomeOtherSources = "grossIncomeOtherSources";
        public const string BusinessBusinessPlanExplanation = "businessPlanExplanation";
        public const string BusinessDecisionMakingEvidenceExplanation = "decisionMakingEvidenceExplanation";
        public const string BusinessAdditionalComplianceExplanation = "additionalComplianceExplanation";

        // Intellectual Properties Keys
        private const string IntellectualPropertiesPrefix = "intellectual-properties";
        public const string IntellectualPropertiesAcquired = $"{IntellectualPropertiesPrefix}.intellectualPropertyAcquired";
        private const string IntellectualPropertiesAssetsAcquiredPrefix = $"{IntellectualPropertiesPrefix}.assetsAcquired";

        private const string FinancialPeriodPrefix = "financial-period";
        public const string FinancialPeriodStartDate = $"{FinancialPeriodPrefix}.startDate";
        public const string FinancialPeriodEndDate = $"{FinancialPeriodPrefix}.endDate";

        public static string IntellectualPropertiesAssetsAcquiredDate(int index) => $"{IntellectualPropertiesAssetsAcquiredPrefix}.{index}.acquisitionDate";

        public static string IntellectualPropertiesAssetsAcquiredDescription(int index) => $"{IntellectualPropertiesAssetsAcquiredPrefix}.{index}.description";

        public static string IntellectualPropertiesAssetsAcquiredIncome(int index) => $"{IntellectualPropertiesAssetsAcquiredPrefix}.{index}.income";

        // Corporate Accounting Records Keys
        private const string CorporateAccountingPrefix = "corporate-accounting-records";
        public const string CorporateAccountingAssessableIncomeGenerated = $"{CorporateAccountingPrefix}.assessableIncomeGenerated";
        public const string CorporateAccountingActivitiesCondition = $"{CorporateAccountingPrefix}.activitiesCondition";

        public static string CorporateAccountingActivitiesDescription(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.description";

        public static string CorporateAccountingActivitiesIncome(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.income";

        public static string CorporateAccountingActivitiesYearIncome(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.incomeYear";

        public static string CorporateAccountingActivitiesRelatedPartyIntellectualProperty(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.relatedPartyIntellectualProperty";

        public static string CorporateAccountingActivitiesNonRelatedIntellectualProperty(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.nonRelatedIntellectualProperty";

        public static string CorporateAccountingActivitiesNonIntellectualProperty(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.nonIntellectualProperty";

        // Corporate Multinational Enterprise Keys
        private const string CorporateMNEPrefix = "corporate-multinational-enterprise";
        public const string CorporateMNEIsPartOfMNEGroup = $"{CorporateMNEPrefix}.isPartOfMNEGroup";
        public const string CorporateMNERequiresCbCReport = $"{CorporateMNEPrefix}.requiresCbCReport";

        public const string SupportingDetailsComment = "supporting-details.additionalComments";

        // Finalize Keys
        private const string FinalizePrefix = "finalize";
        public const string FinalizeConfirmationTrueInformation = $"{FinalizePrefix}.confirmationTrueInformation";
        public const string FinalizeConfirmationUnderstand = $"{FinalizePrefix}.confirmationUnderstand";
        public const string FinalizeConfirmationAwarePerjury = $"{FinalizePrefix}.confirmationAwarePerjury";
        public const string FinalizeDateOfSignature = $"{FinalizePrefix}.dateOfSignature";
        public const string FinalizeAddressOfPersonDeclaring = $"{FinalizePrefix}.addressOfPersonDeclaring";
        public const string FinalizeAddressOfPersonDeclaring2 = $"{FinalizePrefix}.addressOfPersonDeclaring2";
        public const string FinalizeCity = $"{FinalizePrefix}.city";
        public const string FinalizeZipCode = $"{FinalizePrefix}.zipCode";
        public const string FinalizeCountry = $"{FinalizePrefix}.country";
        public const string FinalizeNameOfPersonDeclaring = $"{FinalizePrefix}.nameOfPersonDeclaring";
        public const string FinalizeOnMyOwnBehalf = $"{FinalizePrefix}.onMyOwnBehalf";
        public const string FinalizeAsOfficer = $"{FinalizePrefix}.asOfficer";
        public const string FinalizeAsAttorney = $"{FinalizePrefix}.asAttorney";
        public const string FinalizeAsTrustee = $"{FinalizePrefix}.asTrustee";
        public const string FinalizeConfirmationDeclaration = $"{FinalizePrefix}.confirmationDeclaration";
        public const string FinalizeAuthorityToActOnBehalf = $"{FinalizePrefix}.authorityToActOnBehalf";
        public const string FinalizeLegitimateInterestForProcessing = $"{FinalizePrefix}.legitimateInterestForProcessing";
        public const string FinalizeAcknowledgeSubmissionFee = $"{FinalizePrefix}.acknowledgeSubmissionFee";
        public const string FinalizeDeclarantName = $"{FinalizePrefix}.declarantName";
        public const string FinalizeUserPhoneNumber = $"{FinalizePrefix}.telephone.number";
        public const string FinalizeEntityRelation = $"{FinalizePrefix}.entityRelation";
        public const string FinalizeOtherEntityRelation = $"{FinalizePrefix}.otherEntityRelation";

        // Supporting Details Keys
        private const string SupportingDetailsPrefix = "supporting-details";
        public const string SupportingDetailsAdditionalComments = $"{SupportingDetailsPrefix}.additionalComments";

        // Corporate Address Keys
        private const string CorporateAddressPrefix = "corporate-address";
        public const string CorporateAddressRecordsKeptAtRegisteredOffice = $"{CorporateAddressPrefix}.recordsKeptAtRegisteredOffice";

        // Business Activities Keys
        private const string BusinessActivitiesPrefix = "business-activities";
        public const string BusinessActivitiesFirstActivityFrom = $"{BusinessActivitiesPrefix}.activities.0.from";
        public const string BusinessActivitiesFirstActivityTo = $"{BusinessActivitiesPrefix}.activities.0.to";
        public const string BusinessActivitiesFirstActivityType = $"{BusinessActivitiesPrefix}.activities.0.type";
        public const string BusinessActivitiesFirstActivity = $"{BusinessActivitiesPrefix}.activities.0.activity";

        public const string RelevantActivitiesRegexPrefix = @"relevant-activity-declaration\.relevantActivities";

        public static string RelevantActivitiesIsSelected(int index) => $"{WellKnownFormDocumentAttibuteKeys.RelevantActivities}{index}{WellKnownFormDocumentAttibuteKeys.Selected}";

        public static string RelevantActivitiesLabel(int index) => $"{WellKnownFormDocumentAttibuteKeys.RelevantActivities}{index}{WellKnownFormDocumentAttibuteKeys.Label}";

        public static string RelevantActivitiesIsPartialFinancialPeriod(int index) => $"{WellKnownFormDocumentAttibuteKeys.RelevantActivities}{index}{WellKnownFormDocumentAttibuteKeys.CarriedOnForOnlyPartOfFinancialPeriod}";

        public static string RelevantActivitiesStartDate(int index) => $"{WellKnownFormDocumentAttibuteKeys.RelevantActivities}{index}{WellKnownFormDocumentAttibuteKeys.StartDate}";

        public static string RelevantActivitiesEndDate(int index) => $"{WellKnownFormDocumentAttibuteKeys.RelevantActivities}{index}{WellKnownFormDocumentAttibuteKeys.EndDate}";

        public static string BusinessActivitiesType(int index) => $"{BusinessActivitiesPrefix}.activities.{index}.type";

        public static string BusinessActivitiesActivity(int index) => $"{BusinessActivitiesPrefix}.activities.{index}.activity";

        public static string BusinessActivitiesOtherActivity(int index) => $"{BusinessActivitiesPrefix}.activities.{index}.otherActivity";

        public static string RelevantActivityDetails(string activityKey, string detailKey) => $"{activityKey}{activityKey}.{detailKey}";

        // Parent Entities Keys
        public static string ImmediateParentEntitiesName(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.Name}";

        public static string ImmediateParentEntitiesAlternativeName(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.AlternativeName}";

        public static string ImmediateParentEntitiesJurisdictionOfFormation(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.JurisdictionOfFormation}";

        public static string ImmediateParentEntitiesIncorporationNumber(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.IncorporationNumber}";

        public static string ImmediateParentEntitiesTaxpayerIdentificationNumber(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.ImmediateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.TaxpayerIdentificationNumber}";

        public static string UltimateParentEntitiesName(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.UltimateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.Name}";

        public static string UltimateParentEntitiesAlternativeName(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.UltimateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.AlternativeName}";

        public static string UltimateParentEntitiesJurisdictionOfFormation(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.UltimateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.JurisdictionOfFormation}";

        public static string UltimateParentEntitiesIncorporationNumber(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.UltimateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.IncorporationNumber}";

        public static string UltimateParentEntitiesTaxpayerIdentificationNumber(int index) => $"{WellKnownFormDocumentAttibuteKeys.TaxPayerIdentification}{WellKnownFormDocumentAttibuteKeys.UltimateParentEntities}{index}{WellKnownFormDocumentAttibuteKeys.TaxpayerIdentificationNumber}";

        // CIGA Activities Keys
        public static string CigaActivitiesLabel(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.CigaActivity}{index}{WellKnownFormDocumentAttibuteKeys.Label}";

        public static string CigaActivitiesDescription(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.CigaActivity}{index}{WellKnownFormDocumentAttibuteKeys.Description}";

        // Directors Keys
        public static string DirectorsName(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Directors}{index}{WellKnownFormDocumentAttibuteKeys.Name}";

        public static string DirectorsIsResidentInBahamas(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Directors}{index}{WellKnownFormDocumentAttibuteKeys.IsResidentInBahamas}";

        public static string DirectorsRelationToEntity(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Directors}{index}{WellKnownFormDocumentAttibuteKeys.RelationToEntity}";

        public static string DirectorsQualification(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Directors}{index}{WellKnownFormDocumentAttibuteKeys.Qualification}";

        public static string DirectorsYearsOfExperience(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Directors}{index}{WellKnownFormDocumentAttibuteKeys.YearsOfExperience}";

        public static string DirectorsPhysicallyPresentInBahamas(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Directors}{index}{WellKnownFormDocumentAttibuteKeys.PhysicallyPresentInBahamas}";

        // Employees Keys
        public static string EmployeesFullname(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Employees}{index}{WellKnownFormDocumentAttibuteKeys.Fullname}";

        public static string EmployeesQualification(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Employees}{index}{WellKnownFormDocumentAttibuteKeys.Qualification}";

        public static string EmployeesYearsOfExperience(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Employees}{index}{WellKnownFormDocumentAttibuteKeys.YearsOfExperience}";

        public static string EmployeesContractType(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Employees}{index}{WellKnownFormDocumentAttibuteKeys.ContractType}";

        // Outsourcing Providers Keys
        public static string OutsourcingProvidersEntityName(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.OutsourcingProviders}{index}{WellKnownFormDocumentAttibuteKeys.EntityName}";

        public static string OutsourcingProvidersDetailsOfResources(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.OutsourcingProviders}{index}{WellKnownFormDocumentAttibuteKeys.DetailsOfResources}";

        public static string OutsourcingProvidersNumberOfStaff(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.OutsourcingProviders}{index}{WellKnownFormDocumentAttibuteKeys.NumberOfStaff}";

        public static string OutsourcingProvidersMonitoringAndControl(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.OutsourcingProviders}{index}{WellKnownFormDocumentAttibuteKeys.MonitoringAndControl}";

        public static string OutsourcingProvidersMonitoringControlExplanation(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.OutsourcingProviders}{index}{WellKnownFormDocumentAttibuteKeys.MonitoringControlExplanation}";

        public static string OutsourcingProvidersPhysicalAddress(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.OutsourcingProviders}{index}{WellKnownFormDocumentAttibuteKeys.PhysicalAddress}";

        // Premises Keys
        public static string PremisesAddressLine1(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Premises}{index}{WellKnownFormDocumentAttibuteKeys.AddressLine1}";

        public static string PremisesAddressLine2(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Premises}{index}{WellKnownFormDocumentAttibuteKeys.AddressLine2}";

        public static string PremisesPremiseCountry(string activityKey, int index) => $"{activityKey}{WellKnownFormDocumentAttibuteKeys.Premises}{index}{WellKnownFormDocumentAttibuteKeys.PremiseCountry}";

#pragma warning restore SA1201 // Elements should appear in the correct order
#pragma warning restore CA1034 // Nested types should not be visible
#pragma warning restore SA1202 // Elements should be ordered by access
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}