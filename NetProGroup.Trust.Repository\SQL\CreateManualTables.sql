/****** Object:  Table [dbo].[Staging_PCP_BeneficialOwnersHistory]    Script Date: 2/12/2025 8:04:30 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_BeneficialOwnersHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_BeneficialOwnersHistory]
(
    [UniqueRelationID] [nvarchar](42) NULL,
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [EntityCode] [nvarchar](10) NULL,
    [EntityName] [nvarchar](356) NULL,
    [EntityUniqueNr] [int] NULL,
    [EntityLegacyID] [nvarchar](30) NULL,
    [BOCode] [nvarchar](10) NULL,
    [BOName] [nvarchar](356) NULL,
    [BOUniqueNr] [int] NULL,
    [BOFormerName] [nvarchar](255) NULL,
    [BOFileType] [nvarchar](50) NULL,
    [RelationType] [varchar](16) NULL,
    [BOOwnerType] [nvarchar](255) NULL,
    [BOOwnerTypeCode] [nvarchar](255) NULL,
    [BOFromDate] [datetime] NULL,
    [BOToDate] [datetime] NULL,
    [BOServiceAddress] [nvarchar](max) NULL,
    [BORegisteredAddress] [nvarchar](max) NULL,
    [BOIncorpDateOrDOB] [datetime] NULL,
    [BOIncorpCountryOrBirthCountryCode] [nvarchar](100) NULL,
    [BOIncorpCountryOrBirthCountry] [nvarchar](100) NULL,
    [BOIncorpPlaceOrBirthPlace] [nvarchar](100) NULL,
    [BOIncorpNrOrPassportNr] [nvarchar](100) NULL,
    [BONationality] [nvarchar](35) NULL,
    [BORegisteredCountry] [nvarchar](10) NULL,
    [BOProductionOffice] [nvarchar](10) NULL,
    [BOTIN] [nvarchar](100) NULL,
    [BONameOfRegulator] [nvarchar](255) NULL,
    [BORegulationCountry] [nvarchar](50) NULL,
    [BOStockExchange] [nvarchar](255) NULL,
    [BOStockCode] [nvarchar](100) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Staging_PCP_DirectorsHistory]    Script Date: 3/28/2025 6:40:49 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_DirectorsHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_DirectorsHistory]
(
    [UniqueRelationID] [nvarchar](42) NULL,
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [EntityCode] [nvarchar](10) NULL,
    [EntityName] [nvarchar](356) NULL,
    [EntityUniqueNr] [int] NULL,
    [EntityLegacyID] [nvarchar](30) NULL,
    [DirCode] [nvarchar](10) NULL,
    [DirName] [nvarchar](356) NULL,
    [DirUniqueNr] [int] NULL,
    [DirFormerName] [nvarchar](255) NULL,
    [DirFileType] [nvarchar](50) NULL,
    [RelationType] [nvarchar](150) NULL,
    [DirOfficerType] [nvarchar](100) NULL,
    [DirFromDate] [datetime] NULL,
    [DirToDate] [datetime] NULL,
    [DirStatus] [varchar](9) NULL,
    [DirServiceAddress] [nvarchar](max) NULL,
    [DirRegisteredAddress] [nvarchar](max) NULL,
    [DirIncorpDateOrDOB] [datetime] NULL,
    [DirIncorpCountryOrBirthCountryCode] [nvarchar](10) NULL,
    [DirIncorpCountryOrBirthCountry] [nvarchar](50) NULL,
    [DirIncorpPlaceOrBirthPlace] [nvarchar](100) NULL,
    [DirIncorpNrOrPassportNr] [nvarchar](100) NULL,
    [DirNationality] [nvarchar](35) NULL,
    [DirRegisteredCountry] [nvarchar](10) NULL,
    [DirProductionOffice] [nvarchar](10) NULL,
    [DirTIN] [nvarchar](100) NULL,
    [AlternateToDirCode] [nvarchar](10) NULL,
    [AlternateToDirName] [nvarchar](356) NULL,
    [LicenseeEntityCode] [nvarchar](10) NULL,
    [LicenseeEntityName] [nvarchar](356) NULL,
    [DirectorCapacity] [nvarchar](255) NULL,
    [DirectorID] [nvarchar](100) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Staging_PCP_EntitiesHistory]    Script Date: 2/12/2025 8:04:55 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_EntitiesHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_EntitiesHistory]
(
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [EntityCode] [nvarchar](10) NULL,
    [EntityName] [nvarchar](356) NULL,
    [EntityUniqueNr] [int] NULL,
    [IncorporationNumber] [nvarchar](30) NULL,
    [IncorporationDate] [datetime] NULL,
    [JurisdictionCode] [nvarchar](10) NULL,
    [Jurisdiction] [nvarchar](50) NULL,
    [EntityTypeCode] [nvarchar](10) NULL,
    [EntityType] [nvarchar](50) NULL,
    [EntityLegacyID] [nvarchar](30) NULL,
    [AdministratorCode] [nvarchar](20) NULL,
    [Administrator] [nvarchar](35) NULL,
    [ManagerCode] [nvarchar](20) NULL,
    [Manager] [nvarchar](35) NULL,
    [ReferralOfficeCode] [nvarchar](10) NULL,
    [ReferralOffice] [nvarchar](356) NULL,
    [ProductionOffice] [nvarchar](10) NULL,
    [EntityStatusCode] [nvarchar](4) NULL,
    [EntityStatus] [nvarchar](50) NULL,
    [EntitySubStatusCode] [nvarchar](10) NULL,
    [EntitySubStatus] [nvarchar](50) NULL,
    [RiskGroupCode] [nvarchar](4) NULL,
    [RiskGroup] [nvarchar](50) NULL
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Staging_PCP_MasterClientsHistory]    Script Date: 2/12/2025 8:05:06 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_MasterClientsHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_MasterClientsHistory]
(
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [UserCode] [nvarchar](10) NULL,
    [UserName] [nvarchar](356) NULL,
    [UserPermission] [nvarchar](100) NULL,
    [UserEmailAddress] [nvarchar](250) NULL,
    [MCInfoActivated] [nvarchar](255) NULL,
    [MCInformation] [nvarchar](50) NULL,
    [BOInfoActivated] [nvarchar](255) NULL,
    [BOInformation] [nvarchar](50) NULL,
    [SHInfoActivated] [nvarchar](255) NULL,
    [SHInformation] [nvarchar](50) NULL,
    [DIRInfoActivated] [nvarchar](255) NULL,
    [DIRInformation] [nvarchar](50) NULL
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[TableColumnMapping]    Script Date: 8/18/2025 9:02:26 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableColumnMapping]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[TableColumnMapping](
	[TableName] [nvarchar](128) NULL,
	[ColumnName] [nvarchar](128) NULL
) ON [PRIMARY];
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOFileType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOFormerName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOFromDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOIncorpCountryOrBirthCountry');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOIncorpCountryOrBirthCountryCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOIncorpDateOrDOB');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOIncorpNrOrPassportNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOIncorpPlaceOrBirthPlace');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BONameOfRegulator');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BONationality');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOOwnerType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOOwnerTypeCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOProductionOffice');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BORegisteredAddress');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BORegisteredCountry');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BORegulationCountry');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOServiceAddress');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOStockCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOStockExchange');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOTIN');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOToDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'BOUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'ClientCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'ClientName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'ClientUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'EntityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'EntityLegacyID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'EntityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'EntityUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'RelationType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_BeneficialOwners', N'UniqueRelationID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'AlternateToDirCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'AlternateToDirName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'ClientCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'ClientName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'ClientUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirectorCapacity');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirectorCapacityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirectorID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirFileType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirFormerName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirFromDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirIncorpCountryOrBirthCountry');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirIncorpCountryOrBirthCountryCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirIncorpDateOrDOB');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirIncorpNrOrPassportNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirIncorpPlaceOrBirthPlace');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirNationality');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirOfficerType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirProductionOffice');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirRegisteredAddress');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirRegisteredCountry');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirServiceAddress');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirStatus');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirTIN');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirToDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'DirUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'EntityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'EntityLegacyID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'EntityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'EntityUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'LicenseeEntityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'LicenseeEntityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'RelationType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Directors', N'UniqueRelationID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'Administrator');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'AdministratorCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ClientCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ClientName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ClientUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityLegacyID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityStatus');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityStatusCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntitySubStatus');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntitySubStatusCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityTypeCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'EntityUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'IncorporationDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'IncorporationNumber');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'Jurisdiction');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'JurisdictionCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'Manager');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ManagerCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ProductionOffice');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ReferralOffice');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'ReferralOfficeCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'RiskGroup');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Entities', N'RiskGroupCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'BOInfoActivated');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'BOInformation');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'ClientCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'ClientName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'ClientUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'DIRInfoActivated');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'DIRInformation');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'MCInfoActivated');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'MCInformation');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'SHInfoActivated');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'SHInformation');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'UserCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'UserEmailAddress');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'UserName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_MasterClients', N'UserPermission');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'BenOwnerCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'BenOwnerName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'BenOwnerNrShare');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'CapacityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'CapacityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'ClientCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'ClientName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'ClientUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'EntityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'EntityLegacyID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'EntityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'EntityUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'RelationType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHAddress');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'ShareClassName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'ShareholderID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'ShareTypeCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHEndDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHSharesAuthorised');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHSharesHeld');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHSharesIssued');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHStartDate');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHTypeOfMember');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHTypeOfMemberCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'SHUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Shareholders', N'UniqueRelationID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'BalanceShare');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'BenOwnerCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'BenOwnerName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'BenOwnerNrShare');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'BenOwnerUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'ClientCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'ClientName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'ClientUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'EntityCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'EntityLegacyID');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'EntityName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'EntityUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'RelationType');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'TrustCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'TrusteeCode');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'TrusteeName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'TrusteeUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'TrustName');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'TrustUniqueNr');
INSERT [dbo].[TableColumnMapping] ([TableName], [ColumnName]) VALUES (N'pcp_Trustees', N'UniqueRelationID');
END
GO

/****** Object:  StoredProcedure [dbo].[sp_getColumnMapping]    Script Date: 8/18/2025 9:02:26 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_getColumnMapping]') AND type in (N'P', N'PC'))
BEGIN
EXEC dbo.sp_executesql @statement = N'CREATE PROCEDURE [dbo].[sp_getColumnMapping] AS' 
END
GO

ALTER PROCEDURE [dbo].[sp_getColumnMapping]
  @schema_name VARCHAR(100),
  @table_name VARCHAR(100) 
AS
BEGIN
  SET NOCOUNT ON;

  DECLARE @json_construct NVARCHAR(MAX) = N'{"type": "TabularTranslator", "mappings": {X}}';
  DECLARE @json NVARCHAR(MAX);

  -- Bouw de JSON op met de kolomnamen uit de TableColumnMapping tabel
  SET @json = (
      SELECT 
          (SELECT ColumnName AS 'name' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER) AS source,
          (SELECT ColumnName AS 'name' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER) AS target
      FROM [dbo].[TableColumnMapping]
      WHERE TableName = @table_name
     
      FOR JSON PATH
  );

  -- Voorkom NULL waarden
  SET @json = ISNULL(@json, '[]');

  -- JSON-output correct formatteren
  SELECT REPLACE(@json_construct, '{X}', @json) AS json_output;
END;

GO
