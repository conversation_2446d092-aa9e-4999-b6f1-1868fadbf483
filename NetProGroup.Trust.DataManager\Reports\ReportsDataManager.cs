// <copyright file="ReportsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Trust.Domain.Repository.Extensions;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Reports
{
    /// <summary>
    /// ReportDataManager implementation.
    /// </summary>
    public class ReportsDataManager : IReportsDataManager
    {
        private readonly IReportRepository _repository;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReportsDataManager"/> class.
        /// </summary>
        /// <param name="repository">The repository.</param>
        /// <param name="mapper">The mapper.</param>
        public ReportsDataManager(IReportRepository repository,
            IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        /// <inheritdoc />
        public async Task<Domain.Report.Report> AddReport(Domain.Report.Report report)
        {
            ArgumentNullException.ThrowIfNull(report, nameof(report));

            await _repository.InsertAsync(report, true);

            return report;
        }

        /// <summary>
        /// Checks whether a report with the same type and name already exists in the system.
        /// </summary>
        /// <param name="report">The report to check for existence.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if a report with the same type and name exists, otherwise false.</returns>
        public async Task<bool> ReportExists(Domain.Report.Report report)
        {
            ArgumentNullException.ThrowIfNull(report, nameof(report));

            return await _repository.AnyByConditionAsync(r => r.Type == report.Type && r.ReportName == report.ReportName);
        }

        /// <inheritdoc />
        public async Task<Document> GetDocument(Guid reportId)
        {
            var report = await _repository.FindFirstOrDefaultByConditionAsync(
                             r => r.Id == reportId,
                             query => query.Include(r => r.Document))
                         ?? throw new ConstraintException(ApplicationErrors.REPORT_NOT_FOUND.ToErrorCode(), "Report not found");

            return report.Document;
        }

        /// <inheritdoc />
        public async Task<IPagedList<ReportDTO>> GetReportsByTypeAsync(ReportRequestDTO request, DateTime? createdAfter = null)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var reports = await _repository.FindByConditionAsPagedListAsync(
                GetReportPredicate(request, createdAfter),
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                q => ApplySorting(q, request)
                    .Include(r => r.Document));

            var dtos = _mapper.Map<List<ReportDTO>>(reports);
            return new StaticPagedList<ReportDTO>(dtos, reports.GetMetaData());
        }

        /// <inheritdoc />
        public Task<Domain.Report.Report> CheckReportByIdAsync(Guid reportId)
        {
            return _repository.CheckReportByIdAsync(reportId);
        }

        private static Expression<Func<Domain.Report.Report, bool>> GetReportPredicate(ReportRequestDTO request, DateTime? createdAfter = null)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            Expression<Func<Domain.Report.Report, bool>> predicate = report => true;

            // Apply search term filter if present
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm;
                predicate = predicate.And(report => report.ReportName.Contains(searchTerm));
            }

            if (request.ReportTypes?.Any() == true)
            {
                var types = request.ReportTypes.ToList();
                predicate = predicate.And(report => types.Contains(report.Type));
            }

            if (createdAfter.HasValue)
            {
                createdAfter = createdAfter.Value.Date;
                predicate = predicate.And(report => report.CreatedAt > createdAfter);
            }

            return predicate;
        }

        /// <summary>
        /// Applies sorting to the Report query based on the specified sort criteria.
        /// </summary>
        /// <param name="query">The initial queryable collection of reports.</param>
        /// <param name="sortingInfo">Information for the sorting.</param>
        /// <returns>The sorted queryable collection of reports.</returns>
        private static IQueryable<Domain.Report.Report> ApplySorting(IQueryable<Domain.Report.Report> query, ReportRequestDTO sortingInfo)
        {
            return sortingInfo?.SortBy switch
            {
                var sb when string.Equals(sb, "ReportName", StringComparison.OrdinalIgnoreCase) => sortingInfo.SortOrder == "asc"
                    ? query.OrderBy(p => p.ReportName)
                    : query.OrderByDescending(p => p.ReportName),

                var sb when string.Equals(sb, "CreatedAt", StringComparison.OrdinalIgnoreCase) => sortingInfo.SortOrder == "asc"
                    ? query.OrderBy(p => p.CreatedAt)
                    : query.OrderByDescending(p => p.CreatedAt),

                _ => query.OrderByDescending(p => p.CreatedAt)
            };
        }
    }
}