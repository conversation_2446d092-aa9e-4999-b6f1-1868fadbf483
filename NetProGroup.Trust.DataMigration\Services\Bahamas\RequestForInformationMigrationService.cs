﻿using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Communication;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataMigration.Models.Bahamas;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// Request for information migration service.
    /// </summary>
    public sealed class RequestForInformationMigrationService
    {
        private readonly IRequestForInformationManager _requestForInformationManager;
        private readonly IRequestForInformationRepository _requestForInformationRepository;
        private readonly IAnnouncementsRepository _announcementsRepository;
        private readonly IUserRepository _usersRepository;
        private readonly IInboxService _inboxService;
        private readonly IAnnouncementRecipientsRepository _announcementRecipientsRepository;
        private readonly IInboxOwnersRepository _inboxOwnersRepository;
        private readonly FileMigrationService _fileMigrationService;
        private readonly IWorkContext _workContext;
        private readonly ILogger<RequestForInformationMigrationService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestForInformationMigrationService"/> class.
        /// </summary>
        /// <param name="requestForInformationManager">The request for information manager.</param>
        /// <param name="requestForInformationRepository">The request for information repository.</param>
        /// <param name="announcementsRepository">The announcement repository.</param>
        /// <param name="usersRepository">The users repository.</param>
        /// <param name="inboxService">The inbox service.</param>
        /// <param name="announcementRecipientsRepository">The announcement recipient repository.</param>
        /// <param name="inboxOwnersRepository">The inbox owners repository.</param>
        /// <param name="fileMigrationService">The file migratoin service.</param>
        /// <param name="workContext">The work context.</param>
        /// <param name="logger">The logger instance.</param>
        public RequestForInformationMigrationService(
            IRequestForInformationManager requestForInformationManager,
            IRequestForInformationRepository requestForInformationRepository,
            IAnnouncementsRepository announcementsRepository,
            IUserRepository usersRepository,
            IInboxService inboxService,
            IAnnouncementRecipientsRepository announcementRecipientsRepository,
            IInboxOwnersRepository inboxOwnersRepository,
            FileMigrationService fileMigrationService,
            IWorkContext workContext,
            ILogger<RequestForInformationMigrationService> logger)
        {
            _requestForInformationManager = requestForInformationManager ?? throw new ArgumentNullException(nameof(requestForInformationManager));
            _requestForInformationRepository = requestForInformationRepository ?? throw new ArgumentNullException(nameof(requestForInformationRepository));
            _announcementsRepository = announcementsRepository ?? throw new ArgumentNullException(nameof(announcementsRepository));
            _usersRepository = usersRepository ?? throw new ArgumentNullException(nameof(usersRepository));
            _inboxService = inboxService ?? throw new ArgumentNullException(nameof(inboxService));
            _announcementRecipientsRepository = announcementRecipientsRepository ?? throw new ArgumentNullException(nameof(announcementRecipientsRepository));
            _inboxOwnersRepository = inboxOwnersRepository ?? throw new ArgumentNullException(nameof(inboxOwnersRepository));
            _fileMigrationService = fileMigrationService ?? throw new ArgumentNullException(nameof(fileMigrationService));
            _workContext = workContext ?? throw new ArgumentNullException(nameof(workContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates migration requested for information for requested information.
        /// </summary>
        /// <param name="entry">The entry containing migration data.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="submission">The submission being migrated.</param>
        /// <param name="financialYear">The financial year of the submission</param>
        /// <param name="errors">The current errors.</param>
        public async Task CreateMigrationRequestedInforations(Entry entry, ApplicationUser migrationStartedByUser, string region, Submission submission, int financialYear, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            ArgumentNullException.ThrowIfNull(migrationStartedByUser, nameof(migrationStartedByUser));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            if (entry.RequestedInformation?.Details == null)
            {
                return;
            }

            this._logger.LogTrace("Starting requested for information migration.");

            foreach (var item in entry.RequestedInformation.Details)
            {
                var existingItem = submission.RequestsForInformation.FirstOrDefault(r => r.SubmissionId == submission.Id && r.CreatedAt == item.RequestedAt);

                if (existingItem == null)
                {
                    this._logger.LogDebug("Create new request for information item for the requested information");

                    existingItem = new RequestForInformation();
                    await this._requestForInformationRepository.InsertAsync(existingItem);
                }
                else
                {
                    this._logger.LogDebug("Updating existing request for information item for the requested information");
                }

                existingItem.SubmissionId = submission.Id;
                existingItem.CreatedByUser = await this._usersRepository.FindByUserByPredicateAsync(u => u.UserName == item.Username);

                if (existingItem.CreatedByUser == null)
                {
                    existingItem.CreatedBy = migrationStartedByUser.Id;
                }

                existingItem.CreatedAt = item.RequestedAt;
                existingItem.DeadLine = item.DeadlineAt;
                existingItem.Comments = item.Comment;
                existingItem.Status = MapRfiStatus(item.Status);

                await this._fileMigrationService.MigrateFiles(submission, existingItem, item.Files, errors);
                await this.MigrateAnnouncements(item.Reminders);
            }
        }

        /// <summary>
        /// Creates migration client returned information.
        /// </summary>
        /// <param name="entry">The entry containing migration data.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="submission">The submission being migrated.</param>
        /// <param name="financialYear">The financial year of the submission</param>
        /// <param name="errors">The current errors.</param>
        public async Task CreateMigrationClientReturnedInformations(Entry entry, ApplicationUser migrationStartedByUser, Submission submission, string region, int financialYear, List<string> errors)
        {
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            ArgumentNullException.ThrowIfNull(migrationStartedByUser, nameof(migrationStartedByUser));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            if (entry.ClientReturnedInformation?.Details == null)
            {
                return;
            }

            this._logger.LogTrace("Starting client returned information migration.");

            foreach (var item in entry.ClientReturnedInformation.Details)
            {
                var existingItem = submission.RequestsForInformation.FirstOrDefault(r => r.SubmissionId == submission.Id && r.CreatedAt == item.RequestedAt);

                if (existingItem == null)
                {
                    this._logger.LogDebug("Create new client returned information item for the requested information");

                    existingItem = new RequestForInformation();
                    await this._requestForInformationRepository.InsertAsync(existingItem);
                }
                else
                {
                    this._logger.LogDebug("Updating existing client returned information item for the requested information");
                }

                existingItem.SubmissionId = submission.Id;
                existingItem.CreatedByUser = await this._usersRepository.FindByUserByPredicateAsync(u => u.UserName == item.Username);

                if (existingItem.CreatedByUser == null)
                {
                    existingItem.CreatedBy = migrationStartedByUser.Id;
                }

                existingItem.CreatedAt = item.RequestedAt.GetValueOrDefault();
                existingItem.Comments = item.Comment;
                existingItem.Status = RequestForInformationStatus.Completed;

                await this._fileMigrationService.MigrateFiles(submission, existingItem, item.Files, errors);
            }
        }

        private async Task MigrateAnnouncements(List<ReminderSchema> reminders)
        {
            if (reminders == null)
            {
                return;
            }

            foreach (var item in reminders)
            {
                var existingItem = await this._announcementsRepository.FindSingleOrDefaultByConditionAsync(a => a.SendAt == item.ReminderDate && a.Body == item.Description);

                if (existingItem == null)
                {
                    existingItem = new Announcement();
                }

                existingItem.SendAt = item.ReminderDate.GetValueOrDefault();
                existingItem.Body = item.Description;
                existingItem.Subject = item.Description;

                if (item.ReminderDate > DateTime.UtcNow)
                {
                    existingItem.Status = AnnouncementStatus.Scheduled;
                }
                else
                {
                    existingItem.SentAt = item.ReminderDate;
                    existingItem.Status = AnnouncementStatus.Sent;

                    // Create the inbox entity:
                    var inboxId = await _inboxService.CreateInboxItemAsync(
                        UserConsts.SystemUserId,
                        WellKnownRoleIds.System,
                        UserConsts.InboxUserId,
                        null,
                        existingItem.Subject,
                        existingItem.Body,
                        null,
                        true);

                    // TODO add files to target inbox item:
                    /*
                     * AnnouncementDataManager::SendAnnouncementAsync
                     * 
                     * // Create the inbox documents if any exists
                    if (announcement.Documents.Count > 0)
                    {
                        foreach (var document in announcement.Documents)
                        {
                            // Create the inbox Document entity
                            _inboxService.AppendDocumentToInboxId(inboxId, document.DocumentId);
                        }
                    }*/

                    // TODO Recipient logic (will be done in a separate task after Alejandro finishes)
                    /*
                     // Create announcement recipients
                // Check if the announcement must be sent to all master clients
                if (data.SendToAllMasterClients)
                {
                    // Check if the recipients are all jurisdictions
                    if (data.SendToAllJurisdictions)
                    {
                        var jurisdictions = await _jurisdictionsRepository.FindAllAsync();
                        var jurisdictionIds = jurisdictions.Select(x => x.Id).ToList();
                        await _securityManager.ValidateJurisdictionsAccess(jurisdictionIds);
                        await CreateAnnouncementRecipientsAsync(announcementId, nameof(Jurisdiction), jurisdictionIds);
                    }
                    else
                    {
                        await _securityManager.ValidateJurisdictionsAccess(new List<Guid> { data.JurisdictionId });

                        // Send the announcement to the selected jurisdiction
                        await CreateAnnouncementRecipientAsync(announcementId, nameof(Jurisdiction), data.JurisdictionId);
                    }
                }

                // Create master clients announcement recipients
                if (data.MasterClientCodes.Count > 0)
                {
                    var masterClientIds = (await _masterClientsRepository.FindByConditionAsync(mc => data.MasterClientCodes.Contains(mc.Code))).Select(mc => mc.Id).ToList();
                    await CreateAnnouncementRecipientsAsync(announcementId, nameof(MasterClient), masterClientIds);
                }

                // Create legal entity announcement recipients
                if (data.LegalEntityIds.Count > 0)
                {
                    await CreateAnnouncementRecipientsAsync(announcementId, nameof(LegalEntity), data.LegalEntityIds);
                }

                // Create user announcement recipients
                if (data.UserIds.Count > 0)
                {
                    await CreateAnnouncementRecipientsAsync(announcementId, nameof(ApplicationUser), data.UserIds);
                }                     
                     */

                    // Create the inbox owners:
                    await CreateInboxOwnersAsync(inboxId, existingItem.Id, true);
                }

                // TODO get message using ReminderSchema object and migrate the associated files.
            }
        }

        /// <summary>
        /// Create an inbox owner entity.
        /// </summary>
        /// <param name="inboxId">The inbox id as Guid.</param>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="saveChanges">Determine if the changes to the current dbContext are going to be saved or not.</param>
        private async Task CreateInboxOwnersAsync(Guid inboxId, Guid announcementId, bool saveChanges = false)
        {
            // Retrieve the configured announcement recipients
            var announcementRecipients = await _announcementRecipientsRepository.FindByConditionAsync(
                ao => ao.AnnouncementId == announcementId);

            // Create an inbox owner per announcement recipient
            foreach (var announcementRecipient in announcementRecipients)
            {
                var inboxOwner = new InboxOwner()
                {
                    InboxId = inboxId,
                    OwnerId = announcementRecipient.RecipientId,
                    Type = announcementRecipient.Type,
                };

                await _inboxOwnersRepository.InsertAsync(inboxOwner, saveChanges);
            }
        }

        private static RequestForInformationStatus MapRfiStatus(string statusText)
        {
            switch (statusText)
            {
                case "RETURNED":
                    return RequestForInformationStatus.Completed;

                case "CANCELLED":
                    return RequestForInformationStatus.Cancelled;

                case "REQUESTED":
                    return RequestForInformationStatus.Active;

                default:
                    throw new InvalidOperationException($"Status {statusText} is not supported.");
            }
        }
    }
}
