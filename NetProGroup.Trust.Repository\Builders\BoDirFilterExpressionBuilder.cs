// <copyright file="BoDirFilterExpressionBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Defines.BODirector;
using NetProGroup.Trust.DomainShared.Enums;
using System.Linq.Expressions;

namespace NetProGroup.Trust.Domain.Repository.Builders
{
    /// <summary>
    /// Provides builder for filter expressions.
    /// </summary>
    public static class BoDirFilterExpressionBuilder
    {
        /// <summary>
        /// Provides filter expression  for director has missing information.
        /// </summary>
        public static readonly Expression<Func<Director, bool>> DirHasMissingInformationExpression = GetHasDirMissingInformationPredicate();

        /// <summary>
        /// Provides filter expression for director status is PendingUpdateRequest.
        /// </summary>
        public static readonly Expression<Func<Director, bool>> DirStatusIsPendingUpdateRequestExpression = director =>
            director.DirectorHistories.OrderBy(o => o.CreatedAt).LastOrDefault().Status == LegalEntityRelationStatus.PendingUpdateRequest;

        /// <summary>
        /// Provides filter expression for director Specifics.
        /// </summary>
        public static readonly Expression<Func<Director, string>> DirSpecificsExpression = DirStatusIsPendingUpdateRequestExpression
            .ConditionalSelect(
                trueExpression: _ => "PendingUpdateRequest",
                falseExpression: DirHasMissingInformationExpression.ConditionalSelect("MissingInformation", null));

        /// <summary>
        /// Provides filter expression for beneficial owner has missing information.
        /// </summary>
        public static readonly Expression<Func<BeneficialOwner, bool>> BoHasMissingInformationExpression = GetHasBoMissingInformationPredicate();

        /// <summary>
        /// Provides filter expression for beneficial owner status is PendingUpdateRequest.
        /// </summary>
        public static readonly Expression<Func<BeneficialOwner, bool>> BoStatusIsPendingUpdateRequestExpression = director =>
            director.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).LastOrDefault().Status == LegalEntityRelationStatus.PendingUpdateRequest;

        /// <summary>
        ///  Provides filter expression for beneficial owner Specifics.
        /// </summary>
        public static readonly Expression<Func<BeneficialOwner, string>> BoSpecificsExpression = BoStatusIsPendingUpdateRequestExpression
            .ConditionalSelect(
                trueExpression: _ => "PendingUpdateRequest",
                falseExpression: BoHasMissingInformationExpression.ConditionalSelect("MissingInformation", null));

        /// <summary>
        ///  Expression filter.
        /// </summary>
        /// <returns>Expression Func input type Director and output type BoDirItemDTO. </returns>
        public static Expression<Func<LegalEntity, bool>> EntityHasBoDirInfo() =>
            entity => entity.BeneficialOwners.Count != 0 || entity.Directors.Count != 0;

        /// <summary>
        /// Expression filter.
        /// </summary>
        /// <returns>The expression.</returns>
        public static Expression<Func<Director, bool>> GetHasDirMissingInformationPredicate()
        {
            return director =>
                    director.IsIndividual
                        ? string.IsNullOrEmpty(director.RelationType) ||
                          string.IsNullOrEmpty(director.Name) ||
                          !director.AppointmentDate.HasValue ||
                          string.IsNullOrEmpty(director.ResidentialAddress) ||
                          !director.DateOfBirth.HasValue ||
                          string.IsNullOrEmpty(director.CountryOfBirth) ||
                          string.IsNullOrEmpty(director.Nationality)

                        // Non-individual directors (missing information)
                        : string.IsNullOrEmpty(director.RelationType) ||
                          string.IsNullOrEmpty(director.Name) ||
                          string.IsNullOrEmpty(director.IncorporationNr) ||
                          !director.AppointmentDate.HasValue ||
                          string.IsNullOrEmpty(director.Address) ||
                          !director.IncorporationDate.HasValue ||
                          string.IsNullOrEmpty(director.Country);
        }

        /// <summary>
        /// Expression filter.
        /// </summary>
        /// <returns>The expression.</returns>
        public static Expression<Func<BeneficialOwner, bool>> GetHasBoMissingInformationPredicate()
        {
            return bo => // Nevis
                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP01 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         !bo.DateOfBirth.HasValue ||
                         string.IsNullOrEmpty(bo.CountryOfBirth) ||
                         string.IsNullOrEmpty(bo.Nationality) ||
                         string.IsNullOrEmpty(bo.ResidentialAddress))) ||

                    ((bo.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP02 ||
                      bo.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP03 ||
                      bo.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP04 ||
                      bo.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP05 ||
                      bo.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP06) &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         string.IsNullOrEmpty(bo.IncorporationNr) ||
                         !bo.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(bo.CountryOfFormation) ||
                         string.IsNullOrEmpty(bo.Address))) ||

                    // BVI
                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP01 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         !bo.DateOfBirth.HasValue ||
                         string.IsNullOrEmpty(bo.PlaceOfBirth) ||
                         string.IsNullOrEmpty(bo.Nationality) ||
                         string.IsNullOrEmpty(bo.ResidentialAddress))) ||

                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP02 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         string.IsNullOrEmpty(bo.IncorporationNr) ||
                         !bo.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(bo.Address) ||
                         string.IsNullOrEmpty(bo.Country))) ||

                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP03 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         string.IsNullOrEmpty(bo.IncorporationNr) ||
                         !bo.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(bo.Address) ||
                         string.IsNullOrEmpty(bo.CountryOfFormation) ||
                         string.IsNullOrEmpty(bo.NameOfRegulator) ||
                         string.IsNullOrEmpty(bo.JurisdictionOfRegulator))) ||

                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP04 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         string.IsNullOrEmpty(bo.IncorporationNr) ||
                         !bo.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(bo.Address) ||
                         string.IsNullOrEmpty(bo.CountryOfFormation) ||
                         string.IsNullOrEmpty(bo.SovereignState))) ||

                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP05 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         string.IsNullOrEmpty(bo.IncorporationNr) ||
                         !bo.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(bo.Address) ||
                         string.IsNullOrEmpty(bo.CountryOfFormation) ||
                         string.IsNullOrEmpty(bo.StockExchangeCode) ||
                         string.IsNullOrEmpty(bo.StockExchangeName))) ||

                    (bo.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP06 &&
                        (string.IsNullOrEmpty(bo.Name) ||
                         string.IsNullOrEmpty(bo.IncorporationNr) ||
                         !bo.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(bo.Address) ||
                         string.IsNullOrEmpty(bo.CountryOfFormation)));
        }
    }
}
