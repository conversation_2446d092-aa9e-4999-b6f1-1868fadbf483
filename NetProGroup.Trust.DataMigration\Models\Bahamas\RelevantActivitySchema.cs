using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Relevant activity schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class RelevantActivitySchema
    {
        /// <summary>
        /// Gets or sets the selected.
        /// </summary>
        [BsonElement("selected")]
        public bool Selected { get; set; }

        /// <summary>
        /// Gets or sets the part_of_financial_period.
        /// </summary>
        [BsonElement("part_of_financial_period")]
        public bool PartOfFinancialPeriod { get; set; }

        /// <summary>
        /// Gets or sets the financial_periods.
        /// </summary>
        [BsonElement("financial_periods")]
        public List<FinancialPeriodSchema> FinancialPeriods { get; set; }
    }
}
