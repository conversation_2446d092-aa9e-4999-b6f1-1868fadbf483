﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Users
{
    public class UsersAppServiceTests : TestBase
    {
        private IUsersAppService _usersAppService;
        private IActivityLogRepository _activityLogRepository;

        [SetUp]
        public void Setup()
        {
            _usersAppService = _server.Services.GetRequiredService<IUsersAppService>();
            _activityLogRepository = _server.Services.GetRequiredService<IActivityLogRepository>();
        }

        [Test]
        public async Task AcceptTermsConditionsAsync_ShouldSetCorrectTimeInActivityLog()
        {
            // Arrange
            SetWorkContextUser(ClientUser);
            var expectedDateTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm");

            // Act
            await _usersAppService.AcceptTermsConditionsAsync(ClientUser.Id, new AcceptTermsConditionsDTO
            {
                Version = "1.0"
            });

            // Assert
            var result = await _activityLogRepository.FindByConditionAsync(
                log => log.ActivityType == ActivityLogActivityTypes.UserTermsConditionsAccepted &&
                       log.CreatedByIdentityUserId == ClientUser.Id);

            result.Should().ContainSingle()
                .Which.Text.Should().EndWith($"{expectedDateTime} (UTC)");
        }

        [Test]
        public async Task GetAuthorizedJurisdictionsAsync_WithPermissionForOneJurisdiction_ReturnsThatJurisdiction()
        {
            // Arrange
            var userManager = _server.Services.GetRequiredService<IUserManager>();
            var bahamasRole = await CreateRoleAsync(userManager, WellKnownRoleNames.Bahamas_Owner);

            var testUser = await CreateUserWithRolesAsync(userManager, new[] { bahamasRole.Id });

            SetWorkContextUser(testUser);

            // Act
            var jutisdictions = await _usersAppService.GetAuthorizedJurisdictionsAsync();
            var result = jutisdictions.Select(r => r.Id);

            // Assert
            result.Should().ContainSingle().Which.Should().Be(JurisdictionBahamasId);
        }

        [Test]
        public async Task GetAuthorizedJurisdictionsAsync_WithPermissionForMultipleJurisdictions_ReturnsAllJurisdictions()
        {
            // Arrange
            var userManager = _server.Services.GetRequiredService<IUserManager>();
            var bahamasRole = await CreateRoleAsync(userManager, WellKnownRoleNames.Bahamas_Owner);
            var nevisRole = await CreateRoleAsync(userManager, WellKnownRoleNames.Nevis_Owner);
            var panamaRole = await CreateRoleAsync(userManager, WellKnownRoleNames.Panama_Owner);

            var testUser = await CreateUserWithRolesAsync(userManager, new[] { bahamasRole.Id, nevisRole.Id, panamaRole.Id });
            SetWorkContextUser(testUser);

            var expectedIds = new[]
            {
                JurisdictionBahamasId,
                JurisdictionNevisId,
                JurisdictionPanamaId
            };

            // Act
            var result = await _usersAppService.GetAuthorizedJurisdictionsAsync();

            // Assert
            result.Should().HaveCount(3);
            result.Select(r => r.Id).Should().BeEquivalentTo(expectedIds);
        }

        [Test]
        public async Task GetAuthorizedJurisdictionsAsync_WithClientUser_TrowsExceptionForNonManagementUser()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            // Act
            Func<Task> act = async () => await _usersAppService.GetAuthorizedJurisdictionsAsync();

            // Assert
            await act.Should().ThrowAsync<ForbiddenException>()
                .WithMessage("The user is not a management user");
        }

        private async Task<ApplicationRoleDTO> CreateRoleAsync(IUserManager userManager, string roleName)
        {
            return await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = Guid.NewGuid(),
                Name = roleName,
                DisplayName = roleName
            });
        }

        private async Task<ApplicationUserDTO> CreateUserWithRolesAsync(IUserManager userManager, IEnumerable<Guid> roleIds)
        {
            return await userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User",
                    FirstName = "Test",
                    UserName = $"user_{Guid.NewGuid():N}@contoso.com",
                    DisplayName = "Test User",
                    Email = $"user_{Guid.NewGuid():N}@contoso.com",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = roleIds.ToList()
                }
            );
        }
    }
}

