﻿// <copyright file="PanamaPermissionsBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Builder for permissions for Panama.
    /// </summary>
    public class PanamaPermissionsBuilder : PermissionsBuilderBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PanamaPermissionsBuilder"/> class.
        /// </summary>
        public PanamaPermissionsBuilder()
        {
            SetupCompanyPermissions();
            SetupBODIRPermissions();
            SetupAnnouncementPermissions();
            SetupBFRPermissions();
            SetupStatusPermissions();
            SetupRFIPermissions();
        }

        /// <summary>
        /// Setup the company permissions for panama roles.
        /// </summary>
        private void SetupCompanyPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Panama_Owner,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.Companies_View_Custom_BFR_Fee,
                WellKnownPermissionNames.Companies_Set_Custom_BFR_Fee,
                WellKnownPermissionNames.Companies_View_Log,
                WellKnownPermissionNames.Companies_Move_Delete_Submissions,
                WellKnownPermissionNames.Companies_Set_Back_To_Onboarding_No_Submissions,
                WellKnownPermissionNames.Companies_Set_Inactive_To_File_Submissions);

            SetupPermissions(WellKnownRoleNames.Panama_CMU_SuperUser,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.Companies_View_Custom_BFR_Fee,
                WellKnownPermissionNames.Companies_Set_Custom_BFR_Fee,
                WellKnownPermissionNames.Companies_View_Log,
                WellKnownPermissionNames.Companies_Move_Delete_Submissions,
                WellKnownPermissionNames.Companies_Set_Inactive_To_File_Submissions);

            SetupPermissions(WellKnownRoleNames.Panama_Basic_User,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_View_Custom_BFR_Fee);

            SetupPermissions(WellKnownRoleNames.Panama_ACC_Records_SuperUser,
                WellKnownPermissionNames.Companies_View_Custom_BFR_Fee,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Inactive_To_File_Submissions);
        }

        /// <summary>
        /// Setup the BODIR permissions for panama roles.
        /// </summary>
        private void SetupBODIRPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Panama_Owner,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);

            SetupPermissions(WellKnownRoleNames.Panama_Officers_SuperUser,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);
        }

        /// <summary>
        /// Setup the announcement permissions for panama roles.
        /// </summary>
        private void SetupAnnouncementPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Panama_Owner,
                WellKnownPermissionNames.AnnouncementModule_Search,
                WellKnownPermissionNames.AnnouncementModule_View,
                WellKnownPermissionNames.AnnouncementModule_Delete,
                WellKnownPermissionNames.AnnouncementModule_Create_Limited);

            SetupPermissions(WellKnownRoleNames.Panama_COM_SuperUser,
               WellKnownPermissionNames.AnnouncementModule_Search,
               WellKnownPermissionNames.AnnouncementModule_View,
               WellKnownPermissionNames.AnnouncementModule_Delete);
        }

        /// <summary>
        /// Setup the basic financial report permissions for panama roles.
        /// </summary>
        private void SetupBFRPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Panama_Owner,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_View,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Search,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Export,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Reset,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_View_Paid,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.BFRPanamaModule_Invoices_Export,
                WellKnownPermissionNames.BFRPanamaModule_Start_RFI_Request);

            SetupPermissions(WellKnownRoleNames.Panama_ACC_Records_SuperUser,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_View,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Search,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Export,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Reset,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_View_Paid,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.BFRPanamaModule_Invoices_Export,
                WellKnownPermissionNames.BFRPanamaModule_Start_RFI_Request);

            SetupPermissions(WellKnownRoleNames.Panama_Basic_User,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_View,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Search,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Export,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_View_Paid,
                WellKnownPermissionNames.BFRPanamaModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.BFRPanamaModule_Start_RFI_Request);
        }

        /// <summary>
        /// Setup the status permissions for panama roles.
        /// </summary>
        private void SetupStatusPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Panama_Owner,
                WellKnownPermissionNames.General_Status_Page_Sync);
        }

        /// <summary>
        /// Setup the RFI permissions for panama roles.
        /// </summary>
        private void SetupRFIPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Panama_Owner,
                WellKnownPermissionNames.RFI_Module_View,
                WellKnownPermissionNames.RFI_Complete,
                WellKnownPermissionNames.RFI_Start,
                WellKnownPermissionNames.RFI_Cancel);

            SetupPermissions(WellKnownRoleNames.Panama_ACC_Records_SuperUser,
                WellKnownPermissionNames.RFI_Module_View,
                WellKnownPermissionNames.RFI_Complete,
                WellKnownPermissionNames.RFI_Start,
                WellKnownPermissionNames.RFI_Cancel);

            SetupPermissions(WellKnownRoleNames.Panama_Officers_SuperUser,
                WellKnownPermissionNames.RFI_Complete,
                WellKnownPermissionNames.RFI_Start,
                WellKnownPermissionNames.RFI_Cancel);

            SetupPermissions(WellKnownRoleNames.Panama_Basic_User,
                WellKnownPermissionNames.RFI_Module_View);
        }
    }
}
