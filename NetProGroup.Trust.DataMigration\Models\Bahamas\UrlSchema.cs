﻿using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a Url schema for data migration purposes.
    /// </summary>
    [BsonIgnoreExtraElements]
    public class UrlSchema : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the URL of the resource.
        /// </summary>
        [BsonElement("url")]
        public Uri Url { get; set; }

        /// <summary>
        /// Gets or sets the name of the URL.
        /// </summary>
        [BsonElement("name")]
        public string Name { get; set; }
    }
}
