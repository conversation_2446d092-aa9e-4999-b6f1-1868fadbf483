﻿using AutoMapper;
using FluentAssertions;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.DataManager.AutoMapper;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Tests.AutoMapper
{
    [TestFixture()]
    public class BoDirDataProfileTests
    {
        private IMapper _mapper;

        [SetUp]
        public void SetUp()
        {
            _mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<BoDirDataProfile>();
            }).CreateMapper();
        }

        [Test]
        [TestCase(nameof(Director.RelationType))]
        [TestCase(nameof(Director.Name))]
        [TestCase(nameof(Director.AppointmentDate))]
        [TestCase(nameof(Director.ResidentialAddress))]
        [TestCase(nameof(Director.DateOfBirth))]
        [TestCase(nameof(Director.CountryOfBirth))]
        [TestCase(nameof(Director.Nationality))]
        public void Map_ForIncompleteIndividualDirector_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var director = BuildCompleteDirector("Test Individual Director", true);
            ClearProperty(requiredPropertyName, director);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(director);

            // Assert
            result.HasMissingInformation.Should().BeTrue();
            result.Specifics.Should().Be("MissingInformation",
                "The position is missing required information, so the specifics should indicate this.");
        }

        [Test]
        public void Map_ForCompleteIndividualDirector_SetsMissingInformationToFalse()
        {
            // Arrange
            var director = BuildCompleteDirector("Test Individual Director", true);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(director);

            // Assert
            result.HasMissingInformation.Should().BeFalse();
        }

        [Test]
        [TestCase(nameof(Director.RelationType))]
        [TestCase(nameof(Director.Name))]
        [TestCase(nameof(Director.IncorporationNr))]
        [TestCase(nameof(Director.AppointmentDate))]
        [TestCase(nameof(Director.Address))]
        [TestCase(nameof(Director.IncorporationDate))]
        [TestCase(nameof(Director.Country))]
        public void Map_ForIncompleteNonIndividualDirector_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var director = BuildCompleteDirector("Test None Individual Director", isIndividual: false);
            ClearProperty(requiredPropertyName, director);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(director);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a non individual position.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The position is missing required information, so the specifics should indicate this.");
        }

        [Test]
        public void Map_ForCompleteNonIndividualDirector_SetsMissingInformationToFalse()
        {
            // Arrange
            var director = BuildCompleteDirector("Test Non Individual Director", isIndividual: false);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(director);

            // Assert
            result.HasMissingInformation.Should().BeFalse();
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.DateOfBirth))]
        [TestCase(nameof(BeneficialOwner.CountryOfBirth))]
        [TestCase(nameof(BeneficialOwner.Nationality))]
        [TestCase(nameof(BeneficialOwner.ResidentialAddress))]
        public void Map_ForIncompleteKNTP01BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP01 BeneficialOwner", "KNTP01");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for an KNTP01 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        public void Map_ForCompleteKNTP01BeneficialOwner_SetsMissingInformationToFalse()
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP01 BeneficialOwner", "KNTP01");

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should().BeFalse();
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        public void Map_ForIncompleteKNTP02BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP02 BeneficialOwner", "KNTP02");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a KNTP02 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        public void Map_ForIncompleteKNTP03BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP03 BeneficialOwner", "KNTP03");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a KNTP03 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        public void Map_ForIncompleteKNTP04BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP04 BeneficialOwner", "KNTP04");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a KNTP04 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        public void Map_ForIncompleteKNTP05BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP05 BeneficialOwner", "KNTP05");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a KNTP05 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        public void Map_ForIncompleteKNTP06BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test KNTP06 BeneficialOwner", "KNTP06");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a KNTP06 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.DateOfBirth))]
        [TestCase(nameof(BeneficialOwner.PlaceOfBirth))]
        [TestCase(nameof(BeneficialOwner.Nationality))]
        [TestCase(nameof(BeneficialOwner.ResidentialAddress))]
        public void Map_ForIncompleteVGTP01BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test VGTP01 BeneficialOwner", "VGTP01");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a VGTP01 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.Address))]
        [TestCase(nameof(BeneficialOwner.Country))]
        public void Map_ForIncompleteVGTP02BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test VGTP02 BeneficialOwner", "VGTP02");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a VGTP02 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.Address))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.NameOfRegulator))]
        [TestCase(nameof(BeneficialOwner.JurisdictionOfRegulator))]
        public void Map_ForIncompleteVGTP03BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test VGTP03 BeneficialOwner", "VGTP03");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a VGTP03 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.Address))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.SovereignState))]
        public void Map_ForIncompleteVGTP04BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test VGTP04 BeneficialOwner", "VGTP04");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a VGTP04 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        [TestCase(nameof(BeneficialOwner.StockExchangeCode))]
        [TestCase(nameof(BeneficialOwner.StockExchangeName))]
        public void Map_ForIncompleteVGTP05BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test VGTP05 BeneficialOwner", "VGTP05");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a VGTP05 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(nameof(BeneficialOwner.Name))]
        [TestCase(nameof(BeneficialOwner.IncorporationNr))]
        [TestCase(nameof(BeneficialOwner.IncorporationDate))]
        [TestCase(nameof(BeneficialOwner.CountryOfFormation))]
        [TestCase(nameof(BeneficialOwner.Address))]
        public void Map_ForIncompleteVGTP06BeneficialOwner_SetsMissingInformationToTrue(string requiredPropertyName)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test VGTP06 BeneficialOwner", "VGTP06");
            ClearProperty(requiredPropertyName, beneficialOwner);

            // Act 
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasMissingInformation.Should()
                  .BeTrue("{0} is a required field for a VGTP06 BeneficialOwner.", requiredPropertyName);
            result.Specifics.Should().Be("MissingInformation",
                "The BeneficialOwner is missing required information, so the specifics should indicate this.");
        }

        [Test]
        public void Map_ForEntityWithDirector_SetsHasBoDirInfoToTrue()
        {
            // Arrange
            var director = BuildCompleteDirector("Test Director");

            // Act
            var result = _mapper.Map<BoDirItemDTO>(director);

            // Assert
            result.HasBoDirInformation.Should().BeTrue("Director entity should have BoDir info.");
        }

        [Test]
        public void Map_ForEntityWithBeneficialOwner_SetsHasBoDirInfoToTrue()
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test BeneficialOwner", "KNTP01");

            // Act
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.HasBoDirInformation.Should().BeTrue("BeneficialOwner entity should have BoDir info.");
        }

        [Test]
        public void Map_ForEntityWithoutBeneficialOwnerOrDirectors_SetsHasBoDirInfoToFalse()
        {
            //Arrange
            var masterClient = new MasterClient() { Code = "TEST_123" };
            var legalEntity = BuildLegalEntityWithoutBoDirs(masterClient);

            // Act
            var result = _mapper.Map<BoDirItemDTO>(legalEntity);

            // Assert
            result.HasBoDirInformation.Should().BeFalse("LegalEntity should not have BoDir info.");
        }

        [Test]
        [TestCase(LegalEntityRelationStatus.PendingUpdateRequest, "PendingUpdateRequest")]
        [TestCase(LegalEntityRelationStatus.Confirmed, "MissingInformation")]
        [TestCase(LegalEntityRelationStatus.Initial, "MissingInformation")]
        [TestCase(LegalEntityRelationStatus.Refreshed, "MissingInformation")]
        [TestCase(LegalEntityRelationStatus.UpdateReceived, "MissingInformation")]
        public void Map_ForIncompleteDirectorWithPendingUpdateRequest_OverridesMissingInformationInSpecifics(
            LegalEntityRelationStatus status, string expectedSpecifics)
        {
            // Arrange
            var director = BuildCompleteDirector("Test DirSpecifics", status: status);
            ClearProperty(nameof(Director.Name), director);

            // Act
            var result = _mapper.Map<BoDirItemDTO>(director);

            // Assert
            result.Specifics.Should().Be(expectedSpecifics,
                "The position has pending update request, so the specifics should indicate this.");
        }

        [Test]
        [TestCase(LegalEntityRelationStatus.PendingUpdateRequest, "PendingUpdateRequest")]
        [TestCase(LegalEntityRelationStatus.Confirmed, "MissingInformation")]
        [TestCase(LegalEntityRelationStatus.Initial, "MissingInformation")]
        [TestCase(LegalEntityRelationStatus.Refreshed, "MissingInformation")]
        [TestCase(LegalEntityRelationStatus.UpdateReceived, "MissingInformation")]
        public void Map_ForIncompleteBeneficialOwnerWithPendingUpdateRequest_OverridesMissingInformationInSpecifics(
            LegalEntityRelationStatus status, string expectedSpecifics)
        {
            // Arrange
            var beneficialOwner = BuildCompleteBeneficialOwner("Test Specifics", status: status);
            ClearProperty(nameof(BeneficialOwner.Name), beneficialOwner);

            // Act
            var result = _mapper.Map<BoDirItemDTO>(beneficialOwner);

            // Assert
            result.Specifics.Should().Be(expectedSpecifics,
                "The position has pending update request, so the specifics should indicate this.");
        }

        private static void ClearProperty<T>(string requiredPropertyName, T position)
        {
            // Use reflection to set the required property to null
            var propertyInfo = typeof(T).GetProperty(requiredPropertyName);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"Property '{requiredPropertyName}' does not exist on type '{nameof(position)}'.");
            }

            // Set the property to null or empty string based on its type
            if (propertyInfo.PropertyType == typeof(string))
                propertyInfo.SetValue(position, string.Empty);
            else if (propertyInfo.PropertyType == typeof(DateTime?))
                propertyInfo.SetValue(position, null);
            else if (propertyInfo.PropertyType == typeof(int?))
                propertyInfo.SetValue(position, null);
            else if (propertyInfo.PropertyType == typeof(bool))
                propertyInfo.SetValue(position, false);
            else
                propertyInfo.SetValue(position, null);
        }

        private static Director BuildCompleteDirector(string name, bool isIndividual = false, LegalEntityRelationStatus status = LegalEntityRelationStatus.Confirmed) => new Director
        {
            Name = name,
            Code = "COMPLETE_DIRECTOR",
            IsIndividual = isIndividual,
            RelationType = "Director",
            AppointmentDate = DateTime.UtcNow.AddYears(-1),
            
            // Assuming this is of an individual director
            DateOfBirth = DateTime.UtcNow.AddYears(-30),
            CountryOfBirth = "United States",
            Nationality = "American",
            ResidentialAddress = "123 Main St, New York, NY",

            // Assuming this is of a non individual director
            Country = "United States",
            Address = "123 Main St, New York, NY",
            IncorporationNr = !isIndividual ? "12345" : null,
            IncorporationDate = !isIndividual ? DateTime.UtcNow.AddYears(-1) : null,

            OfficerTypeName = "Director",
            DirectorHistories = new List<DirectorHistory>
                {
                    new DirectorHistory
                    {
                        Name = name,
                        Code = "D_COMPLETE",
                        Status = status,
                        ConfirmedAt = DateTime.UtcNow.AddDays(-30),
                        CreatedAt = DateTime.UtcNow.AddDays(-60),
                        RelationType = "Director",
                        FileType = "Default",
                    }
                },
            FileType = "Default"
        };

        private static BeneficialOwner BuildCompleteBeneficialOwner(string name, string officerTypeCode = "KNTP01", LegalEntityRelationStatus status = LegalEntityRelationStatus.Confirmed) => new BeneficialOwner
        {
            Name = name,
            Code = $"COMPLETE_INDIVIDUAL_BENEFICIALOWNER_{officerTypeCode}",
            DateOfBirth = DateTime.UtcNow.AddYears(-1),
            CountryOfBirth = "United States",
            Nationality = "American",
            ResidentialAddress = "123 Main St, New York, NY",
            OfficerTypeCode = officerTypeCode,
            OfficerTypeName = officerTypeCode,
            IncorporationNr = "INC123456",
            IncorporationDate = DateTime.UtcNow.AddYears(-15),
            CountryOfFormation = "United States",
            Address = "123 Main St, New York, NY", // Assuming Address is the same as Residential
            PlaceOfBirth = "New York",
            Country = "United States",
            NameOfRegulator = "US Regulator",
            JurisdictionOfRegulator = "US Jurisdiction",
            SovereignState = "United States",
            StockExchangeCode = "NYSE",
            StockExchangeName = "New York Stock Exchange",

            BeneficialOwnerHistories = new List<BeneficialOwnerHistory>
                {
                    new BeneficialOwnerHistory
                    {
                        Name = name,
                        Code = "D_COMPLETE",
                        Status = status,
                        ConfirmedAt = DateTime.UtcNow.AddDays(-30),
                        CreatedAt = DateTime.UtcNow.AddDays(-60),
                        FileType = "Default",
                    }
},
            FileType = "Default"
        };

        private static LegalEntity BuildLegalEntityWithoutBoDirs(MasterClient masterClient) => new LegalEntity(Guid.NewGuid())
        {
            Name = "Company-Without-BoDirs",
            Code = "NONE001",
            EntityType = LegalEntityType.Company,
            MasterClient = masterClient,
            JurisdictionId = Guid.NewGuid(),
            EntityTypeName = LegalEntityTypes.IBC,
            ProductionOffice = "TNEV",
            ReferralOffice = "TNEV",
            LegacyCode = "LEG004",
            IncorporationNr = "INC004",
            IncorporationDate = DateTime.UtcNow.AddMonths(-6),
            Directors = new List<Director>(), // Empty list
            BeneficialOwners = new List<BeneficialOwner>() // Empty list
        };
    }
}