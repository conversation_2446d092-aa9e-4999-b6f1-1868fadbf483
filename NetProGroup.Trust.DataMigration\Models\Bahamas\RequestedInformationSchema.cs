﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Requested information scherma.
    /// </summary>
    [BsonIgnoreExtraElements]
    public sealed class RequestedInformationSchema : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the status.
        /// </summary>
        [BsonElement("status")]
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the username.
        /// </summary>
        [BsonElement("username")]
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets the requested_at.
        /// </summary>
        [BsonElement("requested_at")]
        public DateTime RequestedAt { get; set; }

        /// <summary>
        /// Gets or sets the deadline_at.
        /// </summary>
        [BsonElement("deadline_at")]
        public DateTime DeadlineAt { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonElement("comment")]
        public string Comment { get; set; }

        /// <summary>
        /// Gets or sets the MessageId.
        /// </summary>
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("message_id")]
        public string MessageId { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonElement("files")]
        public List<FileSchema> Files { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonElement("reminders")]
        public List<ReminderSchema> Reminders { get; set; }
    }
}
