﻿// <copyright file="PermissionsBuilderBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Base class for building permissions.
    /// </summary>
    public class PermissionsBuilderBase
    {
        private readonly Dictionary<string, IList<string>> _permissionsByRole = new (StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// Creates a dictionary with the roles per permission.
        /// </summary>
        /// <param name="permissionsWithRoles">Dictionary to add the roles for each permission.</param>
        public void BuildPermissionsWithRoles(Dictionary<string, IList<string>> permissionsWithRoles)
        {
            ArgumentNullException.ThrowIfNull(permissionsWithRoles, nameof(permissionsWithRoles));

            foreach (var roleKey in _permissionsByRole.Keys)
            {
                foreach (var permission in _permissionsByRole[roleKey])
                {
                    if (!permissionsWithRoles.TryGetValue(permission, out IList<string> value))
                    {
                        value = new List<string>();
                        permissionsWithRoles[permission] = value;
                    }

                    if (!value.Contains(roleKey))
                    {
                        value.Add(roleKey);
                    }
                }
            }
        }

        /// <summary>
        /// Sets up the permissions for a role.
        /// </summary>
        /// <param name="role">The role to set the permissions for.</param>
        /// <param name="permissions">The permissions to set.</param>
        protected void SetupPermissions(string role, params string[] permissions)
        {
            ArgumentNullException.ThrowIfNull(permissions, nameof(permissions));

            _permissionsByRole.TryAdd(role, new List<string>());

            foreach (var permission in permissions)
            {
                _permissionsByRole[role].Add(permission);
            }
        }

        private void AddPermission(string role, string permission)
        {
            _permissionsByRole[role].Add(permission);
        }

        private void RemovePermission(string role, string permission)
        {
            _permissionsByRole[role].Remove(permission);
        }
    }
}