﻿// <copyright file="NevisTestDataSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Bogus;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.Application.AppServices.Tools;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations.Directors.RequestResponses;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Nevis
{
    /// <summary>
    /// Seeder for Nevis data.
    /// </summary>
    public class NevisTestDataSeeder : SeederBase, INevisTestDataSeeder
    {
        private readonly ILogger _logger;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;
        private readonly IBeneficialOwnersDataManager _beneficialOwnersDataManager;
        private readonly IDirectorsDataManager _directorsDataManager;

        private readonly Faker<SyncBeneficialOwner> _boFaker;
        private readonly ISubmissionsManager _submissionsManager;
        private readonly IModulesRepository _modulesRepository;
        private Module _strModule;
        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="NevisTestDataSeeder"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="jurisdictionsRepository">Instance of the jurisdiction repository.</param>
        /// <param name="legalEntitiesDataManager">Instance of the legal entities data manager.</param>
        /// <param name="beneficialOwnersDataManager">Instance of the beneficial owners data manager.</param>
        /// <param name="directorsDataManager">Instance of the directors data manager.</param>
        /// <param name="submissionsManager">Instance of the submissions manager.</param>
        /// <param name="modulesRepository">Instance of the modules repository.</param>
        public NevisTestDataSeeder(ILogger<NevisTestDataSeeder> logger,
            IServiceProvider serviceProvider,
            IJurisdictionsRepository jurisdictionsRepository,
            ILegalEntitiesDataManager legalEntitiesDataManager,
            IBeneficialOwnersDataManager beneficialOwnersDataManager,
            IDirectorsDataManager directorsDataManager,
            ISubmissionsManager submissionsManager,
            IModulesRepository modulesRepository)
            : base(logger, serviceProvider)
        {
            _logger = logger;
            _jurisdictionsRepository = jurisdictionsRepository;

            _legalEntitiesDataManager = legalEntitiesDataManager;
            _beneficialOwnersDataManager = beneficialOwnersDataManager;
            _directorsDataManager = directorsDataManager;
            _submissionsManager = submissionsManager;
            _modulesRepository = modulesRepository;

            _boFaker = BoFaker.Create();
        }

        /// <inheritdoc/>
        public async Task RunAsync()
        {
            _jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Nevis);
            _strModule = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.SimplifiedTaxReturn);

            try
            {
                await SyncHelper.LockAsync();
                SyncHelper.JurisdictionCodes = new List<string> { "KN" };

                await CreateMasterClientsAsync();
                await CreateCompaniesAsync();
                await CreateBOsAsync();
                await CreateDirectorsAsync();
                await CreateSubmissionsAsync();
            }
            finally
            {
                SyncHelper.Unlock();
            }
        }

        private static Dictionary<string, string> CreateFormDataSet()
        {
            var dataSet = new Dictionary<string, string>
            {
                { FormKeys.TaxResidentNonTaxResident, "false" },
                { FormKeys.TaxResidentResidentCountry, "ALB" },
                { FormKeys.CorporateAddressRecordsKeptAtRegisteredOffice, "false" },
                { "corporate-address.nevisAddress", "Hunkins Waterfront Plaza\nSuite 556, Main Street\nCharlestown, Nevis, West Indies." },
                { "corporate-address.recordsPlaceAddress", "12312321" },
                { FormKeys.CorporateMNEIsPartOfMNEGroup, "false" },
                { FormKeys.CorporateMNERequiresCbCReport, "false" },
                { FormKeys.HeadOfficeCompanyClassification, "IBC" },
                { FormKeys.HeadOfficeAddress1, "2" },
                { FormKeys.HeadOfficeAddress2, "2" },
                { FormKeys.HeadOfficeCity, "2" },
                { FormKeys.HeadOfficeZipCode, "2" },
                { FormKeys.HeadOfficeCountry, "AFG" },
                { FormKeys.HeadOfficeIsAddressInNevisDifferent, "false" },
                { FormKeys.HeadOfficeNevisAddress1, "Trident Trust Company (Nevis) Limited" },
                { FormKeys.HeadOfficeNevisAddress2, "Suite 556, Hunkins Waterfront Plaza" },
                { FormKeys.HeadOfficeNevisCity, "Charlestown" },
                { FormKeys.HeadOfficeNevisZipCode, "KN0802" },
                { FormKeys.HeadOfficeNevisCountry, "KNA" },
                { FormKeys.ContactName, "12" },
                { FormKeys.ContactPosition, "321" },
                { FormKeys.ContactAddress1, "312312" },
                { FormKeys.ContactAddress2, "3123" },
                { FormKeys.ContactZipCode, "12312312" },
                { FormKeys.ContactCountry, "ASM" },
                { FormKeys.ContactTelephoneCountryCode, "AD" },
                { FormKeys.ContactTelephonePrefix, "+376" },
                { FormKeys.ContactTelephoneNumber, "*********" },
                { FormKeys.ContactFaxCountryCode, "" },
                { FormKeys.ContactFaxPrefix, "" },
                { FormKeys.ContactFaxNumber, "" },
                { FormKeys.ContactEmail, "<EMAIL>" },
                { FormKeys.CompanyRepresentativeName, "Trident Trust Company (Nevis) Limited" },
                { FormKeys.CompanyRepresentativeTelephoneCountryCode, "KN" },
                { FormKeys.CompanyRepresentativeTelephonePrefix, "******" },
                { FormKeys.CompanyRepresentativeTelephoneNumber, "4691817" },
                { FormKeys.CompanyRepresentativeFaxCountryCode, "KN" },
                { FormKeys.CompanyRepresentativeFaxPrefix, "******" },
                { FormKeys.CompanyRepresentativeFaxNumber, "4691794" },
                { FormKeys.CompanyRepresentativeEmail, "<EMAIL>" },
                { FormKeys.BusinessActivitiesFirstActivity, "Fund Management Business" },
                { FormKeys.BusinessActivitiesFirstActivityFrom, "2023-01-01T04:00:00.000Z" },
                { FormKeys.BusinessActivitiesFirstActivityTo, "2023-12-31T04:00:00.000Z" },
                { FormKeys.BusinessActivitiesFirstActivityType, "Primary" },
                { "business-activities.activities.0.otherActivity", "" },
                { FormKeys.FinalizeConfirmationTrueInformation, "true" },
                { FormKeys.FinalizeConfirmationUnderstand, "true" },
                { FormKeys.FinalizeConfirmationAwarePerjury, "true" },
                { FormKeys.FinalizeNameOfPersonDeclaring, "12312321" },
                { FormKeys.FinalizeAddressOfPersonDeclaring, "123" },
                { FormKeys.FinalizeAddressOfPersonDeclaring2, "123" },
                { FormKeys.FinalizeOnMyOwnBehalf, "12312312" },
                { FormKeys.FinalizeDateOfSignature, "2024-11-28T04:00:00.000Z" },
                { FormKeys.FinalizeCountry, "ALB" },
                { FormKeys.FinalizeCity, "312321" },
                { FormKeys.FinalizeZipCode, "321321" }
            };

            return dataSet;
        }

        private async Task CreateMasterClientsAsync()
        {
            await CreateMasterClientAsync("ABCD");
            await CreateMasterClientAsync("NEV-1");
            await CreateMasterClientAsync("NEV-2");
            await CreateMasterClientAsync("NEV-3", false);

            await AssignUsersToMasterClientAsync("NEV-1");
            await AssignUsersToMasterClientAsync("NEV-2");
            await AssignUsersToMasterClientAsync("ABCD");
        }

        private async Task CreateCompaniesAsync()
        {
            var masterClientCode1 = "A7";
            var masterClientCode2 = "ANWU";
            await CreateCompaniesAsync([
                ("*********", "Costa Devices Limited", "2037761", masterClientCode1),
                ("*********", "GreenTech Consultancy Limited", "2022248", masterClientCode1),
                ("VG566202", "CNPC International (Chad) Ltd.", "566202", masterClientCode2)
            ]);
        }

        private async Task CreateBOsAsync()
        {
            var syncBOs = new List<SyncBeneficialOwner>();

            var individualFaker = _boFaker
                                  .RuleFor(owner => owner.OfficerTypeCode, "KNTP01")
                                  .RuleFor(owner => owner.FileType, "individual");

            var companyCode1 = "*********";
            var richardScott = individualFaker
                               .RuleFor(owner => owner.Name, "Richard Scott")
                               .RuleFor(owner => owner.CompanyNumber, companyCode1)
                               .RuleFor(owner => owner.EntityCode, companyCode1)
                               .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1954, 8, 15))
                               .RuleFor(owner => owner.CountryOfBirthOrIncorp, "United Kingdom")
                               .RuleFor(owner => owner.CountryCodeOfBirthOrIncorp, "UK")
                               .RuleFor(owner => owner.PlaceOfBirthOrIncorp, "London")
                               .RuleFor(owner => owner.Nationality, "British")
                               .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "Tramman, 5 Beach Road\r\nPort St Mary\r\nIM9 5NG\r\nIsle of Man")
                               .RuleFor(owner => owner.TIN, string.Empty)
                               .Generate();
            syncBOs.Add(richardScott);

            var davidHermanus = individualFaker
                                .RuleFor(owner => owner.Name, "David Hermanus BESTER")
                                .RuleFor(owner => owner.CompanyNumber, companyCode1)
                                .RuleFor(owner => owner.EntityCode, companyCode1)
                                .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1962, 5, 16))
                                .RuleFor(owner => owner.CountryOfBirthOrIncorp, "South Africa")
                                .RuleFor(owner => owner.CountryCodeOfBirthOrIncorp, "SA")
                                .RuleFor(owner => owner.Nationality, "British")
                                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "Woodlee, Hillberry Green\r\nDouglas\r\nIsle of Man IM2 6DE")
                                .RuleFor(owner => owner.TIN, string.Empty)
                                .Generate();
            syncBOs.Add(davidHermanus);

            var sarahMargaret = individualFaker
                                .RuleFor(owner => owner.Name, "Sarah Margaret Goodman")
                                .RuleFor(owner => owner.CompanyNumber, "*********")
                                .RuleFor(owner => owner.EntityCode, "*********")
                                .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1957, 6, 14))
                                .RuleFor(owner => owner.CountryOfBirthOrIncorp, "United Kingdom")
                                .RuleFor(owner => owner.CountryCodeOfBirthOrIncorp, "UK")
                                .RuleFor(owner => owner.PlaceOfBirthOrIncorp, "London")
                                .RuleFor(owner => owner.Nationality, "British")
                                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "Harrigan Estate\r\nTortola\r\nVG1110\r\nBritish Virgin Islands")
                                .RuleFor(owner => owner.TIN, string.Empty)
                                .Generate();
            syncBOs.Add(sarahMargaret);

            var cnpc = _boFaker
                       .RuleFor(owner => owner.Name, "China National Petroleum Corporation")
                       .RuleFor(owner => owner.CompanyNumber, "VG566202")
                       .RuleFor(owner => owner.EntityCode, "VG566202")
                       .RuleFor(owner => owner.FileType, "company")
                       .RuleFor(owner => owner.OfficerTypeCode, "VGTP02")
                       .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1990, 2, 9))
                       .RuleFor(owner => owner.Country, "China")
                       .RuleFor(owner => owner.JurisdictionOfRegulationOrSovereignState, "China")
                       .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "9 Dongzhimen North Street, Dongcheng District, Beijing\r\nChina")
                       .RuleFor(owner => owner.TIN, string.Empty)
                       .Generate();
            syncBOs.Add(cnpc);

            await _beneficialOwnersDataManager.SyncBeneficialOwnersAsync(new SyncBeneficialOwnerRequest { ChangedBeneficialOwners = syncBOs });
        }

        private async Task CreateDirectorsAsync()
        {
            string companyCode;
            var syncDirectors = new List<SyncDirector>();

            companyCode = "*********";

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/2020-06-09",
                FileType = "company",
                RelationType = "Director",
                OfficerTypeName = "Alternate Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Economist Incorporated",
                IncorporationNumberOrPassportNr = "C18834",
                CountryOfBirthOrIncorp = "Saint Kitts and Nevis",
                DateOfBirthOrIncorp = new DateTime(2000, 11, 28),
                FromDate = new DateTime(2020, 6, 9),
                ToDate = null,
                ResidentialOrRegisteredAddress = "Hunkins Waterfront Plaza\r\nSuite 556\r\nMain Street\r\nCharlestown\r\nNevis\r\nSaint Kitts and Nevis"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/2020-07-09",
                FileType = "company",
                RelationType = "Director",
                OfficerTypeName = "Alternate Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Executive Focus Inc.",
                IncorporationNumberOrPassportNr = "C18832",
                CountryOfBirthOrIncorp = "Saint Kitts and Nevis",
                DateOfBirthOrIncorp = new DateTime(2000, 11, 28),
                FromDate = new DateTime(2020, 6, 9),
                ToDate = null,
                ResidentialOrRegisteredAddress = "Hunkins Waterfront Plaza\r\nSuite 556\r\nMain Street\r\nCharlestown\r\nNevis\r\nSaint Kitts and Nevis"
            });

            companyCode = "*********";
            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/1957-06-14",
                FileType = "individual",
                RelationType = "Director",
                OfficerTypeName = "Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Sarah Margaret Goodman",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1957, 6, 14),
                CountryOfBirthOrIncorp = "United Kingdom",
                ServiceAddress = "",
                ResidentialOrRegisteredAddress = "Harrigan Estate\r\nTortola\r\nVG1110\r\nBritish Virgin Islands",
                FromDate = new DateTime(1900, 1, 1),
                ToDate = null,
                Nationality = "British"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/1954-10-12",
                FileType = "individual",
                RelationType = "Director",
                OfficerTypeName = "Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Barry Robert Goodman",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1954, 10, 12),
                CountryOfBirthOrIncorp = "",
                ServiceAddress = "",
                ResidentialOrRegisteredAddress = "",
                FromDate = new DateTime(1900, 1, 1),
                ToDate = null,
                Nationality = "United Kingdom"
            });

            companyCode = "VG566202";
            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/1971-08-28",
                FileType = "individual",
                RelationType = "Director",
                OfficerTypeName = "Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Zhiquan NIE",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1971, 8, 28),
                CountryOfBirthOrIncorp = "China",
                ServiceAddress = "No. 6-1 Fuchengmen Beidajie\r\nXicheng District\r\nBeijing\r\nChina 100034",
                ResidentialOrRegisteredAddress = "No. 2105, Gate No.1, Building 3\r\nShenggujiayuan, Chaoyang District, Beijing,\r\nChina",
                FromDate = new DateTime(2023, 1, 20),
                ToDate = null,
                Nationality = "Chinese"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/1964-09-12",
                FileType = "individual",
                RelationType = "Director",
                OfficerTypeName = "Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Xianxiong HUANG",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1964, 9, 12),
                CountryOfBirthOrIncorp = "China",
                ServiceAddress = "No. 6-1 Fuchengmen Beidajie\r\nXicheng District\r\nBeijing\r\nChina 100034",
                ResidentialOrRegisteredAddress = "No. 1, 3F, No. A100, Dongsimu Village\r\nHaidian District, Beijing\r\nChina",
                FromDate = new DateTime(2021, 4, 23),
                ToDate = null,
                Nationality = "Chinese"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = companyCode + "/1965-12-04",
                FileType = "individual",
                RelationType = "Director",
                OfficerTypeName = "Director",
                CompanyNumber = companyCode,
                EntityCode = companyCode,
                Name = "Henian LIU",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1965, 12, 04),
                CountryOfBirthOrIncorp = "China",
                ServiceAddress = "No. 243, Building 6, No. 20 Xueyuan Road\r\nHaidian District, Beijing\r\nChina",
                ResidentialOrRegisteredAddress = "No. 243, Building 6, No. 20 Xueyuan Road\r\nHaidian District, Beijing\r\nChina",
                FromDate = new DateTime(2018, 9, 29),
                ToDate = null,
                Nationality = "Chinese"
            });

            await _directorsDataManager.SyncDirectorsAsync(new SyncDirectorRequest { ChangedDirectors = syncDirectors });
        }

        private async Task CreateSubmissionsAsync()
        {
            var testUserEmail = "<EMAIL>";
            var user = await ServiceProvider.GetRequiredService<IUserRepository>()
                                             .FindByUserByPredicateAsync(applicationUser => applicationUser.Email == testUserEmail);
            var dbContext = ServiceProvider.GetRequiredService<TrustDbContext>();
            if (user == null)
            {
                user = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    IsActive = true,
                    Email = testUserEmail.ToLower(),
                    NormalizedEmail = testUserEmail.ToUpper(),
                    UserName = testUserEmail.ToLower(),
                    NormalizedUserName = testUserEmail.ToUpper(),
                    ObjectId = null,
                    ApplicationUserRoles = new List<Microsoft.AspNetCore.Identity.IdentityUserRole<Guid>>
                    {
                        new () { RoleId = WellKnownRoleIds.Client }
                    }
                };
                var users = new List<ApplicationUser>()
                {
                    user
                };

                var bulkOperationProvider = ServiceProvider.GetRequiredService<IBulkOperationProvider>();
                await bulkOperationProvider.BulkInsertAsync(users, dbContext, includeGraph: true);
            }

            var submissionIDs = new List<Guid>();
            await Parallel.ForAsync(0, 1, new ParallelOptions() { MaxDegreeOfParallelism = 1 }, async (i, _) =>
            {
                using var logScope = _logger.BeginScope(new { i });
                var scope = ServiceProvider.CreateAsyncScope();
                var scopeServiceProvider = scope.ServiceProvider;

                var submissionsRepository = scopeServiceProvider.GetRequiredService<ISubmissionsRepository>();

                var submissionsManager = scopeServiceProvider.GetRequiredService<ISubmissionsManager>();

                _logger.LogInformation("Creating master client Nevis-{I}", i);
                var masterClientCode = "Nevis-" + i;
                await CreateMasterClientAsync(masterClientCode, true, scopeServiceProvider);

                for (int j = 0; j < 1; j++)
                {
                    _logger.LogInformation("Creating submission for Nevis-{I}-{J}", i, j);
                    var code = $"VGSEED-{i}-{j}";
                    var legalEntityId = await CreateCompanyAsync(
                        code,
                        $"Test Company {i}-{j}",
                        $"220589-{i}-{j}",
                        masterClientCode,
                        scopeServiceProvider);

                    for (int y = 2023; y < 2024; y++)
                    {
                        _logger.LogInformation("Creating submission for Nevis-{I}-{J}-{Y}", i, j, y);
                        var submission = (await submissionsRepository.FindByConditionAsync(submission1 => submission1.LegalEntityId == legalEntityId &&
                                                                                                          submission1.FinancialYear == y)).SingleOrDefault();
                        Guid submissionId;
                        if (submission == null)
                        {
                            submissionId = (await submissionsManager.StartSubmissionAsync(
                                new StartSubmissionDTO()
                                {
                                    FinancialYear = y,
                                    LegalEntityId = legalEntityId,
                                    ModuleId = _strModule.Id,
                                    UserId = user.Id,
                                    UserEmail = user.Email
                                })).Id;
                            submission = await submissionsRepository.GetByIdAsync(submissionId);
                        }
                        else
                        {
                            submissionId = submission.Id;
                        }

                        submissionIDs.Add(submissionId);

                        if (submission.Status != SubmissionStatus.Draft && submission.Status != SubmissionStatus.Revision)
                        {
                            await submissionsManager.ReopenSubmissionAsync(new ReopenSubmissionDTO()
                            {
                                SubmissionId = submissionId,
                                Comments = "Reopening to update seeded test data"
                            });
                        }

                        await submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO() { Id = submissionId, DataSet = CreateFormDataSet() });

                        await submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO() { SubmissionId = submissionId });
                    }
                }
            });

            await _submissionsManager.MarkSubmissionsAsPaidAsync(submissionIDs, true, new List<Guid>() { _jurisdiction.Id });
            _logger.LogInformation("Marking submissions as paid for Nevis");
        }

        private async Task<Guid> CreateCompanyAsync(string code, string name, string incorporationNr, string masterClientCode, IServiceProvider serviceProvider = null)
        {
            var legalEntitiesDataManager = (serviceProvider ?? ServiceProvider).GetRequiredService<ILegalEntitiesDataManager>();
            var legalEntitiesRepository = (serviceProvider ?? ServiceProvider).GetRequiredService<ILegalEntitiesRepository>();
            var request = new DataManager.LegalEntities.Models.SyncLegalEntitiesRequest();

            var legalEntity = new DataManager.LegalEntities.Models.SyncLegalEntity
            {
                JurisdictionCode = _jurisdiction.Code,
                UniqueId = code,
                EntityType = LegalEntityType.Company,
                MasterClientCode = masterClientCode,
                Code = code,
                Name = name,
                IncorporationNr = incorporationNr,
                LegacyCode = $"legacy {code}",
                ReferralOffice = "REF1",
                EntityStatusCode = LegalEntityStatusCodes.Active,
                EntityStatus = LegalEntityStatusNames.Active,
                EntityTypeCode = "CI",
                EntityTypeName = LegalEntityTypes.IBC
            };
            request.LegalEntities.Add(legalEntity);

            await legalEntitiesDataManager.SyncLegalEntitiesAsync(request);
            return (await legalEntitiesRepository.FindByConditionAsync(entity => entity.Code == code && entity.IncorporationNr == incorporationNr)).Single().Id;
        }

        private async Task CreateCompaniesAsync((string code, string name, string incorporationNr, string masterClientCode)[] companies)
        {
            var request = new DataManager.LegalEntities.Models.SyncLegalEntitiesRequest();

            foreach (var (code, name, incorporationNr, masterClientCode) in companies)
            {
                var legalEntity = new DataManager.LegalEntities.Models.SyncLegalEntity
                {
                    JurisdictionCode = _jurisdiction.Code,
                    UniqueId = code,
                    EntityType = LegalEntityType.Company,
                    MasterClientCode = masterClientCode,
                    Code = code,
                    Name = name,
                    IncorporationNr = incorporationNr,
                    LegacyCode = $"legacy {code}",
                    ReferralOffice = "REF1",
                    EntityStatusCode = LegalEntityStatusCodes.Active,
                    EntityStatus = LegalEntityStatusNames.Active,
                    EntityTypeCode = "CI",
                    EntityTypeName = LegalEntityTypes.IBC
                };
                request.LegalEntities.Add(legalEntity);
            }

            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(request);
        }
    }
}