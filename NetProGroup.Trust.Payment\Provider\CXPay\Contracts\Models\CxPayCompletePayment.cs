using System.Xml.Serialization;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models
{
    [XmlRoot("complete-action")]
    public class CxPayCompletePayment
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        [XmlElement("api-key")]
        public string apiKey { get; set; }

        [XmlElement("token-id")]
        public string tokenId { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}