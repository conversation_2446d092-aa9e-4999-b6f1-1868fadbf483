﻿// <copyright file="SearchMasterClientsResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.MasterClients;

namespace NetProGroup.Trust.DataManager.MasterClients.RequestResponses
{
    /// <summary>
    /// Response model for searching Master Client data.
    /// </summary>
    public class SearchMasterClientsResponse
    {
        /// <summary>
        /// Gets or sets the list with MasterClientsSearchResultDTO items.
        /// </summary>
        public List<MasterClientsSearchResultDTO> MasterClientItems { get; set; } = new List<MasterClientsSearchResultDTO>();
    }
}
