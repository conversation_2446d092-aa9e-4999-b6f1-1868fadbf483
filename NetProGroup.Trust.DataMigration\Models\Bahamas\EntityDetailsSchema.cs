using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Enitity details schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public sealed class EntityDetailsSchema
    {
        /// <summary>
        /// Gets or sets the TIN.
        /// </summary>
        [BsonElement("TIN")]
        public string TIN { get; set; }

        /// <summary>
        /// Gets or sets the total_annual_gross_currency.
        /// </summary>
        [BsonElement("total_annual_gross_currency")]
        public string TotalAnnualGrossCurrency { get; set; }

        /// <summary>
        /// Gets or sets the total_annual_gross.
        /// </summary>
        [BsonElement("total_annual_gross")]
        public decimal TotalAnnualGross { get; set; }

        /// <summary>
        /// Gets or sets the is_same_business_address.
        /// </summary>
        [BsonElement("is_same_business_address")]
        public bool IsSameBusinessAddress { get; set; }

        /// <summary>
        /// Gets or sets the business_address.
        /// </summary>
        [BsonElement("business_address")]
        public BusinessAddressSchema BusinessAddress { get; set; }

        /// <summary>
        /// Gets or sets the name_of_MNE_group.
        /// </summary>
        [BsonElement("name_of_MNE_group")]
        public string NameOfMneGroup { get; set; }
    }
}
