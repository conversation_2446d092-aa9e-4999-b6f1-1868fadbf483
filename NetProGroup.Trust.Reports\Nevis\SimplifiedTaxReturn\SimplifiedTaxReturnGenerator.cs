// <copyright file="SimplifiedTaxReturnGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Nevis.SimplifiedTaxReturn.Populators;

namespace NetProGroup.Trust.Reports.Nevis.SimplifiedTaxReturn
{
    /// <summary>
    /// Generates reports for the simplified tax return module.
    /// </summary>
    public class SimplifiedTaxReturnGenerator : ISimplifiedTaxReturnGenerator
    {
        private const string TemplateName = "simplified-tax-return";

        private readonly ISimplifiedTaxReturnRowPopulator _simplifiedTaxReturnRowPopulator;
        private readonly IExcelTemplateService<SubmissionNevisReportDTO> _excelTemplateService;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="SimplifiedTaxReturnGenerator"/> class.
        /// </summary>
        /// <param name="simplifiedTaxReturnRowPopulator">The submission line populator.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="templateProvider">The template provider.</param>
        public SimplifiedTaxReturnGenerator(
            ISimplifiedTaxReturnRowPopulator simplifiedTaxReturnRowPopulator,
            IExcelTemplateService<SubmissionNevisReportDTO> excelTemplateService,
            IReportTemplateProvider templateProvider)
        {
            _simplifiedTaxReturnRowPopulator = simplifiedTaxReturnRowPopulator;
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
        }

        /// <inheritdoc/>
        public async Task<ReportOutput> GenerateSubmissionsReportAsync(List<SubmissionNevisReportDTO> submissions)
        {
            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionsReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new ReportOutput(stream.ToArray());
        }

        /// <summary>
        /// Creates an Excel report for the given companies.
        /// </summary>
        /// <param name="workbook">The workbook to add the companies to.</param>
        /// <param name="submissions">The submissions to add in the report.</param>
        private void CreateSubmissionsReport(XLWorkbook workbook, List<SubmissionNevisReportDTO> submissions)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per company
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };

            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_simplifiedTaxReturnRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig);
        }
    }
}