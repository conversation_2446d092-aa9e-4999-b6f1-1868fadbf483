// <copyright file="IReportTemplateProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.DataManager.ReportTemplates
{
    /// <summary>
    /// Provider for getting the submission template.
    /// </summary>
    public interface IReportTemplateProvider : IScopedService
    {
        /// <summary>
        /// Gets the excel template with the given name.
        /// </summary>
        /// <param name="templateName">The name of the excel template to get.</param>
        /// <returns>The template.</returns>
        Task<MemoryStream> GetExcelTemplateAsync(string templateName);
    }
}