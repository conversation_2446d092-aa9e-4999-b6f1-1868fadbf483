﻿// <copyright file="ISubmissionsDataManagerFactory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Interface for the SubmissionsDataManagerFactory.
    /// </summary>
    public interface ISubmissionsDataManagerFactory : ITransientService
    {
        /// <summary>
        /// Creates the SubmissionsDataManager for the jurisdiction and module.
        /// </summary>
        /// <param name="jurisdictionCode">The code of the jurisdiction.</param>
        /// <param name="moduleKey">The key of the module.</param>
        /// <returns>The created SubmissionsDataManager or null if not implemented.</returns>
        ICommonSubmissionsDataManager CreateSubmissionsDataManager(string jurisdictionCode, string moduleKey);
    }
}