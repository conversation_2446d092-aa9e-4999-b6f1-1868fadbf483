namespace NetProGroup.Trust.Reports.Nevis.Financial.Values
{
    /// <summary>
    /// Represents the export values for invoices.
    /// </summary>
    public static class InvoiceExportValues
    {
        /// <summary>
        /// Specifies the type of invoice.
        /// </summary>
        public static string InvoiceType => "DR_N";

        /// <summary>
        /// Represents the matter code related to the transaction.
        /// </summary>
        public static string MatterCode => "GE";

        /// <summary>
        /// Defines the currency code for the transaction.
        /// </summary>
        public static string CurCode => "USD";

        /// <summary>
        /// Sets the exchange rate applicable for the currency.
        /// </summary>
        public static string Exchange => "1";

        /// <summary>
        /// Indicates the payment term code.
        /// </summary>
        public static string PayTerm => "O3";

        /// <summary>
        /// Specifies the service code related to the transaction.
        /// </summary>
        public static string ServCode => "RSTR";

        /// <summary>
        /// Provides a description of the service or item in the transaction.
        /// </summary>
        public static string Description => "STR filing fee";

        /// <summary>
        /// Local currency code for the transaction.
        /// </summary>
        public static string LCurCode => "USD";

        /// <summary>
        /// Local exchange rate for the transaction.
        /// </summary>
        public static string LExchange => "1";

        /// <summary>
        /// Specifies the number of units involved in the transaction.
        /// </summary>
        public static string NrUnits => "1";

        /// <summary>
        /// Represents the VAT percentage applied to the transaction.
        /// </summary>
        public static string VATPerc => "0";

        /// <summary>
        /// Specifies the tax code associated with the transaction.
        /// </summary>
        public static string TaxCode => "NAP";
    }
}