﻿// <copyright file="SyncShareholder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DataManager.LegalEntityRelations.Shareholders.RequestResponses
{
    /// <summary>
    /// Represents a Shareholder to sync.
    /// </summary>
    public class SyncShareholder
    {
        /// <summary>
        /// Gets or sets the unique relation id.
        /// </summary>
        public string UniqueRelationId { get; set; }
    }
}
