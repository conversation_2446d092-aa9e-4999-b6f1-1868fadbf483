using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.FeatureFlags;
using NetProGroup.Trust.Application.Contracts.FeatureFlags.Models;
using NetProGroup.Trust.Domain.FeatureFlags.Enum;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.FeatureFlags
{
    public class FeatureFlagsAppServiceTests : TestBase
    {
        private IFeatureFlagsAppService _featureFlagsAppService;

        [SetUp]
        public void Setup()
        {
            _featureFlagsAppService = _server.Services.GetRequiredService<IFeatureFlagsAppService>();
        }

        [Test]
        public async Task GetFeatureFlagsAsync_AnnouncementsFeatureIsEnabled()
        {
            // Act
            var result = await _featureFlagsAppService.GetFeatureFlagsAsync();
            FeatureFlagDTO announcementsFlag = result.FirstOrDefault(f => f.Name == FeatureFlag.Announcements.ToString());

            // Assert
            announcementsFlag.IsEnabled.Should().BeTrue();
        }
    }
}
