using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Announcements;
using NetProGroup.Trust.Domain.FeatureFlags.Enum;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.FeatureFlags
{
    public class FeatureAvailabilityTests : TestBase
    {
        private IFeatureAvailability _featureAvailability;

        [SetUp]
        public void Setup()
        {
            _featureAvailability = _server.Services.GetRequiredService<IFeatureAvailability>();
        }

        [Test]
        public void IsFeatureEnabled_AnnouncementsFeatureIsEnabled()
        {
            // Act
            var isAnnouncementFeatureEnabled = _featureAvailability.IsFeatureEnabled(FeatureFlag.Announcements);

            // Assert
            isAnnouncementFeatureEnabled.Should().BeTrue();
        }
    }
}
