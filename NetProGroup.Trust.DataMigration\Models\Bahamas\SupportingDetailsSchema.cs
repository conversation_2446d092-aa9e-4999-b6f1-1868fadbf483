using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Entry supporting details schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public sealed class SupportingDetailsSchema
    {
        /// <summary>
        /// Gets or sets the user who created the entry.
        /// </summary>
        [BsonElement("support_comment")]
        public string Comment { get; set; }

        /// <summary>
        /// Gets or sets the user who created the entry.
        /// </summary>
        [BsonElement("support_attachments")]
        public List<FileSchema> SupportAttachments { get; set; }
    }
}
