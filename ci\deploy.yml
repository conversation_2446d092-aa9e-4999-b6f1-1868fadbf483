parameters:
# Name of the stage (e.g. DeployToDev)
- name: stageName
  type: string
  default: ''
# Stage dependency
- name: dependsOn
  type: string
  default: ''
# Deployment condition
- name: condition
  type: string
  default: ''
# Azure service connection name
- name: serviceConnection
  type: string
  default: ''
# Target environment (dev, tst, acc, prd)
- name: targetEnvironment
  type: string
  default: ''
# Whether to retain this pipeline run
- name: retain
  type: boolean
  default: false

stages:
- stage: ${{ parameters.stageName }}
  dependsOn: ${{ parameters.dependsOn }}
  condition: ${{ parameters.condition }}
  variables:
  - template: variables/global.yml
  - template: variables/${{ parameters.targetEnvironment }}.yml
  jobs:
  - deployment: Deploy
    pool:
      name: ${{ variables.pool }}
    environment:
        name: ${{ variables.environment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - task: SqlAzureDacpacDeployment@1
            displayName: 'Azure SQL SqlTask - Migration Script'
            inputs:
              azureSubscription: '${{ parameters.serviceConnection }}'
              AuthenticationType: servicePrincipal
              ServerName: '$(serverName)'
              DatabaseName: '$(databaseName)'
              deployType: SqlTask
              SqlFile: '$(Pipeline.Workspace)/$(artifactName)/SQL/migrationscript.sql'

          - task: SqlAzureDacpacDeployment@1
            displayName: 'Azure SQL SqlTask - Manual Tables'
            inputs:
              azureSubscription: '${{ parameters.serviceConnection }}'
              AuthenticationType: servicePrincipal
              ServerName: '$(serverName)'
              DatabaseName: '$(databaseName)'
              deployType: SqlTask
              SqlFile: '$(Pipeline.Workspace)/$(artifactName)/SQL/CreateManualTables.sql'

          - task: AzureRmWebAppDeployment@4
            displayName: 'Azure App Service Deploy: $(webAppName)'
            inputs:
              azureSubscription: '${{ parameters.serviceConnection }}'
              appType: webAppLinux
              WebAppName: '$(webAppName)'
              deployToSlotOrASE: true
              ResourceGroupName: '$(resourceGroup)'
              SlotName: '$(slotName)'
              packageForLinux: '$(Pipeline.Workspace)/$(artifactName)/NetProGroup.Trust.API.zip'
              RuntimeStack: 'DOTNETCORE|8.0'
              StartupCommand: 'dotnet NetProGroup.Trust.API.dll'
              
  - template: retention-template.yml
    parameters:
      condition: ${{ parameters.retain }}
