// <copyright file="WellKnownBVIRoleNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Roles
{
    /// <summary>
    /// Specify the configured application roles for BVI.
    /// </summary>
    public static partial class WellKnownBVIRoleNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore
        /// <summary>
        /// Role is for BVI.
        /// </summary>
        public const string BVI = "BVI";

        /// <summary>
        /// Role for the BVI owner.
        /// </summary>
        public const string BVI_Owner = BVI + ".Owner";

        /// <summary>
        /// Role for the BVI Basic User.
        /// </summary>
        public const string BVI_Basic_User = BVI + ".BasicUser";

        /// <summary>
        /// Role for the BVI CMU SuperUser.
        /// </summary>
        public const string BVI_CMU_SuperUser = BVI + ".CMU.SuperUser";

        /// <summary>
        /// Role for the BVI Officers SuperUser.
        /// </summary>
        public const string BVI_Officers_SuperUser = BVI + ".Officers.SuperUser";

        /// <summary>
        /// Role for the BVI ES SuperUser.
        /// </summary>
        public const string BVI_ES_SuperUser = BVI + ".ES.SuperUser";

        /// <summary>
        /// Role for the BVI COM SuperUser.
        /// </summary>
        public const string BVI_COM_SuperUser = BVI + ".COM.SuperUser";

#pragma warning restore SA1310 // Field names should not contain underscore
    }
}