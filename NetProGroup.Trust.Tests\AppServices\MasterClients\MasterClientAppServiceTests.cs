﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.MasterClients
{
    public class MasterClientAppServiceTests : TestBase
    {
        private IMasterClientsAppService _masterClientsAppService;
        private IMasterClientsRepository _masterClientsRepository;
        private TrustOfficeOptions _trustOfficeOptions;

        [SetUp]
        public void Setup()
        {
            _trustOfficeOptions = _server.Services.GetRequiredService<IOptions<TrustOfficeOptions>>().Value;
            _trustOfficeOptions.AllowedDomains = "contoso.com";
            _masterClientsAppService = _server.Services.GetRequiredService<IMasterClientsAppService>();
            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
        }

        [Test]
        public async Task RemoveUserFromMasterClientAsync_ShouldRemoveUser()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            SetUpUserRolesAsync(ManagementUser, new List<string> { WellKnownRoleNames.Common_SupportUser }).Wait();
            var masterClientUser = await _masterClientsAppService.CreateUserToMasterClientAsync(new CreateMasterClientUserDTO
            {
                MasterClientId = _masterClient.Id,
                EmailAddress = "<EMAIL>",
            });

            var request = new RemoveMasterClientUserDTO
            {
                MasterClientId = _masterClient.Id,
                UserId = masterClientUser.UserId
            };

            // Act
            await _masterClientsAppService.RemoveUserFromMasterClientAsync(request);
            var masterClient = await _masterClientsRepository.GetByIdAsync(_masterClient.Id);

            // Assert
            masterClient.MasterClientUsers.Should().NotContain(u => u.Id == masterClientUser.Id);
        }

        [Test]
        public async Task RemoveUserFromMasterClientAsync_ShouldThrowPermissionException()
        {
            SetWorkContextUser(ManagementUser);
            var request = new RemoveMasterClientUserDTO
            {
                MasterClientId = _masterClient.Id,
                UserId = ClientUser.Id
            };

            // Act
            Func<Task> act = async () => await _masterClientsAppService.RemoveUserFromMasterClientAsync(request);

            // Assert
            await act.Should().ThrowAsync<ForbiddenException>();
        }
    }
}
