// <copyright file="WellKnownBahamasFormDocumentAttibuteKeys.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Shared.FormDocuments
{
    /// <summary>
    /// A list of known attribute keys for FormDocuments.
    /// </summary>
    public static partial class WellKnownFormDocumentAttibuteKeys
    {
        #region Common names

        /// <summary>
        /// Start Date common name.
        /// </summary>
        public const string StartDate = ".startDate";

        /// <summary>
        /// End Date common name.
        /// </summary>
        public const string EndDate = ".endDate";

        /// <summary>
        /// Selected common name.
        /// </summary>
        public const string Selected = ".selected";

        /// <summary>
        /// Label common name.
        /// </summary>
        public const string Label = ".label";

        /// <summary>
        /// Was carried for only part of the period.
        /// </summary>
        public const string CarriedOnForOnlyPartOfFinancialPeriod = ".carriedOnForOnlyPartOfFinancialPeriod";

        /// <summary>
        /// Total gross income common name.
        /// </summary>
        public const string TotalGrossIncome = ".totalGrossIncome";

        /// <summary>
        /// Net book value assets common name.
        /// </summary>
        public const string NetBookValuesAssets = ".netBookValuesAssets";

        /// <summary>
        /// Assets description bahamas common name.
        /// </summary>
        public const string AssetsDescriptionBahamas = "assetsDescriptionBahamas";

        /// <summary>
        /// Is directed and managed in bahamas common name.
        /// </summary>
        public const string IsDirectedAndManagedInBahamas = "isDirectedAndManagedInBahamas";

        /// <summary>
        /// Number of meetings common name.
        /// </summary>
        public const string NumberOfMeetings = "numberOfMeetings";

        /// <summary>
        /// Number of meetings in Bahamas common name.
        /// </summary>
        public const string NumberOfMeetingsInBahamas = "numberOfMeetingsInBahamas";

        /// <summary>
        /// Quorum directors common name.
        /// </summary>
        public const string QuorumDirectors = "quorumDirectors";

        /// <summary>
        /// Quorum directors physically present common name.
        /// </summary>
        public const string QuorumPhysicallyPresent = "quorumPhysicallyPresent";

        /// <summary>
        /// Are the minutes kept in Bahamas common name.
        /// </summary>
        public const string AreMinutesKeptInBahamas = "areMinutesKeptInBahamas";

        /// <summary>
        /// Total expenditure for relevant actity common name.
        /// </summary>
        public const string TotalExpenditureRelevantActivity = "totalExpenditureRelevantActivity";

        /// <summary>
        /// Total expenditure Bahamas common name.
        /// </summary>
        public const string TotalExpenditureBahamas = "totalExpenditureBahamas";

        /// <summary>
        /// Total employees in entity common name.
        /// </summary>
        public const string TotalEmployeesEntity = "totalEmployeesEntity";

        /// <summary>
        /// Total employees for relevant activity common name.
        /// </summary>
        public const string TotalEmployeesRelevantActivity = "totalEmployeesRelevantActivity";

        /// <summary>
        /// Total employees in Bahamas common name.
        /// </summary>
        public const string TotalEmployeesBahamas = "totalEmployeesBahamas";

        /// <summary>
        /// Has ciga common name.
        /// </summary>
        public const string HasCiga = "hasCiga";

        /// <summary>
        /// Bahamas outsourcing income common name.
        /// </summary>
        public const string CigaOutsourcingProportion = "cigaOutsourcingProportion";

        /// <summary>
        /// Bahamas outsourcing expenditure common name.
        /// </summary>
        public const string BahamasOutsourcingExpenditure = "bahamasOutsourcingExpenditure";

        /// <summary>
        /// Is compliant with bahamas laws common name.
        /// </summary>
        public const string IsCompliantWithBahamasLawsAndRegulations = "isCompliantWithBahamasLawsAndRegulations";

        /// <summary>
        /// Is high risk entity common name.
        /// </summary>
        public const string IsHighRiskIpEntity = "isHighRiskIpEntity";

        /// <summary>
        /// Relevant IP asset common name.
        /// </summary>
        public const string RelevantIpAsset = "relevantIpAsset";

        /// <summary>
        /// Income generation explanation common name.
        /// </summary>
        public const string IncomeGenerationExplanation = "incomeGenerationExplanation";

        /// <summary>
        /// Employee responsibility common name.
        /// </summary>
        public const string EmployeeResponsibility = "employeeResponsibility";

        /// <summary>
        /// Strategic decisions bahamas common name.
        /// </summary>
        public const string StrategicDecisionsBahamas = "strategicDecisionsBahamas";

        /// <summary>
        /// Trading activities bahamas common name.
        /// </summary>
        public const string TradingActivitiesBahamas = "tradingActivitiesBahamas";

        /// <summary>
        /// Gross income royalties common name.
        /// </summary>
        public const string GrossIncomeRoyalties = "grossIncomeRoyalties";

        /// <summary>
        /// Gross income sale Ip asset common name.
        /// </summary>
        public const string GrossIncomeSaleIpAsset = "grossIncomeSaleIpAsset";

        /// <summary>
        /// Gross income other sources common name.
        /// </summary>
        public const string GrossIncomeOtherSources = "grossIncomeOtherSources";

        /// <summary>
        /// Business plan explanation common name.
        /// </summary>
        public const string BusinessPlanExplanation = "businessPlanExplanation";

        /// <summary>
        /// Decision making evidence explanation common name.
        /// </summary>
        public const string DecisionMakingEvidenceExplanation = "decisionMakingEvidenceExplanation";

        /// <summary>
        /// Additional compliance explanation common name.
        /// </summary>
        public const string AdditionalComplianceExplanation = "additionalComplianceExplanation";

        /// <summary>
        /// Directors common name.
        /// </summary>
        public const string Directors = "directors.";

        /// <summary>
        /// Employees common name.
        /// </summary>
        public const string Employees = "employees.";

        /// <summary>
        /// Name common name.
        /// </summary>
        public const string Name = "name";

        /// <summary>
        /// Full name common name.
        /// </summary>
        public const string Fullname = "fullName";

        /// <summary>
        /// Is resident in Bahamas common name.
        /// </summary>
        public const string IsResidentInBahamas = "isResidentInBahamas";

        /// <summary>
        /// Relation to entity common name.
        /// </summary>
        public const string RelationToEntity = "relationToEntity";

        /// <summary>
        /// Meeting number common name.
        /// </summary>
        public const string MeetingNumber = "meetingNumber";

        /// <summary>
        /// Physically present in Bahamas common name.
        /// </summary>
        public const string PhysicallyPresentInBahamas = "physicallyPresentInBahamas";

        /// <summary>
        /// Director's qualification common name.
        /// </summary>
        public const string Qualification = "qualification";

        /// <summary>
        /// Years of experience common name.
        /// </summary>
        public const string YearsOfExperience = "yearsOfExperience";

        /// <summary>
        /// Contract type common name.
        /// </summary>
        public const string ContractType = "contractType";

        /// <summary>
        /// Premises common name.
        /// </summary>
        public const string Premises = "premises.";

        /// <summary>
        /// Address line 1 common name.
        /// </summary>
        public const string AddressLine1 = "addressLine1";

        /// <summary>
        /// Address line 2 common name.
        /// </summary>
        public const string AddressLine2 = "addressLine2";

        /// <summary>
        /// Premise country common name.
        /// </summary>
        public const string PremiseCountry = "country";

        /// <summary>
        /// Ciga activity common name.
        /// </summary>
        public const string CigaActivity = "activities.";

        /// <summary>
        /// Outsourcing providers common name.
        /// </summary>
        public const string OutsourcingProviders = "outsourcingProviders.";

        /// <summary>
        /// Entity name common name.
        /// </summary>
        public const string EntityName = "entityName";

        /// <summary>
        /// Details of resources common name.
        /// </summary>
        public const string DetailsOfResources = "detailsOfResources";

        /// <summary>
        /// Number of staff common name.
        /// </summary>
        public const string NumberOfStaff = "numberOfStaff";

        /// <summary>
        /// Monitoring and control common name.
        /// </summary>
        public const string MonitoringAndControl = "monitoringAndControl";

        /// <summary>
        /// Monitoring control explanation common name.
        /// </summary>
        public const string MonitoringControlExplanation = "monitoringControlExplanation";

        /// <summary>
        /// Physical address common name.
        /// </summary>
        public const string PhysicalAddress = "physicalAddress";

        /// <summary>
        /// Immediate parent entities common name.
        /// </summary>
        public const string ImmediateParentEntities = "immediateParentEntities.";

        /// <summary>
        /// Ultimate parent entities common name.
        /// </summary>
        public const string UltimateParentEntities = "ultimateParentEntities.";

        /// <summary>
        /// Alternative name common name.
        /// </summary>
        public const string AlternativeName = "alternativeName";

        /// <summary>
        /// Jurisdiction of formation common name.
        /// </summary>
        public const string JurisdictionOfFormation = "jurisdictionOfFormation";

        /// <summary>
        /// Incorporation number common name.
        /// </summary>
        public const string IncorporationNumber = "incorporationNumber";

        /// <summary>
        /// Taxpayer identification number common name.
        /// </summary>
        public const string TaxpayerIdentificationNumber = "taxpayerIdentificationNumber";

        #endregion

        #region Relevant activity keys

        /// <summary>
        /// The relevant activity is 'Holding Business'.
        /// </summary>
        public const string HoldingBusiness = "holding-business.";

        /// <summary>
        /// The relevant activity is 'Finance and Leasing Business'.
        /// </summary>
        public const string FinanceLeasingBusiness = "finance-leasing-business.";

        /// <summary>
        /// The relevant activity is 'Banking Business'.
        /// </summary>
        public const string BankingBusiness = "banking-business.";

        /// <summary>
        /// The relevant activity is 'Insurance Business'.
        /// </summary>
        public const string InsuranceBusiness = "insurance-business.";

        /// <summary>
        /// The relevant activity is 'Fund Management Business'.
        /// </summary>
        public const string FundManagementBusiness = "fund-management-business.";

        /// <summary>
        /// The relevant activity is 'Headquarters Business'.
        /// </summary>
        public const string HeadquartersBusiness = "headquarters-business.";

        /// <summary>
        /// The relevant activity is 'Shipping Business'.
        /// </summary>
        public const string ShippingBusiness = "shipping-business.";

        /// <summary>
        /// The relevant activity is 'Intellectual Propertry Business'.
        /// </summary>
        public const string IntellectualPropertyBusiness = "intellectual-property-business.";

        /// <summary>
        /// The relevant activity is 'Distribution and Service Centre Business'.
        /// </summary>
        public const string DistributionAndServiceCentreBusiness = "distribution-service-centre-business.";

        /// <summary>
        /// Relevant activity 'None'.
        /// </summary>
        public const string None = "none";

        #endregion

        #region FormDocument keys

        /// <summary>
        /// Relevant activity partial financial period.
        /// </summary>
        public const string PartialFinancialPeriod = "partial-financial-period";

        /// <summary>
        /// The entity unique id.
        /// </summary>
        public const string EntityId = "entity-details.entityId";

        /// <summary>
        /// The entity tax payer unique id.
        /// </summary>
        public const string EntityTaxPayerId = "entity-details.entityTin";

        /// <summary>
        /// Determine if the physical address is the same as the registered one.
        /// </summary>
        public const string EntityHasSameAddress = "entity-details.sameAddress";

        /// <summary>
        /// The entity address line.
        /// </summary>
        public const string EntityAddress = "entity-details.streetNumberNameCity";

        /// <summary>
        /// The entity apt/unit.
        /// </summary>
        public const string EntityAptUnit = "entity-details.aptUnit";

        /// <summary>
        /// The entity country.
        /// </summary>
        public const string EntityCountry = "entity-details.country";

        /// <summary>
        /// Determine if this is the first financial report.
        /// </summary>
        public const string IsFirstFinancialReport = "financial-period.firstFinancialReport";

        /// <summary>
        /// The financial period start date.
        /// </summary>
        public const string FinancialPeriodStartAt = "financial-period.startDate";

        /// <summary>
        /// The financial period end date.
        /// </summary>
        public const string FinancialPeriodEndAt = "financial-period.endDate";

        /// <summary>
        /// Determine if the entity is subject to reclassification.
        /// </summary>
        public const string IsReclassifiedToPEH = "financial-period.isReclassifiedToPEH";

        /// <summary>
        /// The tax payer id module key.
        /// </summary>
        public const string TaxPayerIdentification = "tax-payer-identification.";

        /// <summary>
        /// The Additional comments.
        /// </summary>
        public const string AdditionalComments = "supporting-details.additionalComments";

        #endregion
    }
}