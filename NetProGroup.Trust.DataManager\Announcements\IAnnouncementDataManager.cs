// <copyright file="IAnnouncementDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.Application.Contracts.Documents;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Announcements
{
    /// <summary>
    /// Interface for the datamanager for announcements.
    /// </summary>
    public interface IAnnouncementDataManager : IScopedService
    {
        /// <summary>
        /// Retrieve the created announcements in a paginated response.
        /// </summary>
        /// <param name="data">The necessary data used to filter announcements.</param>
        /// <returns>A <see cref="ListAnnouncementDTO"/> representing the created entity.</returns>
        Task<IPagedList<ListAnnouncementDTO>> FilterAnnouncementsAsync(FilterAnnouncementsDTO data);

        /// <summary>
        /// Creates or updates an announcement entity.
        /// </summary>
        /// <param name="data">The necessary data used to create or update an announcement.</param>
        /// <returns>A <see cref="Guid"/> representing the created or updated entity Id.</returns>
        Task<Guid> CreateUpdateAnnouncementAsync(CreateUpdateAnnouncementDTO data);

        /// <summary>
        /// Creates an announcement document entity.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="data">The necessary dataset used to create an announcement document.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CreateAnnouncementDocumentAsync(Guid announcementId, CreateAnnouncementDocumentDTO data);

        /// <summary>
        /// Deletes an announcement document entity.
        /// </summary>
        /// <param name="announcementDocumentId">The announcementDocument id as Guid.</param>
        /// <param name="uploadComplete">The value indicating whether the upload of documents is finished or not.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteAnnouncementDocumentAsync(Guid announcementDocumentId, bool uploadComplete);

        /// <summary>
        /// Retrieve an announcement by its id.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <returns>A <see cref="AnnouncementDTO"/> representing the announcement.</returns>
        Task<AnnouncementDTO> GetAnnouncementByIdAsync(Guid announcementId);

        /// <summary>
        /// Send an announcement entity.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="saveChanges">Whether to save the dB changes immediately.</param>
        /// <param name="sendToActiveMasterClients">The flag to determine if the announcement needs to be send only to active master clients (MC with active legal entities).</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendAnnouncementAsync(Guid announcementId, bool saveChanges = false, bool sendToActiveMasterClients = false);

        /// <summary>
        /// Deletes an announcement entity.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteAnnouncementAsync(Guid announcementId);

        /// <summary>
        /// Gets the announcements that have the Scheduled status.
        /// </summary>
        /// <returns>A list of announcements.</returns>
        Task<List<AnnouncementDTO>> GetScheduledAnnouncementsAsync();
    }
}