﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Announcements : TestBase
    {
        private IAnnouncementDataManager _announcementDataManager;
        private IInboxDataManager _inboxDataManager;
        private Mock<IDateTimeProvider> _dateTimeProviderMock;

        [SetUp]
        public void Setup()
        {
            _announcementDataManager = _server.Services.GetRequiredService<IAnnouncementDataManager>();
            _inboxDataManager = _server.Services.GetRequiredService<IInboxDataManager>();
            _dateTimeProviderMock = new Mock<IDateTimeProvider>();
        }

        [Test]
        public async Task CreateUpdateAnnouncementAsync_InboxMessage_MarkAsRead()
        {
            // Arrange
            CreateUpdateAnnouncementDTO createDto = GetCreateUpdateDTO(sendNow: true, _masterClient.Code);

            //Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Act
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow);

            //Get the inbox messages status 
            var readMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            // Assert
            readMessage.IsRead.Should().BeTrue("The inbox message should be marked as read after creating the read status.");
        }

        [Test]
        public async Task UpdateAnnouncement_ShouldNotChangeStatusFromScheduledToDraft()
        {
            // Arrange
            CreateUpdateAnnouncementDTO createDto = GetCreateUpdateDTO(masterClientCode: _masterClient.Code);

            var announcementId = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);
            var announcement =
                (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x =>
                    x.Id == announcementId);
            var originalStatus = announcement.Status;
            originalStatus.Should().Be(AnnouncementStatus.Scheduled);

            // Update the announcement with changes (should not revert to Draft)
            createDto.Id = announcementId;
            createDto.Subject = "Updated Scheduled Announcement";
            createDto.Body = "This is an updated scheduled announcement body.";

            // Act
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Assert
            var updatedAnnouncement =
                (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x =>
                    x.Id == announcementId);
            updatedAnnouncement.Status.Should().Be(originalStatus,
                "Status should not change from Scheduled to Draft when updating announcement.");
        }

        [Test]
        public async Task FilterAnnouncements_MultipleMasterClients_MapsOnlyCorrectMasterClientCodes()
        {
            // Arrange
            const string masterClientCode1 = "1";
            const string masterClientCode2 = "2";
            var masterClient1 = await CreateMasterClient(masterClientCode1);
            var masterClient2 = await CreateMasterClient(masterClientCode2);
            var createDto1 = GetCreateUpdateDTO(masterClientCode: masterClient1.Code);
            var createDto2 = GetCreateUpdateDTO(masterClientCode: masterClient2.Code);

            // Create announcement
            var announcement1Id = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);

            // Act
            var result = await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO());

            var actualAnnouncement = result.Should().HaveCount(2).And.ContainSingle(dto => dto.Id == announcement1Id).Subject;
            actualAnnouncement.MasterClientCodes.Should().ContainSingle()
                .Which.Should().Be(masterClientCode1, "The code should match the master client that was set as a recipient.");
        }

        [Test]
        public async Task FilterAnnouncements_MultipleJurisdictions_MapsOnlyCorrectJurisdictionNames()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            await SetUpUserRolesAsync(ManagementUser, [WellKnownRoleNames.Bahamas_Owner, WellKnownRoleNames.Nevis_Owner]);
            var createDto1 = GetCreateUpdateDTO(jurisdictionId: JurisdictionBahamasId);
            var createDto2 = GetCreateUpdateDTO(jurisdictionId: JurisdictionNevisId);

            // Create announcement
            var announcement1Id = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);

            // Act
            var result = await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO());

            var actualAnnouncement = result.Should().HaveCount(2).And.ContainSingle(dto => dto.Id == announcement1Id).Subject;
            actualAnnouncement.JurisdictionNames.Should().ContainSingle()
                .Which.Should().Be("Bahamas", "The name should match the jurisdiction that was set as a recipient.");
        }

        [Test]
        public async Task GetAnnouncementByIdAsync_MultipleMasterClients_MapsOnlyCorrectMasterClientCodes()
        {
            // Arrange
            const string masterClientCode1 = "1";
            const string masterClientCode2 = "2";
            var masterClient1 = await CreateMasterClient(masterClientCode1);
            var masterClient2 = await CreateMasterClient(masterClientCode2);
            var createDto1 = GetCreateUpdateDTO(masterClientCode: masterClient1.Code);
            var createDto2 = GetCreateUpdateDTO(masterClientCode: masterClient2.Code);

            // Create announcements
            var announcement1Id = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);

            // Act
            var result = await _announcementDataManager.GetAnnouncementByIdAsync(announcement1Id);

            // Assert
            result.MasterClientCodes.Should().ContainSingle()
                .Which.Should().Be(masterClientCode1, "The code should match the master client that was set as a recipient.");
        }

        [Test]
        public async Task GetAnnouncementByIdAsync_MultipleJurisdictions_MapsOnlyCorrectJurisdictionNames()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            await SetUpUserRolesAsync(ManagementUser, [WellKnownRoleNames.Bahamas_Owner, WellKnownRoleNames.Nevis_Owner]);
            var createDto1 = GetCreateUpdateDTO(jurisdictionId: JurisdictionBahamasId);
            var createDto2 = GetCreateUpdateDTO(jurisdictionId: JurisdictionNevisId);

            // Create announcements
            var announcement1Id = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);

            // Act
            var result = await _announcementDataManager.GetAnnouncementByIdAsync(announcement1Id);

            // Assert
            result.JurisdictionNames.Should().ContainSingle()
                .Which.Should().Be("Bahamas", "The name should match the jurisdiction that was set as a recipient.");
        }

        [Test]
        public async Task GetAnnouncementAsync_ShouldReturnPastScheduleAnnouncements_RegardlessOfJurisdiction()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            await SetUpUserRolesAsync(ManagementUser, [WellKnownRoleNames.Bahamas_Owner, WellKnownRoleNames.Nevis_Owner]);
            var scheduledDate = DateTime.UtcNow.AddMinutes(-10);
            var currentDateTime = DateTime.UtcNow;
            _dateTimeProviderMock.Setup(provider => provider.UtcNow).Returns(currentDateTime);

            var createDto1 = GetCreateUpdateDTO(jurisdictionId: JurisdictionBahamasId, scheduledDate: scheduledDate);
            var createDto2 = GetCreateUpdateDTO(jurisdictionId: JurisdictionNevisId, scheduledDate: scheduledDate);
            var createDto3 = GetCreateUpdateDTO(jurisdictionId: JurisdictionNevisId, scheduledDate: scheduledDate.AddHours(2));
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto3);

            // Act
            var result = await _announcementDataManager.GetScheduledAnnouncementsAsync();

            // Assert
            result.Should().NotBeEmpty();
            result.Should().HaveCount(2);
            result[0].SendAt.Should().Be(scheduledDate);
            result[1].SendAt.Should().Be(scheduledDate);
        }

        private CreateUpdateAnnouncementDTO GetCreateUpdateDTO(bool sendNow = false, string masterClientCode = null, Guid? jurisdictionId = null, DateTime? scheduledDate = null)
        {
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = sendNow,
                SendAt = scheduledDate ?? DateTime.UtcNow.AddMinutes(10),
                MasterClientCodes = masterClientCode != null ? [masterClientCode] : [],
                SendToAllMasterClients = jurisdictionId != null,
                SendToAllJurisdictions = false,
                JurisdictionId = jurisdictionId ?? JurisdictionBahamasId,
                UserIds = [ClientUser.Id]
            };
            return createDto;
        }

        private async Task<MasterClient> CreateMasterClient(string code)
        {
            var masterClient = new MasterClient { Code = code, Name = $"Test Master Client {code}" };

            await _server.Services.GetRequiredService<IMasterClientsRepository>().InsertAsync(masterClient, true);
            return masterClient;
        }
    }
}