// <copyright file="CreateUpdateAnnouncementDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Announcements
{
    /// <summary>
    /// Represent the necessary data to create or update an announcement.
    /// </summary>
    public class CreateUpdateAnnouncementDTO : EntityDTO<Guid?>
    {
        /// <summary>
        /// Gets or sets the announcement subject.
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets the email subject.
        /// </summary>
        public string EmailSubject { get; set; }

        /// <summary>
        /// Gets or sets the body of the announcement.
        /// </summary>
        public string Body { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the announcement will include documents.
        /// </summary>
        /// <remarks>
        /// If the value is true the announcement status will be set to Draft.
        /// </remarks>
        public bool IncludeAttachments { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the announcement is going to be send now or not.
        /// </summary>
        public bool SendNow { get; set; }

        /// <summary>
        /// Gets or sets the send date for the announcement.
        /// </summary>
        public DateTime? SendAt { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the announcement is going to be linked to all master clients.
        /// </summary>
        public bool SendToAllMasterClients { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the announcement is going to be sent to active master clients only.
        /// </summary>
        public bool SendToActiveMasterClients { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the announcement is going to be linked to all jurisdictions.
        /// </summary>
        public bool SendToAllJurisdictions { get; set; }

        /// <summary>
        /// Gets or sets the code of the master clients related to the announcement.
        /// </summary>
        public List<string> MasterClientCodes { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the id of the legal entities related to the announcement.
        /// </summary>
        public List<Guid> LegalEntityIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the id of the jurisdiction related to the announcement.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the id of the users related to the announcement.
        /// </summary>
        public List<Guid> UserIds { get; set; } = new List<Guid>();
    }
}