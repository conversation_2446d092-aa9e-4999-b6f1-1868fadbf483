﻿// <copyright file="SyncDetails.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Scheduling
{
    /// <summary>
    /// Details of a sync operation.
    /// </summary>
    public class SyncDetails
    {
        /// <summary>
        /// Gets or sets the last successful sync.
        /// </summary>
        public DateTime LastSuccessfulSync { get; set; }

        /// <summary>
        /// Gets or sets the jurisdictions used.
        /// </summary>
        public List<string> JurisdictionsUsed { get; set; }

        /// <summary>
        /// Gets or sets the number of records updated.
        /// </summary>
        public int UpdatedCount { get; set; }

        /// <summary>
        /// Gets or sets the number of records deleted.
        /// </summary>
        public int DeletedCount { get; set; }
    }
}