﻿// <copyright file="ListSubmissionsByMasterClientRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.Submissions.RequestResponses
{
    /// <summary>
    /// A request model for the submissions datamanager to search for submissions.
    /// </summary>
    public class ListSubmissionsByMasterClientRequest
    {
        /// <summary>
        /// Gets or sets the info for paging.
        /// </summary>
        public PagingInfo PagingInfo { get; set; }

        /// <summary>
        /// Gets or sets the financial yeara to search the submisisons for.
        /// </summary>
        public List<int> FinancialYears { get; set; } = new List<int>();

        /// <summary>
        /// Gets or sets an indication whether the submission must be paid or unpaid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets the ids of the master clients to search the submissions for (via companies).
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the list of legal entity IDs.
        /// </summary>
        public List<Guid> LegalEntityIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets the value indicating whether the submission has an invoice.
        /// </summary>
        public bool? HasInvoice { get; set; }

        /// <summary>
        /// Gets or sets the list of submission statuses to filter by.
        /// </summary>
        public List<SubmissionStatus> SubmissionStatuses { get; set; }
    }
}
