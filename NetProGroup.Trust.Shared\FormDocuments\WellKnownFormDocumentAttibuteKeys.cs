// <copyright file="WellKnownFormDocumentAttibuteKeys.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Shared.FormDocuments
{
    /// <summary>
    /// A list of known attribute keys for FormDocuments.
    /// </summary>
    public static partial class WellKnownFormDocumentAttibuteKeys
    {
        /// <summary>
        /// Use accounting tools.
        /// </summary>
        public const string UseAccountingTool = "financial-period.tridentAccountingRecordsTool";

        /// <summary>
        /// Company main activity.
        /// </summary>
        public const string CompanyMainActivity = "financial-period.companyActivity";

        /// <summary>
        /// First financial report.
        /// </summary>
        public const string FirstFinancialReport = "financial-period.firstFinancialReport";

        /// <summary>
        /// Number of authorized shares.
        /// </summary>
        public const string AuthorizeSharedNo = "equity-details.noAuthorizedShares";

        /// <summary>
        /// Number of issued shares.
        /// </summary>
        public const string IssuedSharedNo = "equity-details.noIssuedShares";

        /// <summary>
        /// Value per share.
        /// </summary>
        public const string ValuePerShare = "equity-details.parValuePerShare";

        /// <summary>
        /// Equity paid in cash.
        /// </summary>
        public const string EquityPaidInCash = "equity-details.paidInCash";

        /// <summary>
        /// Equity property transfer.
        /// </summary>
        public const string EquityPropertyTransfer = "equity-details.propertyTransfer";

        /// <summary>
        /// Equity securities transfer.
        /// </summary>
        public const string EquitySecuritiesTransfer = "equity-details.securitiesTransfer";

        /// <summary>
        /// Equity paid cash amount.
        /// </summary>
        public const string EquityPaidCash = "equity-details.cashAmount";

        /// <summary>
        /// Equity securities value.
        /// </summary>
        public const string EquitySecuritiesValue = "equity-details.securitiesValue";

        /// <summary>
        /// Total interest income.
        /// </summary>
        public const string TotalInterestIncome = "income-details.totalInterestIncome";

        /// <summary>
        /// Total dividend income.
        /// </summary>
        public const string TotalDividendIncome = "income-details.totalDividendIncome";

        /// <summary>
        /// Total other income.
        /// </summary>
        public const string TotalOtherIncome = "income-details.otherIncome";

        /// <summary>
        /// Ending interest receivable.
        /// </summary>
        public const string EndingInterestReceivable = "income-details.endingInterestReceivable";

        /// <summary>
        /// Other ending interest receivable.
        /// </summary>
        public const string OtherEndingInterestReceivable = "income-details.otherEndingInterestReceivable";

        /// <summary>
        /// Beginning interest receivable.
        /// </summary>
        public const string BeginningInterestReceivable = "income-details.beginningInterestReceivable";

        /// <summary>
        /// Other beginning interest receivable.
        /// </summary>
        public const string OtherBeginningInterestreceivable = "income-details.otherBeginningInterestReceivable";

        /// <summary>
        /// Total proceeds.
        /// </summary>
        public const string TotalProceeds = "income-details.totalProceeds";

        /// <summary>
        /// Total market value.
        /// </summary>
        public const string TotalMarketValue = "income-details.totalMarketValue";

        /// <summary>
        /// Total purchase cost.
        /// </summary>
        public const string TotalPurchaseCost = "income-details.totalPurchaseCost";

        /// <summary>
        /// Portfolio management fees period.
        /// </summary>
        public const string PortfolioManagementFeesPeriod = "expense-details.portfolioManagementFeesPeriod";

        /// <summary>
        /// Company administration fees period.
        /// </summary>
        public const string CompanyAdministrationFeesPeriod = "expense-details.companyAdministrationFeesPeriod";

        /// <summary>
        /// Loan interest payments.
        /// </summary>
        public const string LoanInterestPayments = "expense-details.loanInterestPayments";

        /// <summary>
        /// Bank charges.
        /// </summary>
        public const string BankCharges = "expense-details.bankCharges";

        /// <summary>
        /// Tax withheld.
        /// </summary>
        public const string TaxWithheld = "expense-details.taxWithheld";

        /// <summary>
        /// Portfolio management fees.
        /// </summary>
        public const string PortfolioManagementFees = "expense-details.portfolioManagementFees";

        /// <summary>
        /// Company administration fees.
        /// </summary>
        public const string CompanyAdministrationFees = "expense-details.companyAdministrationFees";

        /// <summary>
        /// Other company expenses.
        /// </summary>
        public const string OtherCompanyExpenses = "expense-details.otherCompanyExpenses";

        /// <summary>
        /// Beginning interest payable.
        /// </summary>
        public const string BeginningInterestPayable = "expense-details.beginningInterestPayable";

        /// <summary>
        /// Other period paid expenses.
        /// </summary>
        public const string OtherPeriodPaidExpenses = "expense-details.otherPeriodPaidExpenses";

        /// <summary>
        /// Ending interest payable loans.
        /// </summary>
        public const string EndingInterestPayableLoans = "expense-details.endingInterestPayableLoans";

        /// <summary>
        /// Other period not paid expenses.
        /// </summary>
        public const string OtherPeriodNotPaidExpenses = "expense-details.otherPeriodNotPaidExpenses";

        /// <summary>
        /// Dividends paid shareholders.
        /// </summary>
        public const string DividendsPaidShareholders = "expense-details.dividendsPaidShareholders";

        /// <summary>
        /// Ddividends not paid shareholders.
        /// </summary>
        public const string DividendsNotPaidShareholders = "expense-details.dividendsNotPaidShareholders";

        /// <summary>
        /// Total payments purchase securities investments.
        /// </summary>
        public const string TotalPaymentsPurchaseSecuritiesInvestments = "expense-details.totalPaymentsPurchaseSecuritiesInvestments";

        /// <summary>
        /// Company liabilities.
        /// </summary>
        public const string CompanyLiabilities = "liabilities-details.companyLiabilities";

        /// <summary>
        /// Loans.
        /// </summary>
        public const string Loans = "liabilities-details.loans";

        /// <summary>
        /// Account payable accrual.
        /// </summary>
        public const string AccountPayableAccrual = "liabilities-details.accountPayableAccrual";

        /// <summary>
        /// Other liabilities.
        /// </summary>
        public const string OtherLiabilities = "liabilities-details.otherLiabilities";

        /// <summary>
        /// Company assets.
        /// </summary>
        public const string CompanyAssets = "non-current-assets-details.companyAssets";

        /// <summary>
        /// Fixed assets.
        /// </summary>
        public const string FixedAssets = "non-current-assets-details.fixedAssets";

        /// <summary>
        /// Assets paid in cash.
        /// </summary>
        public const string AssetsPaidInCash = "non-current-assets-details.assetsPaidInCash";

        /// <summary>
        /// Amount paid.
        /// </summary>
        public const string AmountPaid = "non-current-assets-details.amountPaid";

        /// <summary>
        /// Company cash.
        /// </summary>
        public const string CompanyCash = "current_assets_details.companyCash";

        /// <summary>
        /// Cash bank accounts.
        /// </summary>
        public const string CashBankAccounts = "current_assets_details.cashBankAccounts";

        /// <summary>
        /// Cash setup capital.
        /// </summary>
        public const string CashSetupCapital = "current_assets_details.cashSetupCapital";

        /// <summary>
        /// Cash received income.
        /// </summary>
        public const string CashReceivedIncome = "current_assets_details.cashReceivedIncome";

        /// <summary>
        /// Cash paid expenses.
        /// </summary>
        public const string CashPaidExpenses = "current_assets_details.cashPaidExpenses";

        /// <summary>
        /// Cash received paid loans.
        /// </summary>
        public const string CashReceivedPaidLoans = "current_assets_details.cashReceivedPaidLoans";

        /// <summary>
        /// Cash received investments sold.
        /// </summary>
        public const string CashReceivedInvestmentsSold = "current_assets_details.cashReceivedInvestmentsSold";

        /// <summary>
        /// Cash paid investments purchased.
        /// </summary>
        public const string CashPaidInvestmentsPurchased = "current_assets_details.cashPaidInvestmentsPurchased";

        /// <summary>
        /// Cash received sale paid non current assets.
        /// </summary>
        public const string CashReceivedSalePaidNonCurrentAssets = "current_assets_details.cashReceivedSalePaidNonCurrentAssets";

        /// <summary>
        /// Review previous info.
        /// </summary>
        public const string ReviewPreviousInfo = "current_assets_details.reviewPreviousInfo";

        /// <summary>
        /// Financial reports accurate.
        /// </summary>
        public const string FinancialReportsAccurate = "financial-reports-details.financialReportsAccurate";

        /// <summary>
        /// Value per share.
        /// </summary>
        public const string ConfirmationDeclaration = "finalize.confirmationDeclaration";

        /// <summary>
        /// Confirmation assets.
        /// </summary>
        public const string ConfirmationAssets = "finalize.confirmationAssets";

        /// <summary>
        /// Confirmation funds.
        /// </summary>
        public const string ConfirmationFunds = "finalize.confirmationFunds";

        /// <summary>
        /// Confirmation reports.
        /// </summary>
        public const string ConfirmationReports = "finalize.confirmationReports";

        /// <summary>
        /// Confirmation tax advice.
        /// </summary>
        public const string ConfirmationTaxAdvice = "finalize.confirmationTaxAdvice";

        /// <summary>
        /// Confirmation fee acknowledgement.
        /// </summary>
        public const string ConfirmationFeeAcknowledgement = "finalize.confirmationFeeAcknowledgement";

        /// <summary>
        /// Declarant name.
        /// </summary>
        public const string DeclarantName = "finalize.declarantName";

        /// <summary>
        /// Entity relation.
        /// </summary>
        public const string EntityRelation = "finalize.entityRelation";

        /// <summary>
        /// Other entity relation.
        /// </summary>
        public const string OtherEntityRelation = "finalize.otherEntityRelation";

        /// <summary>
        /// Prefix.
        /// </summary>
        public const string Prefix = "finalize.telephone.prefix";

        /// <summary>
        /// PhoneNumber.
        /// </summary>
        public const string PhoneNumber = "finalize.telephone.number";

        /// <summary>
        /// Value per share.
        /// </summary>
        public const string DeclarantEmail = "finalize.email";

        /// <summary>
        /// Other incomes.
        /// </summary>
        public const string OtherIncomes = "income-details.otherIncomes";

        /// <summary>
        /// Equity properties.
        /// </summary>
        public const string EquityProperties = "equity-details.properties";

        /// <summary>
        /// Financial Period.
        /// </summary>
        public const string FinancialPeriod = "financial-period";

        #region Common names

        /// <summary>
        /// Description common name.
        /// </summary>
        public const string Description = ".description";

        /// <summary>
        /// Amount common name.
        /// </summary>
        public const string Amount = ".amount";

        /// <summary>
        /// Current common name.
        /// </summary>
        public const string Current = ".current";

        /// <summary>
        /// NonCurrent common name.
        /// </summary>
        public const string NonCurrent = ".nonCurrent";

        /// <summary>
        /// Purchase cost common name.
        /// </summary>
        public const string PurchaseCost = ".purchaseCost";

        /// <summary>
        /// Assessed value common name.
        /// </summary>
        public const string AssessedValue = ".assessedValue";

        /// <summary>
        /// Purchase year common name.
        /// </summary>
        public const string PurchaseYear = ".purchaseYear";

        /// <summary>
        /// Account name common name.
        /// </summary>
        public const string AccountName = ".accountType";

        /// <summary>
        /// Bank name common name.
        /// </summary>
        public const string BankName = ".bankName";

        /// <summary>
        /// Type common name.
        /// </summary>
        public const string Type = ".type";

        /// <summary>
        /// Other type common name.
        /// </summary>
        public const string OtherType = ".otherType";

        /// <summary>
        /// Value common name.
        /// </summary>
        public const string Value = ".value";

        #endregion
    }
}