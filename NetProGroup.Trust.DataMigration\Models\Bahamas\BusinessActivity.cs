using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a business activity in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class BusinessActivity
    {
        /// <summary>
        /// Gets or sets the unique identifier for the business activity.
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the start date of the business activity.
        /// </summary>
        [BsonElement("from")]
        public DateTime From { get; set; }

        /// <summary>
        /// Gets or sets the end date of the business activity.
        /// </summary>
        [BsonElement("to")]
        public DateTime To { get; set; }

        /// <summary>
        /// Gets or sets the activity description.
        /// </summary>
        [BsonElement("activity")]
        public string Activity { get; set; }

        /// <summary>
        /// Gets or sets the type of the business activity.
        /// </summary>
        [BsonElement("type")]
        public string Type { get; set; }
    }
}
