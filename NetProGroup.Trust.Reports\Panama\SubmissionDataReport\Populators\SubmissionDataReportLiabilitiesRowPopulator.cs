// <copyright file="SubmissionDataReportLiabilitiesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportLiabilitiesRowPopulator : LinePopulatorBase, ISubmissionDataReportLiabilitiesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the administrative and operational liabilities
            var loanLiabilities = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.Loans, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (loanLiabilities.Count > 1)
            {
                // Group the other incomes by the index
                var loanLiabilityGroups = loanLiabilities.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.Loans + ".")[1].Split(".")[0]);

                foreach (var group in loanLiabilityGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the liability type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Loans");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the liability values
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    SetCellValueAndStyle(worksheet, currentRow, 7, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.NonCurrent, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }

            // Retrieve the accounts payable liabilities
            var accountPayableLiabilities = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.AccountPayableAccrual, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (accountPayableLiabilities.Count > 1)
            {
                // Group the other incomes by the index
                var accountPayableLiabilityGroups = accountPayableLiabilities.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.AccountPayableAccrual + ".")[1].Split(".")[0]);

                foreach (var group in accountPayableLiabilityGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the liability type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Accounts Payable and Accrual");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the liability values
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    SetCellValueAndStyle(worksheet, currentRow, 7, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.NonCurrent, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }

            // Retrieve the other liabilities
            var otherLiabilities = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherLiabilities, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (otherLiabilities.Count > 1)
            {
                // Group the other incomes by the index
                var otherLiabilityGroups = otherLiabilities.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherLiabilities + ".")[1].Split(".")[0]);

                foreach (var group in otherLiabilityGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the liability type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Other Liabilities");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the liability values
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Current, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    SetCellValueAndStyle(worksheet, currentRow, 7, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.NonCurrent, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }
        }
    }
}