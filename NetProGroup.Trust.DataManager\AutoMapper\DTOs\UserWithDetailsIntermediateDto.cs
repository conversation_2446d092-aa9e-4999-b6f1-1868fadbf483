﻿// <copyright file="UserWithDetailsIntermediateDto.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Users;

namespace NetProGroup.Trust.DataManager.AutoMapper.DTOs
{
    /// <summary>
    /// Intermediate class to support Automapper Projection.
    /// This is needed because there is no EF navigation property to the roles where we can get the role names.
    /// So we first join the user and its roles in this class, and then we can project to the DTOs we need.
    /// </summary>
    internal sealed class UserWithDetailsIntermediateDto
    {
        /// <summary>
        /// Gets or sets the user.
        /// </summary>
        public ApplicationUser User { get; set; }

        /// <summary>
        /// Gets or sets the collection of <see cref="MasterClientUser"/> entities associated with the user.
        /// </summary>
        public IEnumerable<MasterClientUser> MasterClientUsers { get; set; }

        /// <summary>
        /// Gets or sets the collection of <see cref="UserAttribute"/> entities associated with the user.
        /// </summary>
        public IEnumerable<UserAttribute> Attributes { get; set; }
    }
}