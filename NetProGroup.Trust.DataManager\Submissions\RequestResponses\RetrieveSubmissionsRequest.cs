// <copyright file="RetrieveSubmissionsRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.DataManager.Submissions.RequestResponses
{
    /// <summary>
    /// A request model for the submissions datamanager to search for submissions given a list of ids.
    /// </summary>
    public class RetrieveSubmissionsRequest : IJurisdictionFilteredRequest
    {
        /// <summary>
        /// Gets or sets the ids of the submission to search.
        /// </summary>
        public List<Guid> SubmissionIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Gets or sets a value indicating whether to include deleted submissions in the search results.
        /// </summary>
        public bool IncludeDeleted { get; set; }

        /// <summary>
        /// Gets or sets the ids of the jurisdictions to search the submissions for (via companies).
        /// </summary>
        public List<Guid> AuthorizedJurisdictionIDs { get; set; } = new List<Guid>();
    }
}