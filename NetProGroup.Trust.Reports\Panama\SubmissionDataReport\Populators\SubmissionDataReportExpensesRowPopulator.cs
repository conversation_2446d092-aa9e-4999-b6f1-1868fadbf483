// <copyright file="SubmissionDataReportExpensesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportExpensesRowPopulator : LinePopulatorBase, ISubmissionDataReportExpensesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the other company expenses
            var otherExpenses = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherCompanyExpenses, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (otherExpenses.Count > 1)
            {
                // Group the other incomes by the index
                var otherExpenseGroups = otherExpenses.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherCompanyExpenses + ".")[1].Split(".")[0]);

                foreach (var group in otherExpenseGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the expense type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Other company expense");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the expense value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }

            // Retrieve the other expense paid
            var otherPaidExpenses = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherPeriodPaidExpenses, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (otherPaidExpenses.Count > 1)
            {
                // Group the other incomes by the index
                var otherPaidExpenseGroups = otherPaidExpenses.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherPeriodPaidExpenses + ".")[1].Split(".")[0]);

                foreach (var group in otherPaidExpenseGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the expense type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Expense paid");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the expense value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }

            // Retrieve the other prior period income received
            var otherNotPaidExpenses = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherPeriodNotPaidExpenses, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (otherNotPaidExpenses.Count > 1)
            {
                // Group the other incomes by the index
                var otherNonPaidExpenseGroups = otherNotPaidExpenses.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherPeriodNotPaidExpenses + ".")[1].Split(".")[0]);

                foreach (var group in otherNonPaidExpenseGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the expense type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Expense not paid");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the expense value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }
        }
    }
}