using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a payment schema in the old database.
    /// This class contains details about a payment transaction.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class PaymentSchema
    {
        /// <summary>
        /// Gets or sets the payment type.
        /// </summary>
        [BsonElement("payment_type")]
        public string PaymentType { get; set; }

        /// <summary>
        /// Gets or sets the payment reference.
        /// </summary>
        [BsonElement("payment_reference")]
        public string PaymentReference { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the payment was received.
        /// </summary>
        [BsonElement("payment_received_at")]
        public DateTime PaymentReceivedAt { get; set; }

        /// <summary>
        /// Gets or sets the batch payment ID.
        /// </summary>
        [BsonElement("batchpayment_id")]
        public string BatchPaymentId { get; set; }

        /// <summary>
        /// Gets or sets the batch payment transaction ID.
        /// </summary>
        [BsonElement("batchpayment_transactionId")]
        public string BatchPaymentTransactionId { get; set; }

        /// <summary>
        /// Gets or sets the batch payment code.
        /// </summary>
        [BsonElement("batchpayment_code")]
        public string BatchPaymentCode { get; set; }

        /// <summary>
        /// Gets or sets the amount of the payment.
        /// </summary>
        [BsonElement("amount")]
        public decimal Amount { get; set; }
    }
}
