﻿using NetProGroup.Trust.DataMigration.Models.Bahamas;
using NetProGroup.Trust.Domain.Shared.Constants;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Forms.Forms;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// Provides functionality to map Entry objects to KeyValueForm objects.
    /// </summary>
    public static class EntryToFormMapper
    {
        /// <summary>
        /// Maps an Entry object to a KeyValueForm object.
        /// </summary>
        /// <param name="entry">The Entry object to map from.</param>
        /// <param name="form">The KeyValueForm object to map to.</param>
        /// <param name="countryOverrides">Optional dictionary of country name overrides. Key is the original country name, value is what it should be mapped to.</param>
        /// <returns>A tuple indicating success and any errors encountered during mapping.</returns>
        public static (bool Success, List<string> Errors) MapEntryToForm(Entry entry, KeyValueForm form, Dictionary<string, string> countryOverrides = null)
        {
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            ArgumentNullException.ThrowIfNull(form, nameof(form));

            var errors = new List<string>();

            Map(entry.Company, "companyCode");

            // Company Data
            Map(entry.CompanyData.Name, FormKeys.CompanyName);
            Map(entry.CompanyData.Address, FormKeys.CompanyAddress);
            Map(entry.CompanyData.Code, FormKeys.CompanyCode);
            Map(entry.CompanyData.IncorporationCode, FormKeys.CompanyIncorporationNumber);
            MapDate(entry.CompanyData.IncorporationDate, FormKeys.CompanyIncorporationDate);
            Map(entry.CompanyData.MasterClientCode, FormKeys.CompanyMasterClientCode);
            Map(entry.CompanyData.ReferralOffice, FormKeys.CompanyReferralOffice);
            Map(true, FormKeys.CompanyIsActive);

            // Entity details
            if (entry.EntityDetails != null)
            {
                Map(entry.EntityDetails.TIN, FormKeys.EntityDetailsTin);
                Map(entry.EntityDetails.TotalAnnualGrossCurrency, FormKeys.TaxPayerIdentificationTotalAnnualGrossCurrency);
                Map(entry.EntityDetails.TotalAnnualGross, FormKeys.TaxPayerIdentificationTotalAnnualGross);
                Map(entry.EntityDetails.IsSameBusinessAddress, FormKeys.EntityDetailsSameAddress);
                Map(entry.EntityDetails.BusinessAddress, FormKeys.EntityDetailsBusinessAddress);
                Map(entry.EntityDetails.NameOfMneGroup, FormKeys.TaxPayerIdentificationNameOfNmeGroup);
            }

            // Financial period details:
            MapDate(entry.FinancialPeriodDetails.FinancialPeriodBegins, FormKeys.FinancialPeriodDetailsStartDate);
            MapDate(entry.FinancialPeriodDetails.FinancialPeriodEnds, FormKeys.FinancialPeriodDetailsEndDate);
            Map(entry.FinancialPeriodDetails.HasFinancialPeriodChanged, FormKeys.FinancialPeriodDetailsHasFinancialPeriodChanged);
            Map(entry.FinancialPeriodDetails.HasReclassification, FormKeys.FinancialPeriodDetailsHasReclassification);
            Map(entry.FinancialPeriodDetails.HasOtas, FormKeys.FinancialPeriodDetailsHasOtas);
            Map(entry.FinancialPeriodDetails.HasOtasReceipt, FormKeys.FinancialPeriodDetailsHasOtasReceipt);

            // Tax residency
            if (entry.TaxResidency != null)
            {
                Map(entry.TaxResidency.ResidentInBha, FormKeys.TaxResidencyResidentInBha);
                Map(entry.TaxResidency.IsInvestmentFund2019, FormKeys.TaxResidencyIsInvestmentFund);
                Map(entry.TaxResidency.ClaimTaxResidencyOutside, FormKeys.TaxResidencyClaimTaxResidencyOutsideBahamas);
                Map(entry.TaxResidency.EntityJurisdiction, FormKeys.TaxResidencyEntityJurisdiction);
                Map(entry.TaxResidency.ForeignTaxIdNumber, FormKeys.TaxResidencyForeignTaxIdNumber);
                Map(entry.TaxResidency.HasUltimateParents, FormKeys.TaxResidencyHasUltimateParents);
                Map(entry.TaxResidency.HasImmediateParents, FormKeys.TaxResidencyHasImmediateParents);
                MapAdditionalParents(entry.TaxResidency.AdditionalParents.Where(x => x.ParentType == "Ultimate").ToList(), FormKeys.TaxResidencyUltimateParents);
                MapAdditionalParents(entry.TaxResidency.AdditionalParents.Where(x => x.ParentType == "Immediate").ToList(), FormKeys.TaxResidencyImmediateParents);
            }

            // Relevant activities
            if (entry.RelevantActivities != null)
            {
                MapRelevantActivities(entry.RelevantActivities, FormKeys.RelevantActivitiesRelevantActivityDeclaration);
            }

            // Banking business
            MapBusiness(entry.BankingBusiness, FormKeys.BusinessPrefixBanking);
            MapBusiness(entry.InsuranceBusiness, FormKeys.BusinessPrefixInsurance);
            MapBusiness(entry.FundManagementBusiness, FormKeys.BusinessPrefixFundmanagement);
            MapBusiness(entry.FinanceLeasingBusiness, FormKeys.BusinessPrefixFinancialLeasing);
            MapBusiness(entry.HeadquartersBusiness, FormKeys.BusinessPrefixHeadquarters);
            MapBusiness(entry.ShippingBusiness, FormKeys.BusinessPrefixShipping);
            MapBusiness(entry.HoldingBusiness, FormKeys.BusinessPrefixHolding);
            MapBusiness(entry.IntellectualPropertyBusiness, FormKeys.BusinessPrefixIntellectualProperty);
            MapBusiness(entry.ServiceCentreBusiness, FormKeys.BusinessPrefixServiceCentre);

            // Supporting details
            if (entry.SupportingDetails != null)
            {
                Map(entry.SupportingDetails.Comment, FormKeys.SupportingDetailsComment);
            }

            // Confirmation
            if (entry.Confirmation != null)
            {
                Map(entry.Confirmation.Confirmed, FormKeys.FinalizeConfirmationDeclaration);
                Map(entry.Confirmation.ConfirmedAuthority, FormKeys.FinalizeAuthorityToActOnBehalf);
                Map(entry.Confirmation.ConfirmedConditions, FormKeys.FinalizeLegitimateInterestForProcessing);
                Map(entry.Confirmation.ConfirmedPayment, FormKeys.FinalizeAcknowledgeSubmissionFee);
                Map(entry.Confirmation.UserFullName, FormKeys.FinalizeDeclarantName);
                Map(entry.Confirmation.UserPhoneNumber, FormKeys.FinalizeUserPhoneNumber);
                Map(entry.Confirmation.RelationToEntity, FormKeys.FinalizeEntityRelation);
                Map(entry.Confirmation.RelationToEntityOther, FormKeys.FinalizeOtherEntityRelation);
            }

            return (errors.Count == 0, errors);

            void Map(object entryValue, string formKey)
            {
                if (entryValue != null)
                {
                    var value = entryValue.ToString()!;
                    if (entryValue is bool)
                    {
#pragma warning disable CA1308 // Normalize strings to uppercase
                        value = value.ToLowerInvariant();
#pragma warning restore CA1308 // Normalize strings to uppercase
                    }
                    form.DataSet.Add(formKey, value);
                }
            }

#pragma warning disable CS8321 // Local function is declared but never used
            void MapCountry(string countryName, string formKey, List<string> errorList)
            {
                if (!string.IsNullOrEmpty(countryName))
                {
                    bool success;
                    string alpha3Code;

                    // Apply country name overrides if provided
                    if (countryOverrides != null && countryOverrides.ContainsValue(countryName))
                    {
                        success = true;
                        alpha3Code = countryOverrides.Single(x => x.Value == countryName).Key;
                    }
                    else
                    {
                        (success, alpha3Code) = CountryCodeMapper.GetAlpha3Code(countryName);
                    }

                    if (!success)
                    {
                        errorList.Add($"Failed to map country: {countryName}");
                        form.DataSet.Add(formKey, countryName); // Use original name if mapping fails
                    }
                    else
                    {
                        form.DataSet.Add(formKey, alpha3Code);
                    }
                }
            }
#pragma warning restore CS8321 // Local function is declared but never used

            void MapDate(DateTime? dateTime, string formKey)
            {
                Map(dateTime?.ToString(GeneralConsts.DateTimestampFormat), formKey);
            }

            void MapRelevantActivities(RelevantActivitiesSchema activities, string formKeyPart)
            {
                if (activities == null)
                {
                    return;
                }

                MapRelevantActivity(activities.BankingBusiness, $"{formKeyPart}.relevantActivities.3");
                MapRelevantActivity(activities.InsuranceBusiness, $"{formKeyPart}.relevantActivities.4");
                MapRelevantActivity(activities.FundManagementBusiness, $"{formKeyPart}.relevantActivities.5");
                MapRelevantActivity(activities.FinanceLeasingBusiness, $"{formKeyPart}.relevantActivities.2");
                MapRelevantActivity(activities.HeadquartersBusiness, $"{formKeyPart}.relevantActivities.6");
                MapRelevantActivity(activities.ShippingBusiness, $"{formKeyPart}.relevantActivities.7");
                MapRelevantActivity(activities.HoldingBusiness, $"{formKeyPart}.relevantActivities.1");
                MapRelevantActivity(activities.IntellectualPropertyBusiness, $"{formKeyPart}.relevantActivities.8");
                MapRelevantActivity(activities.ServiceCentreBusiness, $"{formKeyPart}.relevantActivities.9");
            }

            void MapRelevantActivity(RelevantActivitySchema activity, string formKeyPart)
            {
                if (activity == null || activity.FinancialPeriods == null)
                {
                    return;
                }

                Map(activity.Selected, $"{formKeyPart}.selected");
                Map(activity.PartOfFinancialPeriod, $"{formKeyPart}.carriedOnForOnlyPartOfFinancialPeriod");
                Map(activity.FinancialPeriods[0].FinancialPeriodBegins, $"{formKeyPart}.startDate"); // TODO check
                Map(activity.FinancialPeriods[0].FinancialPeriodEnds, $"{formKeyPart}.endDate");
            }

            void MapBusiness(BusinessSchema business, string formKeyPart)
            {
                if (business == null)
                {
                    return;
                }

                Map(business.GrossIncomeTotal, $"{formKeyPart}.{FormKeys.BusinessTotalGrossIncome}");
                Map(business.ActivityNetbookValues, $"{formKeyPart}.{FormKeys.BusinessNetBookValuesAssets}");
                Map(business.EquipmentNatureDescription, $"{formKeyPart}.{FormKeys.BusinessAssetsDescriptionBahamas}");
                Map(business.ManagementInBah, $"{formKeyPart}.{FormKeys.BusinessIsDirectedAndManagedInBahamas}");
                Map(business.NumberOfBoardMeetings, $"{formKeyPart}.{FormKeys.BusinessNumberOfMeetings}");
                Map(business.NumberOfBoardMeetingsInBahamas, $"{formKeyPart}.{FormKeys.BusinessNumberOfMeetingsInBahamas}");
                Map(business.BoardMeetingsQuorum, $"{formKeyPart}.{FormKeys.BusinessQuorumDirectors}");
                Map(business.QuorumOfDirectors, $"{formKeyPart}.{FormKeys.BusinessQuorumPhysicallyPresent}");
                Map(business.AreMinutedHeldInBah, $"{formKeyPart}.{FormKeys.BusinessAreMinutesKeptInBahamas}");
                Map(business.TotalExpenditure, $"{formKeyPart}.{FormKeys.BusinessTotalExpenditureRelevantActivity}");
                Map(business.TotalExpenditureBah, $"{formKeyPart}.{FormKeys.BusinessTotalExpenditureBahamas}");

                // In the development environment there is an entry with 0.5 instead of a round number. This should the value:
                Map((int)Math.Round(business.FullTotalEmployees, MidpointRounding.AwayFromZero), $"{formKeyPart}.{FormKeys.BusinessTotalEmployeesEntity}");

                // In the development environment there is an entry with 1.586 instead of a round number. This should the value:
                Map((int)Math.Round(business.TotalEmployeesEngagedRelevantActivity, MidpointRounding.AwayFromZero), $"{formKeyPart}.{FormKeys.BusinessTotalEmployeesRelevantActivity}");
                Map(business.TotalEmployeesPresentInBahamas, $"{formKeyPart}.{FormKeys.BusinessTotalEmployeesBahamas}");
                Map(business.OwnPremisesInBah, $"{formKeyPart}.{FormKeys.BusinessBahamasPremisesOwnership}");
                Map(business.HasAnyCiga, $"{formKeyPart}.{FormKeys.BusinessHasCiga}");
                Map(business.HasAnyCigaOutsourced, $"{formKeyPart}.{FormKeys.BusinessIsCigaOutsourced}");
                Map(business.ProportionCarriedByOutsourcing, $"{formKeyPart}.{FormKeys.BusinessCigaOutsourcingProportion}");
                Map(business.TotalExpenditureOutsourcedBah, $"{formKeyPart}.{FormKeys.BusinessBahamasOutsourcingExpenditure}");
                Map(business.EntityApplicableLaws, $"{formKeyPart}.{FormKeys.BusinessIsCompliantWithBahamasLawsAndRegulations}");
                Map(business.IsHighRiskEntity, $"{formKeyPart}.{FormKeys.BusinessIsHighRiskIpEntity}");
                Map(business.RelevantIp, $"{formKeyPart}.{FormKeys.BusinessRelevantIpAsset}");
                Map(business.IntellectualProperyUse, $"{formKeyPart}.{FormKeys.BusinessIncomeGenerationExplanation}");
                Map(business.DecisionGenerationIncome, $"{formKeyPart}.{FormKeys.BusinessEmployeeResponsibility}");
                Map(business.StrategicDecisions, $"{formKeyPart}.{FormKeys.BusinessStrategicDecisionsBahamas}");
                Map(business.HistoryTradingActivities, $"{formKeyPart}.{FormKeys.BusinessTradingActivitiesBahamas}");
                Map(business.GrossIncomeRoyalties, $"{formKeyPart}.{FormKeys.BusinessGrossIncomeRoyalties}");
                Map(business.GrossIncomeGains, $"{formKeyPart}.{FormKeys.BusinessGrossIncomeSaleIpAsset}");
                Map(business.GrossIncomeOthers, $"{formKeyPart}.{FormKeys.BusinessGrossIncomeOtherSources}");
                Map(business.BusinessPlanDetails, $"{formKeyPart}.{FormKeys.BusinessBusinessPlanExplanation}");
                Map(business.DecisionMakingEvidence, $"{formKeyPart}.{FormKeys.BusinessDecisionMakingEvidenceExplanation}");
                Map(business.OtherEvidenceDetails, $"{formKeyPart}.{FormKeys.BusinessAdditionalComplianceExplanation}");
                MapDirectorList(business.Directors, formKeyPart);
                MapEmployeeList(business.Employees, formKeyPart);
                MapPremisesList(business.Premises, formKeyPart, errors);
                MapCigaActivitiesList(business.CigaActivities, formKeyPart);
                MapOutsourcingProvidersList(business.OutsourcingProviders, formKeyPart);
            }

            void MapDirectorList(List<DirectorSchema> items, string formKeyPart)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                for (int i = 0; i < items.Count; i++)
                {
                    Map(items[i].Name, $"{formKeyPart}.directors.{i}.name");
                    Map(items[i].IsResidentInBahamas, $"{formKeyPart}.directors.{i}.isResidentInBahamas");
                    Map(items[i].RelationToEntity, $"{formKeyPart}.directors.{i}.relationToEntity");
                    Map(items[i].PhysicallyPresentInBah, $"{formKeyPart}.directors.{i}.physicallyPresentInBahamas");
                    Map(items[i].Qualification, $"{formKeyPart}.directors.{i}.qualification");
                    Map(items[i].YearsOfExperience, $"{formKeyPart}.directors.{i}.yearsOfExperience");
                    MapIntegerList(items[i].MeetingNumber, $"{formKeyPart}.directors.{i}.meetingNumber");
                }
            }

            void MapIntegerList(IList<int> items, string formKey)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                Map('[' + string.Join(", ", items) + ']', formKey);
            }

            void MapEmployeeList(List<EmployeeSchema> items, string formKeyPart)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                for (int i = 0; i < items.Count; i++)
                {
                    Map(items[i].Name, $"{formKeyPart}.employees.{i}].fullName");
                    Map(items[i].Qualification, $"{formKeyPart}.employees.{i}.qualification");
                    Map(items[i].YearsOfExperience, $"{formKeyPart}.employees.{i}.yearsOfExperience");
                    Map(items[i].ContractType, $"{formKeyPart}.employees.{i}.contractType");
                }
            }

            void MapPremisesList(List<PremisesSchema> items, string formKeyPart, List<string> errorList)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                for (int i = 0; i < items.Count; i++)
                {
                    Map(items[i].AddressLine1, $"{formKeyPart}.premises.{i}.addressLine1");
                    Map(items[i].AddressLine2, $"{formKeyPart}.premises.{i}.addressLine2");
                    MapCountry(items[i].Country, $"{formKeyPart}.premises.{i}.country", errorList);
                }
            }

            void MapCigaActivitiesList(List<CigaActivitySchema> items, string formKeyPart)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                for (int i = 0; i < items.Count; i++)
                {
                    Map(items[i].Code, $"{formKeyPart}.activities.{i}.code");
                    Map(items[i].Description, $"{formKeyPart}.activities.{i}.description");
                }
            }

            void MapOutsourcingProvidersList(List<OutsourcingProviderSchema> items, string formKeyPart)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                for (int i = 0; i < items.Count; i++)
                {
                    Map(items[i].EntityName, $"{formKeyPart}.outsourcingProviders.{i}.entityName");
                    Map(items[i].ResourceDetails, $"{formKeyPart}.outsourcingProviders.{i}.resourceDetails");
                    Map(items[i].StaffCount, $"{formKeyPart}.outsourcingProviders.{i}.staffCount");
                    Map(items[i].MonitoringControl, $"{formKeyPart}.outsourcingProviders.{i}.monitoringControl");
                    Map(items[i].PhysicalAddress, $"{formKeyPart}.outsourcingProviders.{i}.physicalAddress");
                    Map(items[i].MonitoringControlExplanation, $"{formKeyPart}.outsourcingProviders.{i}.monitoringControlExplanation");
                }
            }

            void MapAdditionalParents(List<AdditionalParentsSchema> items, string formKeyPart)
            {
                if (items == null || items.Count == 0)
                {
                    return;
                }

                for (int i = 0; i < items.Count; i++)
                {
                    Map(items[i].ParentType, $"{formKeyPart}.outsourcingProviders.{i}.entityName");
                    Map(items[i].ParentName, $"{formKeyPart}.outsourcingProviders.{i}.resourceDetails");
                    Map(items[i].AlternativeName, $"{formKeyPart}.outsourcingProviders.{i}.staffCount");
                    Map(items[i].Jurisdiction, $"{formKeyPart}.outsourcingProviders.{i}.monitoringControl");
                    Map(items[i].IncorporationNumber, $"{formKeyPart}.outsourcingProviders.{i}.physicalAddress");
                    Map(items[i].TIN, $"{formKeyPart}.outsourcingProviders.{i}.monitoringControlExplanation");
                }
            }
        }
    }
}
