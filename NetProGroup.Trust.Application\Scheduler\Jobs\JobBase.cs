﻿// <copyright file="JobBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Scheduler.Cron;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs
{
    /// <summary>
    /// Base implementation for a scheduled job.
    /// </summary>
    /// <typeparam name="TData">The job data type.</typeparam>
    public abstract class JobBase<TData> where TData : class, new()
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IScheduledJobsRepository _jobsRepository;
        private readonly IApplicationInsightsDependencyTracker _dependencyTracker;
        private readonly ScheduledJobSettings _jobSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="JobBase{TData}"/> class.
        /// </summary>
        /// <param name="logger">Logger instance used for logging job events.</param>
        /// <param name="serviceProvider">Service provider instance for resolving dependencies.</param>
        /// <param name="options">The scheduling job settings options.</param>
        internal JobBase(ILogger logger,
                         IServiceProvider serviceProvider,
                         IOptions<ScheduledJobSettings> options)
        {
            _logger = Check.NotNull(logger, nameof(logger));
            _serviceProvider = Check.NotNull(serviceProvider, nameof(serviceProvider));
            _jobsRepository = serviceProvider.GetRequiredService<IScheduledJobsRepository>();
            _dependencyTracker = serviceProvider.GetRequiredService<IApplicationInsightsDependencyTracker>();
            _jobSettings = options.Value;
        }

        /// <summary>
        /// Gets the logger instance.
        /// </summary>
        protected ILogger Logger => _logger;

        /// <summary>
        /// Gets the service provider for the job.
        /// </summary>
        protected IServiceProvider ServiceProvider => _serviceProvider;

        /// <summary>
        /// Runs the job.
        /// </summary>
        /// <param name="jobDetails">The details for the job to run.</param>
        /// <param name="token">The optional cancellation token.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Catch all")]
        public virtual async Task RunAsync(JobRunDetails jobDetails, CancellationToken token = default)
        {
            ArgumentNullException.ThrowIfNull(jobDetails, nameof(jobDetails));

            var scheduledJob = await _jobsRepository.GetByIdAsync(jobDetails.JobId);

            if (scheduledJob == null)
            {
                _logger.LogWarning("ScheduledJob '{JobId}' not found", jobDetails.JobId);
                return;
            }

            var jobConfiguration = _jobSettings[scheduledJob.Key];
            if (!jobConfiguration.Enabled && !scheduledJob.Trigger)
            {
                return;
            }

            await _dependencyTracker.TrackDependencyAsync(
                $"ScheduledJob '{scheduledJob.Name}'",
                async () =>
                {
                    // set indicator that the job is running
                    scheduledJob.IsActive = true;
                    scheduledJob.Trigger = false;

                    await _jobsRepository.SaveChangesAsync();

                    TData data = default;
                    try
                    {
                        _logger.LogInformation("Running job '{JobName}'", scheduledJob.Name);

                        data = scheduledJob.GetData<TData>() ?? new TData();

                        await DoWorkAsync(data, token);

                        // Store the time of the last run
                        scheduledJob.LastRunAt = jobDetails.LastRunUtc;

                        await _jobsRepository.SaveChangesAsync();
                    }
                    catch (NoLockException)
                    {
                        _logger.LogWarning("No lock for job '{JobName}'", scheduledJob.Name);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "DoWork() for scheduled job '{JobName}' failed", scheduledJob.Name);
                        throw; // Re-throw to let the dependency tracker mark as failed
                    }
                    finally
                    {
                        if (data != default)
                        {
                            scheduledJob.SetData(data);
                        }

                        // Reset the IsActive flag. (reset indicator that the job is not running)
                        scheduledJob.IsActive = false;
                        await _jobsRepository.SaveChangesAsync();
                    }
                },
                "Background");
        }

        /// <summary>
        /// The actual implementation of the job.
        /// Note: do not swallow exceptions in the implementation.
        /// Let them bubble up, so that the job status can be updated accordingly.
        /// </summary>
        /// <param name="data">The job data to process.</param>
        /// <param name="token">Token for cancellation.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected abstract Task DoWorkAsync(TData data, CancellationToken token = default);

        /// <summary>
        /// Acquires a lock to execute a scheduled job.
        /// </summary>
        /// <param name="jobId">Id of the job to acqiure the lock for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected async Task<LockDTO> AcquireLockAsync(Guid jobId)
        {
            var lockManager = _serviceProvider.GetRequiredService<ILockManager>();

            var request = new AcquireLockRequestDTO
            {
                IdentityUserId = Guid.NewGuid(),
                EntityName = "ScheduledJob",
                EntityId = jobId,
                Session = string.Empty
            };

            return await lockManager.AcquireLockAsync(request);
        }

        /// <summary>
        /// Releases a lock.
        /// </summary>
        /// <param name="theLock">The lock to release.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected async Task ReleaseLockAsync(LockDTO theLock)
        {
            var lockManager = _serviceProvider.GetRequiredService<ILockManager>();
            await lockManager.ReleaseLockAsync(theLock);
        }
    }
}
