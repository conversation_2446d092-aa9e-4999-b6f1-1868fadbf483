// <copyright file="ICompaniesStrSubmissionStatusRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Nevis.CompaniesSTRSubmissionStatus.Populators
{
    /// <summary>
    /// Interface for the companies without submissions row populator.
    /// </summary>
    public interface ICompaniesStrSubmissionStatusRowPopulator : ITemplateRowPopulator<LegalEntity>, ITransientService;
}