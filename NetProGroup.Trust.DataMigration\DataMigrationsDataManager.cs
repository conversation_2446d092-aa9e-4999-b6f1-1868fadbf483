// <copyright file="DataMigrationsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Domain.DataMigrations;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Repository;
using System.Text.Json;
using System.Threading.Tasks.Dataflow;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Manager for migration record data.
    /// </summary>
    public class DataMigrationsDataManager : IDataMigrationsDataManager
    {
        private readonly TrustDbContext _sqlDbContext;
        private readonly ILogger<IDataMigrationsDataManager> _logger;
        private readonly IDataMigrationsRepository _dataMigrationsRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataMigrationsDataManager"/> class.
        /// </summary>
        /// <param name="sqlDbContext">Instance of the TrustDbContext.</param>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="dataMigrationsRepository">Instance of the DataMigrationsRepository.</param>
        public DataMigrationsDataManager(TrustDbContext sqlDbContext, ILogger<IDataMigrationsDataManager> logger, IDataMigrationsRepository dataMigrationsRepository)
        {
            _sqlDbContext = sqlDbContext ?? throw new ArgumentNullException(nameof(sqlDbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dataMigrationsRepository = dataMigrationsRepository;
        }

        /// <summary>
        /// Gets or creates a migration record for the specified region.
        /// </summary>
        /// <param name="region">The region for the migration record.</param>
        /// <param name="startedByUserId">The user ID that started the migration.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<Domain.DataMigrations.DataMigration> GetOrCreateMigrationRecordAsync(string region, Guid startedByUserId)
        {
            try
            {
                var jurisdiction = await _sqlDbContext.Set<Jurisdiction>()
                                                      .SingleAsync(j => j.Code == region);

                var migrationRecord = await _sqlDbContext.Set<Domain.DataMigrations.DataMigration>()
                                                         .Include(dm => dm.Jurisdiction)
                                                         .FirstOrDefaultAsync(m => m.JurisdictionId == jurisdiction.Id);

                if (migrationRecord == null || migrationRecord.MigrationCompleted)
                {
                    migrationRecord = new Domain.DataMigrations.DataMigration(jurisdiction.Id, startedByUserId);
                    _sqlDbContext.Add(migrationRecord);
                    await _sqlDbContext.SaveChangesAsync();
                    _logger.LogInformation("Created new migration record for region: {Region}", region);
                }
                else
                {
                    _logger.LogInformation("Retrieved existing migration record for region: {Region}", region);
                }

                return migrationRecord;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating migration record for region: {Region}", region);
                throw;
            }
        }

        /// <summary>
        /// Updates the migration record.
        /// </summary>
        /// <param name="migrationRecord">The migration record to update.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task UpdateMigrationRecordAsync(Domain.DataMigrations.DataMigration migrationRecord)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));

            try
            {
                _logger.LogDebug("Updating migration record for region: {Region}", migrationRecord.Region);

                migrationRecord.LastUpdated = DateTime.UtcNow;
                await _sqlDbContext.SaveChangesAsync();

                _logger.LogInformation("Migration record updated for region: {Region}", migrationRecord.Region);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating migration record for region: {Region}", migrationRecord.Region);
                throw;
            }
        }

        /// <summary>
        /// Gets or creates an entity progress record.
        /// </summary>
        /// <param name="migrationRecord">The associated migration record.</param>
        /// <param name="entityName">The name of the entity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<EntityMigrationProgress> GetOrCreateEntityProgressAsync(Domain.DataMigrations.DataMigration migrationRecord, string entityName)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));

            try
            {
                var entityProgress = await _sqlDbContext.Set<EntityMigrationProgress>()
                    .FirstOrDefaultAsync(ep => ep.DataMigrationId == migrationRecord.Id && ep.EntityName == entityName);

                if (entityProgress == null)
                {
                    entityProgress = new EntityMigrationProgress
                    {
                        DataMigrationId = migrationRecord.Id,
                        EntityName = entityName,
                        LastUpdated = DateTime.UtcNow
                    };
                    _sqlDbContext.Add(entityProgress);

                    _logger.LogDebug("Created new entity progress record for entity: {EntityName}", entityName);
                }
                else
                {
                    _logger.LogTrace("Retrieved existing entity progress record for entity: {EntityName}", entityName);
                }

                return entityProgress;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating entity progress record for entity: {EntityName}", entityName);
                throw;
            }
        }

        /// <summary>
        /// Updates the entity progress record.
        /// </summary>
        /// <param name="entityProgress">The entity progress record to update.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task UpdateEntityProgressAsync(EntityMigrationProgress entityProgress)
        {
            ArgumentNullException.ThrowIfNull(entityProgress, nameof(entityProgress));

            try
            {
                entityProgress.LastUpdated = DateTime.UtcNow;
                await _sqlDbContext.SaveChangesAsync();
                _logger.LogDebug("Updated entity progress record for entity: {EntityName}", entityProgress.EntityName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating entity progress record for entity: {EntityName}", entityProgress.EntityName);
                throw;
            }
        }

        /// <summary>
        /// Stores unprocessed records for a migration.
        /// </summary>
        /// <param name="migrationRecord">The migration record.</param>
        /// <param name="unprocessedRecords">The list of unprocessed records.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task StoreUnprocessedRecordsAsync(Domain.DataMigrations.DataMigration migrationRecord, List<UnprocessedRecord> unprocessedRecords)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            ArgumentNullException.ThrowIfNull(unprocessedRecords, nameof(unprocessedRecords));

            try
            {
                if (unprocessedRecords.Count == 0)
                {
                    _logger.LogInformation("No unprocessed records to store for region: {Region}", migrationRecord.Region);
                    return;
                }

                if (!string.IsNullOrEmpty(migrationRecord.UnprocessedRecords))
                {
                    var existingUnprocessedRecords = JsonSerializer.Deserialize<List<UnprocessedRecord>>(migrationRecord.UnprocessedRecords);
                    unprocessedRecords.AddRange(existingUnprocessedRecords);
                }

                migrationRecord.UnprocessedRecords = JsonSerializer.Serialize(unprocessedRecords);
                migrationRecord.LastUpdated = DateTime.UtcNow;
                await _sqlDbContext.SaveChangesAsync();

                _logger.LogInformation("Stored {Count} unprocessed records for region: {Region}", unprocessedRecords.Count, migrationRecord.Region);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error storing unprocessed records for region: {Region}", migrationRecord.Region);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<Domain.DataMigrations.DataMigration> GetLastDataMigrationForJurisdiction(string jurisdictionCode)
        {
            var dataMigration = await _dataMigrationsRepository.FindFirstOrDefaultByConditionAsync(
                dm => dm.Jurisdiction.Code == jurisdictionCode,
                q => q.Include(dm => dm.EntityMigrationProgresses)
                      .Include(dm => dm.Jurisdiction)
                      .OrderByDescending(migration => migration.CreatedAt));

            return dataMigration;
        }

        /// <inheritdoc />
        public async Task<Domain.DataMigrations.DataMigration> RefreshMigrationRecordAsync(Domain.DataMigrations.DataMigration dataMigrationRecord)
        {
            ArgumentNullException.ThrowIfNull(dataMigrationRecord, nameof(dataMigrationRecord));

            await _sqlDbContext.Entry(dataMigrationRecord).ReloadAsync();
            return dataMigrationRecord;
        }
    }
}
