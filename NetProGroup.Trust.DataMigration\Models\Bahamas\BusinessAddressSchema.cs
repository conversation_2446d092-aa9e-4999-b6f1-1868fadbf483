using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Business address schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public sealed class BusinessAddressSchema
    {
        /// <summary>
        /// Gets or sets the first line of the address.
        /// </summary>
        [BsonElement("address_line1")]
        public string Address1 { get; set; }

        /// <summary>
        /// Gets or sets the second line of the address.
        /// </summary>
        [BsonElement("address_line2")]
        public string Address2 { get; set; }

        /// <summary>
        /// Gets or sets the country.
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }
    }
}
