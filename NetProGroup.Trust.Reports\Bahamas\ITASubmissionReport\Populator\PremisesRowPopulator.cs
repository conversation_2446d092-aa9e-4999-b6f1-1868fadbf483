// <copyright file="PremisesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class PremisesRowPopulator : LinePopulatorBase, IPremisesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.RelevantActivities).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.RelevantActivities)[1].Split(".")[0]);

            foreach (var relevantActivityGroup in relevantActivityGroups)
            {
                // Check if the activity was selected
                var isSelected = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Selected);

                if (isSelected == "true")
                {
                    // Retrieve the name
                    var relevantActivity = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Retrieve the relevant activity data
                    var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                    // Retrieve the created premises
                    var premises = relevantActivityData.GetAttributesWithKey(WellKnownFormDocumentAttibuteKeys.Premises).ToList();

                    // Group the premises
                    var premiseGroups = premises.GroupBy(a => a.Key.Split(relevantActivityKey + WellKnownFormDocumentAttibuteKeys.Premises)[1].Split(".")[0]);

                    foreach (var premiseGroup in premiseGroups)
                    {
                        // Retrieve the entity unique id
                        var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                        SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                        // Retrieve the name
                        var activityName = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                        SetCellValueAndStyle(worksheet, currentRow, 2, activityName);

                        var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                        SetCellValueAndStyle(worksheet, currentRow, 3, financialPeriodEndDate);

                        // Retrieve the premise address line 1
                        var addressLine1 = premiseGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AddressLine1);

                        SetCellValueAndStyle(worksheet, currentRow, 4, addressLine1);

                        // Retrieve the premise address line 2
                        var addressLine2 = premiseGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AddressLine2);

                        SetCellValueAndStyle(worksheet, currentRow, 5, addressLine1);

                        // Retrieve the premise country
                        var country = premiseGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.PremiseCountry);

                        SetCellValueAndStyle(worksheet, currentRow, 6, country);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}