using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.DataManager.Reports;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Trust.Domain.Report.Enum;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class ReportDataManagerTests : TestBase
    {
        private IReportsDataManager _reportDataManager;
        private IReportRepository _repository;

        [SetUp]
        public void Setup()
        {
            _reportDataManager = _server.Services.GetRequiredService<IReportsDataManager>();
            _repository = _server.Services.GetRequiredService<IReportRepository>();
            SetWorkContextUser(ManagementUser);
        }

        [Test]
        public async Task GetReportsByTypeAsync_CalledWith6Month_ReturnsReportsWithinCreatedAfterPeriod()
        {
            // Arrange
            var now = DateTime.UtcNow;
            var createdAfter = now.AddMonths(-6);

            // Seed reports: 2 within period, 1 outside
            var reportType = ReportType.Financial; // Use a valid enum value
            var reports = new List<Report>
                {
                    new Report(Guid.NewGuid()) { DocumentId = Guid.NewGuid(), ReportName = "Report1", Type = reportType, CreatedAt = now.AddMonths(-1), Document = new Document(Guid.NewGuid()) { Filename = "Doc1.pdf", Description = "Test financial report document" } },
                    new Report(Guid.NewGuid()) { DocumentId = Guid.NewGuid(), ReportName = "Report2", Type = reportType, CreatedAt = now.AddMonths(-2), Document = new Document (Guid.NewGuid()) { Filename = "Doc1.pdf", Description = "Test financial report document" }},
                    new Report(Guid.NewGuid()) { DocumentId = Guid.NewGuid(), ReportName = "Report3", Type = reportType, CreatedAt = now.AddMonths(-7), Document = new Document (Guid.NewGuid()) { Filename = "Doc1.pdf" , Description = "Test financial report document"} } // outside period
                };

            foreach (var report in reports)
            {
                var entities = await _repository.InsertAsync(report, true);
            }

            var request = new ReportRequestDTO
            {
                ReportTypes = new[] { reportType }
            };

            // Act
            var result = await _reportDataManager.GetReportsByTypeAsync(request, createdAfter);

            // Assert
            result.Should().NotBeNull();
            var returnedReports = result.ToList();
            returnedReports.Should().HaveCount(2);
            returnedReports.Select(r => r.ReportName).Should().Contain(new[] { "Report1", "Report2" });
            returnedReports.Select(r => r.ReportName).Should().NotContain("Report3");
        }
    }
}