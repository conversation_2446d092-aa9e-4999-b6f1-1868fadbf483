// <copyright file="SubmissionDataReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport
{
    /// <summary>
    /// Generates the submission data report.
    /// </summary>
    public class SubmissionDataReportGenerator : ISubmissionDataReportGenerator
    {
        private const string ReportName = "submission-data";
        private readonly ISubmissionDataReportRowPopulator _submissionDataReportRowPopulator;
        private readonly ISubmissionDataReportPropertiesRowPopulator _submissionDataReportPropertiesRowPopulator;
        private readonly ISubmissionDataReportIncomesRowPopulator _submissionDataReportIncomesRowPopulator;
        private readonly ISubmissionDataReportExpensesRowPopulator _submissionDataReportExpensesRowPopulator;
        private readonly ISubmissionDataReportLiabilitiesRowPopulator _submissionDataReportLiabilitiesRowPopulator;
        private readonly ISubmissionDataReportAssetsRowPopulator _submissionDataReportAssetsRowPopulator;
        private readonly ISubmissionDataReportBankAccountsRowPopulator _submissionDataReportBankAccountsRowPopulator;
        private readonly IExcelTemplateService<Submission> _excelTemplateService;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionDataReportGenerator"/> class.
        /// </summary>
        /// <param name="submissionDataReportRowPopulator">The submission line populator.</param>
        /// <param name="submissionDataReportPropertiesRowPopulator">The properties row populator.</param>
        /// <param name="submissionDataReportIncomesRowPopulator">The incomes row populator.</param>
        /// <param name="submissionDataReportExpensesRowPopulator">The expenses row populator.</param>
        /// <param name="submissionDataReportLiabilitiesRowPopulator">The liabilities row populator.</param>
        /// <param name="submissionDataReportAssetsRowPopulator">The assets row populator.</param>
        /// <param name="submissionDataReportBankAccountsRowPopulator">The bank row populator.</param>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="templateProvider">The template provider.</param>
        public SubmissionDataReportGenerator(
            ISubmissionDataReportRowPopulator submissionDataReportRowPopulator,
            ISubmissionDataReportPropertiesRowPopulator submissionDataReportPropertiesRowPopulator,
            ISubmissionDataReportIncomesRowPopulator submissionDataReportIncomesRowPopulator,
            ISubmissionDataReportExpensesRowPopulator submissionDataReportExpensesRowPopulator,
            ISubmissionDataReportLiabilitiesRowPopulator submissionDataReportLiabilitiesRowPopulator,
            ISubmissionDataReportAssetsRowPopulator submissionDataReportAssetsRowPopulator,
            ISubmissionDataReportBankAccountsRowPopulator submissionDataReportBankAccountsRowPopulator,
            IExcelTemplateService<Submission> excelTemplateService,
            IReportTemplateProvider templateProvider)
        {
            _submissionDataReportRowPopulator = submissionDataReportRowPopulator;
            _submissionDataReportPropertiesRowPopulator = submissionDataReportPropertiesRowPopulator;
            _submissionDataReportIncomesRowPopulator = submissionDataReportIncomesRowPopulator;
            _submissionDataReportExpensesRowPopulator = submissionDataReportExpensesRowPopulator;
            _submissionDataReportLiabilitiesRowPopulator = submissionDataReportLiabilitiesRowPopulator;
            _submissionDataReportAssetsRowPopulator = submissionDataReportAssetsRowPopulator;
            _submissionDataReportBankAccountsRowPopulator = submissionDataReportBankAccountsRowPopulator;
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
        }

        /// <inheritdoc/>
        public async Task<ReportOutput> GenerateSubmissionDataReportAsync(List<Submission> submissions)
        {
            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(ReportName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionsReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new ReportOutput(stream.ToArray());
        }

        /// <summary>
        /// Creates an Excel report for the given companies.
        /// </summary>
        /// <param name="workbook">The workbook to add the companies to.</param>
        /// <param name="submissions">The submissions to add in the report.</param>
        private void CreateSubmissionsReport(XLWorkbook workbook, List<Submission> submissions)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per company
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };

            // Fill the main sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 0);

            // Fill the properties sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportPropertiesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 1);

            // Fill the incomes sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportIncomesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 2);

            // Fill the expenses sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportExpensesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 3);

            // Fill the liabilities sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportLiabilitiesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 4);

            // Fill the assets sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportAssetsRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 5);

            // Fill the bank sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_submissionDataReportBankAccountsRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 6);
        }
    }
}