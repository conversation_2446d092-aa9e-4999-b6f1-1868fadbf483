﻿// <copyright file="SubmissionsDataManagerBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Attributes;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms;
using NetProGroup.Trust.Forms.Forms;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Base class for submission managers.
    /// </summary>
    public abstract class SubmissionsDataManagerBase : ICommonSubmissionsDataManager
    {
        private readonly IWorkContext _workContext;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly ISettingsManager _settingsManager;
        private readonly ISystemAuditManager _systemAuditManager;

        private readonly IFormTemplatesRepository _formTemplatesRepository;
        private readonly IFormDocumentAttributesRepository _formDocumentAttributesRepository;
        private readonly IFormDocumentDocumentsRepository _formDocumentDocumentsRepository;
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;
        private readonly IFormDocumentRevisionsRepository _formDocumentRevisionsRepository;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsDataManagerBase"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="serviceProvider">The serviceprovider to get the needed services.</param>
        protected SubmissionsDataManagerBase(ILogger logger,
                                             IServiceProvider serviceProvider)
        {
            _logger = logger;
            _mapper = serviceProvider.GetRequiredService<IMapper>();
            _systemAuditManager = serviceProvider.GetRequiredService<ISystemAuditManager>();
            _settingsManager = serviceProvider.GetRequiredService<ISettingsManager>();
            _workContext = serviceProvider.GetRequiredService<IWorkContext>();

            _authorizationFilterExpressionFactory = serviceProvider.GetRequiredService<IAuthorizationFilterExpressionFactory>();

            _submissionsRepository = serviceProvider.GetRequiredService<ISubmissionsRepository>();
            _formTemplatesRepository = serviceProvider.GetRequiredService<IFormTemplatesRepository>();
            _formDocumentAttributesRepository = serviceProvider.GetRequiredService<IFormDocumentAttributesRepository>();
            _formDocumentDocumentsRepository = serviceProvider.GetRequiredService<IFormDocumentDocumentsRepository>();
            _formDocumentRevisionsRepository = serviceProvider.GetRequiredService<IFormDocumentRevisionsRepository>();
            _legalEntitiesRepository = serviceProvider.GetRequiredService<ILegalEntitiesRepository>();
        }

        /// <summary>
        /// Gets the WorkContext instance.
        /// </summary>
        protected IWorkContext WorkContext => _workContext;

        /// <summary>
        /// Gets the logger instance.
        /// </summary>
        protected ILogger Logger => _logger;

        /// <summary>
        /// Gets the mapper instance.
        /// </summary>
        protected IMapper Mapper => _mapper;

        /// <summary>
        /// Gets the LegalEntitiesRepository instance.
        /// </summary>
        protected ILegalEntitiesRepository LegalEntitiesRepository => _legalEntitiesRepository;

        /// <summary>
        /// Gets the SubmissionsRepository instance.
        /// </summary>
        protected ISubmissionsRepository SubmissionsRepository => _submissionsRepository;

        /// <summary>
        /// Gets the FormTemplatesRepository instance.
        /// </summary>
        protected IFormTemplatesRepository FormTemplatesRepository => _formTemplatesRepository;

        /// <summary>
        /// Gets the FormDocumentRevisionsRepository instance.
        /// </summary>
        protected IFormDocumentRevisionsRepository FormDocumentRevisionsRepository => _formDocumentRevisionsRepository;

        /// <summary>
        /// Gets the SystemAuditManager instance.
        /// </summary>
        protected ISystemAuditManager SystemAuditManager => _systemAuditManager;

        /// <summary>
        /// Gets the SettingsManager instance.
        /// </summary>
        protected ISettingsManager SettingsManager => _settingsManager;

        /// <summary>
        /// Lists the submissions for a specific legal entity and module. No search parameters.
        /// </summary>
        /// <remarks>
        /// The purpose of the method is to be used for the client portal.
        /// </remarks>
        /// <param name="request">The request with the parameters.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public virtual async Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(ListSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));
            Check.NotDefaultOrNull<Guid>(request.LegalEntityId, nameof(request.LegalEntityId));
            Check.NotNull<PagingInfo>(request.PagingInfo, nameof(request.PagingInfo));

            var submissions = await _submissionsRepository.FindByConditionAsPagedListMappedAsync<Submission, ListSubmissionDTO>(
                fd => fd.LegalEntityId == request.LegalEntityId &&
                      fd.ModuleId == request.ModuleId &&
                      fd.Status != SubmissionStatus.Temporal,
                _mapper.ConfigurationProvider,
                pageNumber: request.PagingInfo.PageNumber,
                pageSize: request.PagingInfo.PageSize,
                options: q => q
                              .TagWithCallSite(),
                optionsMapped: q => ApplySorting(q, request.SortingInfo));

            return submissions;
        }

        /// <summary>
        /// Lists the sumission using searchparameters in the filter request.
        /// </summary>
        /// <remarks>
        /// The purpose of this method is to be used by the management portal.
        /// </remarks>
        /// <param name="request">The request with the search parameters.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public virtual async Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsAsync(SearchSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.PagingInfo, nameof(request.PagingInfo));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            // Check for companies that match the criteria (based on the authorized jurisdictions)
            var submissionPredicate = GetSubmissionPredicate(request);

            // Select the submissions
            var submissions = await _submissionsRepository.FindByConditionAsPagedListMappedAsync<Submission, ListSubmissionDTO>(
                submissionPredicate,
                _mapper.ConfigurationProvider,
                pageNumber: request.PagingInfo.PageNumber,
                pageSize: request.PagingInfo.PageSize,
                options: q => q
                               .Include(s => s.LegalEntity.MasterClient)
                               .Include(s => s.Invoice.PaymentInvoices).ThenInclude(pi => pi.Payment)
                               .Include(s => s.SubmittedByUser)
                               .TagWithCallSite(),
                optionsMapped: q => ApplySorting(q, request.SortingInfo));

            return submissions;
        }

        /// <inheritdoc/>
        public virtual async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId)
        {
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            var submission = await _submissionsRepository.GetByIdAsync(submissionId);
            var result = _mapper.Map<SubmissionDTO>(submission);

            return result;
        }

        /// <inheritdoc/>
        public virtual async Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, UpdateSubmissionInformationDTO model, bool saveChanges = false)
        {
            throw new NotImplementedException("Must be implemented in derived SubmissionDataManager");
#pragma warning disable CS0162 // Unreachable code detected
            await Task.CompletedTask;
#pragma warning restore CS0162 // Unreachable code detected
        }

        /// <inheritdoc/>
        public virtual async Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model, Jurisdiction jurisdiction, Module module)
        {
            throw new NotImplementedException("Must be implemented in derived SubmissionDataManager");
#pragma warning disable CS0162 // Unreachable code detected
            await Task.CompletedTask;
#pragma warning restore CS0162 // Unreachable code detected
        }

        /// <inheritdoc/>
        public virtual async Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model)
        {
            throw new NotImplementedException("Must be implemented in derived SubmissionDataManager");
#pragma warning disable CS0162 // Unreachable code detected
            await Task.CompletedTask;
#pragma warning restore CS0162 // Unreachable code detected
        }

        /// <inheritdoc/>
        public virtual async Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model)
        {
            throw new NotImplementedException("Must be implemented in derived SubmissionDataManager");
#pragma warning disable CS0162 // Unreachable code detected
            await Task.CompletedTask;
#pragma warning restore CS0162 // Unreachable code detected
        }

        /// <inheritdoc/>
        public virtual async Task<List<ListSubmissionDTO>> ListScheduledSubmissionsAsync(DateTime? scheduledDate)
        {
            throw new NotImplementedException("Must be implemented in derived SubmissionDataManager");
#pragma warning disable CS0162 // Unreachable code detected
            await Task.CompletedTask;
#pragma warning restore CS0162 // Unreachable code detected
        }

        /// <inheritdoc/>
        public virtual async Task SubmitScheduledSubmissionAsync(Guid submissionId)
        {
            throw new NotImplementedException("Must be implemented in derived SubmissionDataManager");
#pragma warning disable CS0162 // Unreachable code detected
            await Task.CompletedTask;
#pragma warning restore CS0162 // Unreachable code detected
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, bool includeFormDocument)
        {
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            // Check for the submission entity.
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(
                submissionId,
                options: q => q.Include(s => s.LegalEntity).ThenInclude(le => le.MasterClient)
                    .Include(s => s.Invoice).ThenInclude(inv => inv.PaymentInvoices).ThenInclude(pi => pi.Payment)
                    .Include(s => s.SubmittedByUser));

            FormDocumentRevision formDocumentRevision = null;
            if (includeFormDocument)
            {
                // Get the last revision for the submission
                formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));
            }

            var result = _mapper.Map<SubmissionDTO>(submission);
            if (formDocumentRevision != null)
            {
                result.FormDocument = _mapper.Map<FormDocumentWithRevisionsDTO>(formDocumentRevision.FormDocument);
            }

            // Retreive the FormDocumentDocuments if any exists
            var formDocumentDocuments = await _formDocumentDocumentsRepository.FindByConditionAsync(
                fdd => fdd.FormDocumentId == submission.FormDocumentId);

            // Return the document ids relation to the submission
            result.DocumentIds = formDocumentDocuments.Select(fdd => fdd.DocumentId).ToList();

            return result;
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, Guid revisionId, bool includeFormDocument)
        {
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            // Check for the submission entity.
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(submissionId);

            FormDocumentRevision formDocumentRevision = null;
            if (includeFormDocument)
            {
                // Get the given revision for the submission
                formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.Id == revisionId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument));
            }

            var result = _mapper.Map<SubmissionDTO>(submission);
            result.FormDocument = _mapper.Map<FormDocumentWithRevisionsDTO>(formDocumentRevision.FormDocument);

            // In case there are multiple revisions (after reopening)
            if (result.FormDocument.Revisions.Count > 1)
            {
                result.FormDocument.Revisions = new FormDocumentRevisionDTO[] { result.FormDocument.Revisions.FirstOrDefault(r => r.Id == revisionId) };
            }

            return result;
        }

        /// <summary>
        /// Applies the sorting to the IQueryable for ListSubmissionDTO.
        /// </summary>
        /// <param name="query">The IQueryable.</param>
        /// <param name="sortingInfo">The info for the sorting.</param>
        /// <returns>The resulting IQueryable.</returns>
        protected static IQueryable<ListSubmissionDTO> ApplySorting(IQueryable<ListSubmissionDTO> query, SortingInfo sortingInfo)
        {
            sortingInfo = sortingInfo.Validate();

            // Define how to sort
            var sortingColumns = new Dictionary<string, Expression<Func<ListSubmissionDTO, object>>>()
            {
                { nameof(ListSubmissionDTO.MasterClientCode), s => s.MasterClientCode },
                {
                    nameof(ListSubmissionDTO.Status), s =>
                    s.Status == SubmissionStatus.Draft ? 1 :
                    s.Status == SubmissionStatus.Paid ? 2 :
                    s.Status == SubmissionStatus.Submitted ? 3 :
                    4
                }
            };
            Expression<Func<ListSubmissionDTO, object>> defaultSort = s => s.CreatedAt;

            return query.SortBySpecification<ListSubmissionDTO, SearchSubmissionsRequestDTO>(sortingInfo, defaultSort, sortingColumns);
        }

        /// <summary>
        /// Starts a submission for the given template version and data.
        /// </summary>
        /// <param name="model">The necessary data to create a submission.</param>
        /// <param name="templateVersion">The version to use as FormTemplateVersion.</param>
        /// <param name="status">The desired status for the submission.</param>
        /// <param name="beforeSaveAsync">Callback to make some adjustments to the submission before saving.</param>
        /// <returns>The created submission as SubmissionDTO.</returns>
        protected virtual async Task<SubmissionDTO> StartSubmission(StartSubmissionDTO model,
                                                                    FormTemplateVersion templateVersion,
                                                                    SubmissionStatus status = SubmissionStatus.Draft,
                                                                    Func<Submission, Task> beforeSaveAsync = null)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            ArgumentNullException.ThrowIfNull(templateVersion, nameof(templateVersion));

            // Create a FormDocument from this template and set status to 'draft'
            var formDocument = new FormDocument(Guid.NewGuid())
            {
                FormTemplateVersionId = templateVersion.Id,
                LegalEntityId = model.LegalEntityId,
                ModuleId = model.ModuleId,
                Name = templateVersion.Name,
                Year = model.FinancialYear,
                Status = DomainShared.Enums.FormDocumentStatus.Draft
            };

            FormBuilder.FromJson(templateVersion.DataAsJson);

            // Create a new revision and add it to the document
            var formDocumentRevision = new FormDocumentRevision
            {
                Revision = 0,
                Status = DomainShared.Enums.FormDocumentRevisionStatus.Draft,
                DataAsJson = templateVersion.DataAsJson,
            };

            formDocument.FormDocumentRevisions.Add(formDocumentRevision);

            // Create a submission and assign the FormDocument
            var id = Guid.NewGuid();
            var submission = new Submission(id)
            {
                Name = templateVersion.Name,
                FinancialYear = model.FinancialYear,
                ReportId = id.ToString(),
                FormDocument = formDocument,
                LegalEntityId = model.LegalEntityId,
                ModuleId = model.ModuleId,
                Status = status,
                Layout = LayoutConsts.TridentTrust
            };

            SetLastActivity(submission);

            if (beforeSaveAsync != null)
            {
                await beforeSaveAsync(submission);
            }

            await _submissionsRepository.InsertAsync(submission);

            await _systemAuditManager.AddActivityLogAsync(submission,
                                                          ActivityLogActivityTypes.SubmissionStarted,
                                                          "Submission started",
                                                          $"Submission '{submission.Name}' started");

            await _submissionsRepository.SaveChangesAsync();

            var result = _mapper.Map<SubmissionDTO>(submission);

            return result;
        }

        /// <summary>
        /// Updates the attributes of FormDocument with the values from the submission form.
        /// </summary>
        /// <param name="form">The formbase that holds the submission form values.</param>
        /// <param name="formDocument">The FormDocument entity with the attributes.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected virtual async Task UpdateFormAttributes(FormBase form, FormDocument formDocument)
        {
            ArgumentNullException.ThrowIfNull(form, nameof(form));
            ArgumentNullException.ThrowIfNull(formDocument, nameof(formDocument));

            // Update attributes
            var existingAttributes = formDocument.Attributes.ToList();

            var formValues = form.GetFormValues();
            foreach (var fieldValue in formValues)
            {
                var attr = existingAttributes.FirstOrDefault(x => x.Key == fieldValue.Key);
                if (attr != null)
                {
                    attr.Value = fieldValue.Value;
                    existingAttributes.Remove(attr);
                }
                else
                {
                    formDocument.Attributes.Add(new Domain.Forms.FormDocumentAttribute(fieldValue.Key, fieldValue.Key) { Value = fieldValue.Value });
                }
            }

            if (existingAttributes.Count > 0)
            {
                await _formDocumentAttributesRepository.DeleteAsync(existingAttributes);
            }
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        protected virtual Expression<Func<Submission, bool>> GetSubmissionPredicate(SearchSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            Expression<Func<Submission, bool>> predicate = s => s.Status != SubmissionStatus.Temporal;

            // Combine with the submission predicate using the authorized jurisdiction ids.
            predicate = predicate.And(_authorizationFilterExpressionFactory.GetSubmissionJurisdictionFilterPredicate(filter));

            predicate = predicate.And(submission => submission.ModuleId == filter.ModuleId);

            if (filter.FinancialYear != 0)
            {
                predicate = predicate.And(submission => submission.FinancialYear == filter.FinancialYear);
            }

            if (filter.SubmittedAfterDate.HasValue)
            {
                var date = filter.SubmittedAfterDate.Value.Date;
                predicate = predicate.And(submission => submission.SubmittedAt > date);
            }

            if (filter.SubmittedBeforeDate.HasValue)
            {
                var date = filter.SubmittedBeforeDate.Value.Date;
                predicate = predicate.And(submission => submission.SubmittedAt < date);
            }

            if (filter.IsPaid.HasValue)
            {
                predicate = predicate.And(submission => submission.IsPaid == filter.IsPaid.Value);
            }

            if (filter.IsExported.HasValue)
            {
                predicate = predicate.And(submission => submission.ExportedBy.HasValue == filter.IsExported.Value);
            }

            if (!string.IsNullOrEmpty(filter.Country))
            {
                var country = filter.Country;

                predicate = predicate.And(submission => submission.FormDocument.Attributes.Any(a =>
                    (a.Key == "address-of-head-office.country" && a.Value == country) ||
                    (a.Key == "address-of-head-office.nevisCountry" && a.Value == country) ||
                    (a.Key == "contact-information.country" && a.Value == country) ||
                    (a.Key == "tax-resident.residentCountry" && a.Value == country) ||
                    (a.Key == "finalize.country" && a.Value == country)));
            }

            // Add expression to filter on legal entity
            var legalEntityPredicate = GetLegalEntityPredicate(filter);
            predicate = predicate.And(ExpressionExtensions.Combine<Submission, LegalEntity, bool>(s => s.LegalEntity, legalEntityPredicate));

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        protected virtual Expression<Func<LegalEntity, bool>> GetLegalEntityPredicate(SearchSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            var predicate = _authorizationFilterExpressionFactory.GetLegalEntityJurisdictionFilterPredicate(filter);

            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                // Only one of them needs to contain the search term
                var text = filter.GeneralSearchTerm;
                predicate = predicate.And(legalEntity => legalEntity.Name.Contains(text) ||
                                                         legalEntity.MasterClient.Code.Contains(text) ||
                                                         legalEntity.ReferralOffice.Contains(text) ||
                                                         legalEntity.Code.Contains(text) ||
                                                         legalEntity.LegacyCode.Contains(text));
            }
            else
            {
                // Use specific searches to 'AND' them
                if (!string.IsNullOrEmpty(filter.LegalEntitySearchTerm))
                {
                    var text = filter.LegalEntitySearchTerm;
                    predicate = predicate.And(legalEntity => legalEntity.Name.Contains(text) ||
                                                             legalEntity.Code.Contains(text) ||
                                                             legalEntity.LegacyCode.Contains(text));
                }

                if (!string.IsNullOrEmpty(filter.ReferralOfficeSearchTerm))
                {
                    var text = filter.ReferralOfficeSearchTerm;
                    predicate = predicate.And(legalEntity => legalEntity.ReferralOffice.Contains(text));
                }

                if (!string.IsNullOrEmpty(filter.MasterClientSearchTerm))
                {
                    var text = filter.MasterClientSearchTerm;
                    predicate = predicate.And(legalEntity => legalEntity.MasterClient.Code.Contains(text));
                }
            }

            return predicate;
        }

        /// <summary>
        /// Validates the data.
        /// </summary>
        /// <param name="data">The data to validate.</param>
        /// <param name="submission">The submission that the data is for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected virtual async Task ValidateUpdateInformationAsync(UpdateSubmissionInformationDTO data, Submission submission)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(data, nameof(data));
            Check.NotDefaultOrNull<DateTime>(data.StartAt, nameof(data.StartAt));
            Check.NotDefaultOrNull<DateTime>(data.EndAt, nameof(data.EndAt));

            // Check the submission date range
            if (data.StartAt > data.EndAt)
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The start at date cannot be later than the ends at date.");
            }

            var monthDifference = data.StartAt.Month - data.EndAt.Month + (12 * (data.StartAt.Year - data.EndAt.Year));
            if (monthDifference > 12)
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The end date cannot be more than 12 months greater than the start date.");
            }

            // Avoid overlapping periods between submissions in the same company and module.
            var overlappingSubmissions = await _submissionsRepository.FindByConditionAsync(
                s => (s.StartsAt < data.EndAt && s.EndsAt > data.StartAt) &&
                      s.Id != submission.Id &&
                      s.ModuleId == submission.ModuleId &&
                      s.LegalEntityId == submission.LegalEntityId);

            if (overlappingSubmissions.Any())
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATES.ToErrorCode(),
                    $"The date range selected overlaps with existing submissions.");
            }
        }

        /// <summary>
        /// Determines the new status for the submission when updating something.
        /// </summary>
        /// <param name="submission">The updated submission.</param>
        /// <returns>The new status.</returns>
        protected virtual SubmissionStatus GetSubmissionStatusOnUpdate(Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            // Set the status for the submission as Draft if it is currently temporal
            if (submission.Status == SubmissionStatus.Temporal)
            {
                return SubmissionStatus.Draft;
            }
            else if (submission.Status == SubmissionStatus.Scheduled)
            {
                if (submission.FormDocument != null)
                {
                    if (submission.FormDocument.FormDocumentRevisions.Count == 1)
                    {
                        // Still first version so back to draft
                        return SubmissionStatus.Draft;
                    }
                    else
                    {
                        // Multiple revisions so back to revision
                        return SubmissionStatus.Revision;
                    }
                }
            }

            return submission.Status;
        }

        /// <summary>
        /// Sets the last activity in the attributes of the submission.
        /// </summary>
        /// <param name="submission">The submission to set the activity attributes on.</param>
        protected virtual void SetLastActivity(Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            submission.Attributes.SetAttributeValue<DateTime>(WellknownSubmissionAttributes.LastActivityAt, DateTime.UtcNow);
            submission.Attributes.SetAttributeValue<Guid?>(WellknownSubmissionAttributes.LastActivityBy, _workContext.IdentityUserId);
        }
    }
}
