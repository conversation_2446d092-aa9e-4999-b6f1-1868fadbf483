﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Messaging.Tokens;
using NetProGroup.Framework.Services.Communication;
using NetProGroup.Framework.Services.Communication.Services;
using NetProGroup.Framework.Services.Communication.EFRepository;
using NetProGroup.Trust.Application.Communication;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.Tests.Shared;
using AutoMapper; 

namespace NetProGroup.Trust.Tests.Communication
{
    [TestFixture()]
    public class CommunicationAppServiceTests : TestBase
    {
        private CommunicationAppService _sut;
        private Mock<IEmailService> _mockEmailService;
        private TrustOfficeOptions _trustOfficeOptions;
        private string _capturedRecipient;

        [SetUp]
        public void Setup()
        {
            // Get the real TrustOfficeOptions from DI
            _trustOfficeOptions = _server.Services.GetRequiredService<IOptions<TrustOfficeOptions>>().Value;
            _trustOfficeOptions.AllowedDomains = "netprogroup.com";

            // Create a mock for IEmailService
            _mockEmailService = new Mock<IEmailService>();
            _mockEmailService
                .Setup(m => m.SendEmailByTemplateNameAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<TokenList>(),
                    It.IsAny<bool>(),
                    It.IsAny<List<Guid>>()))
                .Callback<string, string, TokenList, bool, List<Guid>>((recipient, _, _, _, _) =>
                {
                    _capturedRecipient = recipient;
                })
                .ReturnsAsync(Guid.NewGuid());
            // Create the system under test with the mock email service
            _sut = new CommunicationAppService(
                _server.Services.GetRequiredService<ILogger<CommunicationAppService>>(),
                _server.Services.GetRequiredService<IMapper>(),
                _server.Services.GetRequiredService<Framework.Mvc.Contexts.IWorkContext>(),
                _server.Services.GetRequiredService<Microsoft.Extensions.Configuration.IConfiguration>(),
                _server.Services.GetRequiredService<IInboxService>(),
                _server.Services.GetRequiredService<IInboxRepository>(),
                _mockEmailService.Object,
                _server.Services.GetRequiredService<Framework.Services.Identity.Services.IUserManager>(),
                _server.Services.GetRequiredService<Framework.Services.Identity.Repository.IUserRepository>(),
                _server.Services.GetRequiredService<ICommunicationManager>(),
                _server.Services.GetRequiredService<ISystemAuditManager>(),
                _server.Services.GetRequiredService<Framework.Services.Configuration.IConfigurationManager>(),
                Options.Create(_trustOfficeOptions),
                _server.Services.GetRequiredService<ILegalEntitiesDataManager>()
            );
        }

        [Test]
        public async Task SendAnnouncementAsync_WithNonAllowedEmailDomain_UsesRecipientOverride()
        {
            // Arrange
            string originalRecipient = "<EMAIL>"; // Non-allowed domain
            string overrideRecipient = "<EMAIL>";
            string emailSubject = "Test Announcement";
            string templateName = "TestTemplate";

            // Configure the override recipient
            _trustOfficeOptions.RecipientOverride.Announcement = overrideRecipient;

            // Act
            await _sut.SendAnnouncementAsync(originalRecipient, emailSubject, templateName);

            // Assert
            _capturedRecipient.Should().Be(overrideRecipient, "the override recipient should be used");
        }

        [Test]
        public async Task SendAnnouncementAsync_WithAllowedEmailDomain_UsesOriginalRecipient()
        {
            // Arrange
            string originalRecipient = "<EMAIL>"; // Allowed domain
            string overrideRecipient = "<EMAIL>";
            string emailSubject = "Test Announcement";
            string templateName = "TestTemplate";

            // Configure the allowed domains
            _trustOfficeOptions.RecipientOverride.Announcement = overrideRecipient;

            // Act
            await _sut.SendAnnouncementAsync(originalRecipient, emailSubject, templateName);

            // Assert
            _capturedRecipient.Should().Be(originalRecipient, "the original recipient should be used");
        }

        [Test]
        public async Task SendAnnouncementAsync_WithNonAllowedEmailDomain_AndEmptyOverride_ThrowsException()
        {
            // Arrange
            string originalRecipient = "<EMAIL>"; // Non-allowed domain
            string emailSubject = "Test Announcement";
            string templateName = "TestTemplate";

            // Configure the allowed domains but with empty override
            _trustOfficeOptions.RecipientOverride.Announcement = string.Empty;

            // Act & Assert
            Func<Task> act = async () => await _sut.SendAnnouncementAsync(originalRecipient, emailSubject, templateName);

            var exception = (await act.Should().ThrowAsync<BadRequestException>()).And;
            exception.Should().NotBeNull("the exception should not be null");
            exception.Message.Should().NotBeNullOrEmpty("the exception message should not be null or empty");
            exception.Message.Should().Contain("Recipient email", "the exception should indicate that the recipient email is not allowed");
            exception.Message.Should().Contain("not allowed", "the exception should indicate that the recipient email is not allowed");
            exception.Message.Should().Contain("no override recipient is configured", "the exception should indicate that no override recipient is configured");
        }
    }
}