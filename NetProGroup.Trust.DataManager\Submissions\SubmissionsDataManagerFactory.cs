﻿// <copyright file="SubmissionsDataManagerFactory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.DataManager.Submissions.BVI.EconomicSubstance;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Factory for creating the appropriate SubmissionsDataManager for the jurisdiction/module.
    /// </summary>
    public class SubmissionsDataManagerFactory : ISubmissionsDataManagerFactory
    {
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsDataManagerFactory"/> class.
        /// </summary>
        /// <param name="serviceProvider">The serviceprovider instance.</param>
        public SubmissionsDataManagerFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Creates the SubmissionsDataManager for the jurisdiction and module.
        /// </summary>
        /// <param name="jurisdictionCode">The code of the jurisdiction.</param>
        /// <param name="moduleKey">The key of the module.</param>
        /// <returns>The created SubmissionsDataManager or null if not implemented.</returns>
        public ICommonSubmissionsDataManager CreateSubmissionsDataManager(string jurisdictionCode, string moduleKey)
        {
            return jurisdictionCode switch
            {
                JurisdictionCodes.BritishVirginIslands => CreateBVISubmissionsDataManager(moduleKey),
                _ => null
            };
        }

        private ICommonSubmissionsDataManager CreateBVISubmissionsDataManager(string moduleKey)
        {
            return moduleKey switch
            {
                ModuleKeyConsts.EconomicSubstanceBVI => _serviceProvider.GetRequiredService<IBVIESSubmissionsDataManager>(),
                _ => null
            };
        }
    }
}
