using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Reports;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual
{
    /// <summary>
    /// Interface for exporting 2019 submissions.
    /// </summary>
    public interface IExport2019SubmissionsIRDGenerator : IExportYearSubmissionsIRDGenerator, IScopedService;

    /// <summary>
    /// Manager for exporting 2019 submissions.
    /// </summary>
    public class Export2019SubmissionsIRDGenerator : BaseExportSubmissionsIRDGenerator, IExport2019SubmissionsIRDGenerator
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Export2019SubmissionsIRDGenerator"/> class.
        /// </summary>
        /// <param name="logger">the logger.</param>
        /// <param name="submissionsManager">The submissions manager.</param>
        /// <param name="templateProvider">template provider.</param>
        public Export2019SubmissionsIRDGenerator(ILogger<BaseExportSubmissionsIRDGenerator> logger,
            ISubmissionReportsDataManager submissionsManager,
            IReportTemplateProvider templateProvider) : base(logger, templateProvider, submissionsManager)
        {
        }

        /// <inheritdoc />
        protected override int Year { get => 2019; }

        /// <inheritdoc />
        protected override string ExportModule { get => ModuleKeyConsts.SimplifiedTaxReturn; }

        /// <inheritdoc />
        protected override string ExportJurisdiction { get => JurisdictionCodes.Nevis; }

        /// <inheritdoc/>
        public override async Task<ReportDownloadResponseDTO> ExportAsync(ExportSubmissionDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var template = await GetTemplateContentAsync();

            // Modify the file in memory with ClosedXML
            using var workbook = new XLWorkbook(template);
            var submissions = await GetSubmissions(request);

            ModifyWorkbook(workbook, submissions);

            // Save modified workbook to a new FileStream stream
            var modifiedStream = new MemoryStream();
            workbook.SaveAs(modifiedStream);
            return CreateResponse(modifiedStream);
        }

        private static void ModifySchedule1TabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            var worksheet = workbook.Worksheet(2);

            int row = 3; // Starting row (assuming the first row is for headers)

            foreach (var submission in submissions)
            {
                var form = submission.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
                foreach (var index in GetIndexForIntellectualProperties(form!.DataSet))
                {
                    // Column A: Morning_Star_Id
                    worksheet.Cell(row, 1).Value = submission.ReportId;

                    // Column B: IntellectualPropertiesAssetsAcquiredDescription
                    worksheet.Cell(row, 2).Value = TryGetFormValue(form, FormKeys.IntellectualPropertiesAssetsAcquiredDescription(index));

                    // Column C: IntellectualPropertiesAssetsAcquiredDate
                    worksheet.Cell(row, 3).Value = FormatDate(form, FormKeys.IntellectualPropertiesAssetsAcquiredDate(index));

                    // Column D: IntellectualPropertiesAssetsAcquiredIncome
                    worksheet.Cell(row, 4).Value = TryGetFormValue(form, FormKeys.IntellectualPropertiesAssetsAcquiredIncome(index));

                    // Column E: Schedule Type
                    worksheet.Cell(row, 5).Value = "Schedule 1";
                    // Increment the row for the next submission
                    row++;
                }
            }
        }

        private static void ModifySchedule2TabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            var worksheet = workbook.Worksheet(3);

            int row = 3; // Starting row (assuming the first row is for headers)

            foreach (var submission in submissions)
            {
                var form = submission.FormDocument.FormDocumentRevisions.FirstOrDefault()?.GetFormBuilder().Form as KeyValueForm;
                foreach (var index in GetIndexForAccountingActivities(form!.DataSet))
                {
                    // Column A: Morning_Star_Id
                    worksheet.Cell(row, 1).Value = submission.ReportId;

                    // Column B: CorporateAccountingActivitiesDescription
                    worksheet.Cell(row, 2).Value = TryGetFormValue(form, FormKeys.CorporateAccountingActivitiesDescription(index));

                    // Column C: CorporateAccountingActivities
                    var activitiesArray = new List<string>()
                    {
                        TryGetFormKey(form, FormKeys.CorporateAccountingActivitiesRelatedPartyIntellectualProperty(index), "Related Party Intellectual Property"),
                        TryGetFormKey(form, FormKeys.CorporateAccountingActivitiesNonRelatedIntellectualProperty(index), "Non-Related Party Intellectual Property"),
                        TryGetFormKey(form, FormKeys.CorporateAccountingActivitiesNonIntellectualProperty(index), "Non-Intellectual Property"),
                    };

                    worksheet.Cell(row, 3).Value = string.Join(", ", activitiesArray.Where(s => s != null));

                    // Column D: CorporateAccountingActivitiesIncome
                    worksheet.Cell(row, 4).Value = TryGetFormValue(form, FormKeys.CorporateAccountingActivitiesIncome(index));

                    // Column E: Schedule Type
                    worksheet.Cell(row, 5).Value = "Schedule 2";

                    // Increment the row for the next submission
                    row++;
                }
            }
        }

        private static string TryGetFormValue(KeyValueForm form, string key)
        {
            if (form?.DataSet.TryGetValue(key, out var value) == true)
            {
                return value;
            }
            else
            {
                return "";
            }
        }

        private void ModifyWorkbook(XLWorkbook workbook, List<Submission> submissions)
        {
            ModifySubmissionTabTemplate(workbook, submissions);
            ModifySchedule1TabTemplate(workbook, submissions);
            ModifySchedule2TabTemplate(workbook, submissions);
        }

        private void ModifySubmissionTabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            var worksheet = workbook.Worksheet(1);

            int row = 3; // Starting row (assuming the first row is for headers)

            foreach (var submission in submissions)
            {
                LogSubmission(submission.Id);

                var form = submission.FormDocument.FormDocumentRevisions.FirstOrDefault()?.GetFormBuilder().Form as KeyValueForm; // TODO should be lastordefault?

                var values = new List<XLCellValue>();

                // Morning_Star_Id
                values.Add(submission.ReportId);

                // TAX_PAYER_NO
                values.Add(""); // This column is empty

                // COMP_REGIST_NO
                values.Add(form!.DataSet[FormKeys.CompanyCode]);

                // CORP_NAME
                values.Add(GetValueOrDefault(form, FormKeys.CompanyName));

                // FISC_YR_START
                values.Add(new DateTime(submission.FinancialYear!.Value, 1, 1).ToString(WellKnownReportConstants.DateFormat));

                // FISC_YR_END
                values.Add(new DateTime(submission.FinancialYear.Value, 12, 31).ToString(WellKnownReportConstants.DateFormat));

                // HEAD_OFFICE_ADDRESS1
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeAddress1));

                // HEAD_OFFICE_ADDRESS2
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeAddress2));

                // STKNV_OFFICE_ADDRESS1
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisAddress1));

                // STKNV_OFFICE_ADDRESS2
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisAddress2));

                // CONTACT_PERSON_NAME
                values.Add(GetValueOrDefault(form, FormKeys.ContactName));

                // CONTACT_PERSON_POSITION
                values.Add(GetValueOrDefault(form, FormKeys.ContactPosition));

                // CONTACT_PERSON_ADDRESS
                values.Add(GetValueOrDefault(form, FormKeys.ContactAddress1));

                // CONTACT_PERSON_PHONE
                values.Add(GetPhoneNumberWithPrefix(form, FormKeys.ContactTelephoneNumber, FormKeys.ContactTelephonePrefix));

                // CONTACT_PERSON_FAX
                values.Add(GetPhoneNumberWithPrefix(form, FormKeys.ContactFaxNumber, FormKeys.ContactFaxPrefix));

                // CONTACT_PERSON_EMAIL
                values.Add(GetValueOrDefault(form, FormKeys.ContactEmail));

                // COMP_REP_NAME
                values.Add(GetValueOrDefault(form, FormKeys.CompanyRepresentativeName)); // This column is marked as empty

                // COMP_REP_POSITION
                values.Add(""); // This column is marked as empty

                // COMP_REP_ADDRESS
                values.Add(form.DataSet[FormKeys.HeadOfficeNevisAddress1]);

                // COMP_REP_PHONE
                values.Add(GetPhoneNumberWithPrefix(form, FormKeys.CompanyRepresentativeTelephoneNumber, FormKeys.CompanyRepresentativeTelephonePrefix));

                // COMP_REP_FAX
                values.Add(GetPhoneNumberWithPrefix(form, FormKeys.CompanyRepresentativeFaxNumber, FormKeys.CompanyRepresentativeTelephonePrefix));

                // COMP_REP_EMAIL
                values.Add(GetValueOrDefault(form, FormKeys.CompanyRepresentativeEmail));

                // BUSINESS_ACTIVITY
                var firstOrPrimaryActivity = GetFirstOrPrimaryActivity(form);
                values.Add(firstOrPrimaryActivity);

                // USERID
                values.Add(""); // This column is marked as empty

                // INSERT_DATE
                values.Add(submission.CreatedAt.ToString(WellKnownReportConstants.DateFormat));

                // UPDATE_DATE
                values.Add(submission.UpdatedAt.ToString(WellKnownReportConstants.DateFormat));

                // APPLICATION_STATUS
                values.Add("PAID");

                // SUBMIT_DATE
                values.Add(submission.SubmittedAt?.ToString(WellKnownReportConstants.DateFormat));

                // DECL_SIGN
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeNameOfPersonDeclaring));

                // DECL_ADDRESS
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAddressOfPersonDeclaring));

                // HEAD_OFFICE_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.HeadOfficeCountry)));

                // HEAD_OFFICE_CITY
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeCity));

                // HEAD_OFFICE_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeZipCode));

                // STKNV_OFFICE_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCountry)));

                // STKNV_OFFICE_CITY
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCity));

                // STKNV_OFFICE_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisZipCode));

                // CONTACT_PERSON_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.ContactCountry)));

                // CONTACT_PERSON_CITY
                values.Add("");

                // CONTACT_PERSON_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.ContactZipCode));

                // COMP_REP_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCountry)));

                // COMP_REP_CITY
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisCity));

                // COMP_REP_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.HeadOfficeNevisZipCode));

                // CONTACT_PERSON_LASTNAME
                values.Add("");

                // COMP_REP_LASTNAME
                values.Add("");

                // BUSINESS_ACTIVITY_NEW
                values.Add(firstOrPrimaryActivity);

                var haveIncomeGenerated = bool.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingAssessableIncomeGenerated, "false"));
                var incorporatedBefore2019 = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentIncorporatedBefore2019, "false"));
                var nonTaxResident = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentNonTaxResident, "false"));
                var isPartOfMneGroup = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNEIsPartOfMNEGroup, "false"));
                var requiresCbeReport = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNERequiresCbCReport, "false"));

                // Question No 1 (Yes/No)
                values.Add(incorporatedBefore2019 ? "True" : "False");

                // Question No 1.1 (Yes/No)
                values.Add(incorporatedBefore2019
                    ? ""
                    :
                    form.DataSet.TryGetValue(FormKeys.TaxResidentNonTaxResident, out var taxResidentNonTaxResident1)
                        ?
                        Boolean.Parse(taxResidentNonTaxResident1) ? "True" : "False"
                        :
                        "");

                // Question No 1.2 (Yes/No)
                values.Add(nonTaxResident
                    ? ""
                    : GetCountryOrNoCountry(GetValueOrDefault(form, FormKeys.TaxResidentResidentCountry)));

                // Question No 2 (Yes/No)
                values.Add((!incorporatedBefore2019 && !nonTaxResident)
                    ? ""
                    :
                    form.DataSet.TryGetValue(FormKeys.IntellectualPropertiesAcquired, out var intellectualPropertiesAcquired)
                        ?
                        Boolean.Parse(intellectualPropertiesAcquired) ? "True" : "False"
                        : "");

                // Question No 3 (Yes/No)
                values.Add((!incorporatedBefore2019 && !nonTaxResident)
                    ? ""
                    : haveIncomeGenerated
                        ? "True"
                        : "False");

                // Question No 3.1 (Yes/No)
                values.Add(
                    (!incorporatedBefore2019 && !nonTaxResident && !haveIncomeGenerated)
                        ? ""
                        : Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesCondition, "false"))
                            ? "True"
                            : "False");

                // Question No 4 (Yes/No)
                values.Add(
                    (!incorporatedBefore2019 && !nonTaxResident)
                        ? "" :
                    isPartOfMneGroup
                        ? "True"
                        : "False");

                // Question No 4.1 (Yes/No)
                values.Add(
                    (!incorporatedBefore2019 && !nonTaxResident)
                        ? ""
                        : isPartOfMneGroup
                            ? requiresCbeReport
                                ? "True"
                                : "False"
                            : "");

                // Question No 4.2 (Yes/No)
                values.Add("");

                // APPLICATION_ID
                values.Add("");

                // RETURN_BEHALF
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeOnMyOwnBehalf));

                // RETURN_OFFICER_CORP
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAsOfficer));

                // RETURN_AC_AT_MGR
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAsAttorney));

                // RETURN_TR_EX_AR
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeAsTrustee));

                // DECL_DATE
                values.Add(FormatDate(form, FormKeys.FinalizeDateOfSignature));

                // DECLARANT_COUNTRY
                values.Add(CountryCodeMapper.GetCountryName(GetValueOrDefault(form, FormKeys.FinalizeCountry)));

                // DECLARANT_CITY
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeCity));

                // DECLARANT_ZIP_CODE
                values.Add(GetValueOrDefault(form, FormKeys.FinalizeZipCode));

                for (int i = 1; i <= values.Count; i++)
                {
                    worksheet.Cell(row, i).Value = values[i - 1];
                }

                // Increment the row for the next submission
                row++;
            }
        }
    }
}
