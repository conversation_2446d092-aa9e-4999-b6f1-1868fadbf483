// <copyright file="BasicFinancialReportRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Domain.Shared.Reports;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Bahamas.EconomicSubstance.Populators
{
    /// <summary>
    /// Populate a row for the basic financial report module report.
    /// </summary>
    public class EconomicSubstanceReportRowPopulator : LinePopulatorBase, IEconomicSubstanceReportRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, ListSubmissionBahamasDTO data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Set the Email
            SetCellValueAndStyle(worksheet, currentRow, 1, data.CreatedByEmail);

            // Entity Name
            SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntityName);

            // Set the Entity Number 
            SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntityCode);

            // Set the  Master Client Code  
            SetCellValueAndStyle(worksheet, currentRow, 4, data.MasterClientCode);

            // Set the Company Number  
            SetCellValueAndStyle(worksheet, currentRow, 5, data.IncorporationCode);

            // Set the Status
            SetCellValueAndStyle(worksheet, currentRow, 6, data.Status.ToString());

            // Set the Created
            SetCellValueAndStyle(worksheet, currentRow, 7, data.CreatedAt.ToString(WellKnownReportConstants.DateFormat));

            // Set the Submitted
            SetCellValueAndStyle(worksheet, currentRow, 8, data.SubmittedAt.HasValue ? data.SubmittedAt.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the Re - Open 
            SetCellValueAndStyle(worksheet, currentRow, 9, data.ReopenedAt.HasValue ? data.ReopenedAt.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the Re - Submitted    
            SetCellValueAndStyle(worksheet, currentRow, 10, data.SubmittedAt.HasValue ? data.SubmittedAt.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the RFI
            SetCellValueAndStyle(worksheet, currentRow, 11, data.RequestForinformation);

            // Set the RFI completed 
            SetCellValueAndStyle(worksheet, currentRow, 12, data.RequestForInformationCompletedAt.HasValue ? data.RequestForInformationCompletedAt.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the Incorporation Date 
            SetCellValueAndStyle(worksheet, currentRow, 13, data.IncorporationDate.HasValue ? data.IncorporationDate.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the payment date
            SetCellValueAndStyle(worksheet, currentRow, 14, data.PaymentReceivedAt.HasValue ? data.PaymentReceivedAt.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the payment reference
            SetCellValueAndStyle(worksheet, currentRow, 15, data.PaymentReference);

            // Set the Financial Period Enddate    
            SetCellValueAndStyle(worksheet, currentRow, 16, data.FinancialPeriodEndsAt.HasValue ? data.FinancialPeriodEndsAt.Value.ToString(WellKnownReportConstants.DateFormat) : string.Empty);

            // Set the Referral Office 
            SetCellValueAndStyle(worksheet, currentRow, 17, data.LegalEntityReferralOffice);

            // Set the None
            SetCellValueAndStyle(worksheet, currentRow, 18, data.HasActivityNone);

            // Set the BB
            SetCellValueAndStyle(worksheet, currentRow, 19, data.HasActivityBankingBusiness);

            // Set the IB
            SetCellValueAndStyle(worksheet, currentRow, 20, data.HasActivityInsuranceBusiness);

            // Set the FMB
            SetCellValueAndStyle(worksheet, currentRow, 21, data.HasActivityFundManagementBusiness);

            // Set the FLB
            SetCellValueAndStyle(worksheet, currentRow, 22, data.HasActivityFinanceLeasingBusiness);

            // Set the  HQ
            SetCellValueAndStyle(worksheet, currentRow, 23, data.HasActivityHeadquartersBusiness);

            // Set the SB
            SetCellValueAndStyle(worksheet, currentRow, 24, data.HasActivityShippingBusiness);

            // Set the HB
            SetCellValueAndStyle(worksheet, currentRow, 25, data.HasActivityHoldingBusiness);

            // Set the IP
            SetCellValueAndStyle(worksheet, currentRow, 26, data.HasActivityIntellectualPropertyBusiness);

            // Set the SC
            SetCellValueAndStyle(worksheet, currentRow, 27, data.HasActivityShippingBusiness);
        }
    }
}