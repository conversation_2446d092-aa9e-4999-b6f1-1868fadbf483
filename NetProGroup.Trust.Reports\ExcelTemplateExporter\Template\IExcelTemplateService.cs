using ClosedXML.Excel;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter.Template
{
    /// <summary>
    /// Interface for Excel template service.
    /// </summary>
    public interface IExcelTemplateService<T> // TODO: ITransientService

    // we're expecting to use the service lifetime
    // but the framework doesn't support it yet (open generic type in the dependency injection container.)
    {
        /// <summary>
        /// Applies the template to the workbook.
        /// </summary>
        /// <param name="workbook">The workbook to apply the template to.</param>
        /// <param name="data">The data to apply the template to.</param>
        /// <param name="config">The configuration for the template.</param>
        /// <param name="worksheetIndex">The index of the worksheet to apply the template to.</param>
        void ApplyTemplate(XLWorkbook workbook, IEnumerable<T> data, TemplateConfiguration config, int worksheetIndex = 0);

        /// <summary>
        /// Registers a row populator for a specific line number.
        /// </summary>
        /// <param name="populator">The populator to register.</param>
        public void RegisterRowPopulator(ITemplateRowPopulator<T> populator);

        /// <summary>
        /// Cleans the row populator.
        /// </summary>
        public void ClearRowPopulator();
    }
}