// <copyright file="ISimplifiedTaxReturnGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;

namespace NetProGroup.Trust.Reports.Nevis.SimplifiedTaxReturn
{
    /// <summary>
    /// Interface for simplied tax return.
    /// </summary>
    public interface ISimplifiedTaxReturnGenerator : ITransientService
    {
        /// <summary>
        /// Generates a submissions report for the module.
        /// </summary>
        /// <param name="submissions">The list of submissions to be included in the report.</param>
        /// <returns>A <see cref="Task{ReportOutput}"/> representing the asynchronous operation.</returns>
        Task<ReportOutput> GenerateSubmissionsReportAsync(List<SubmissionNevisReportDTO> submissions);
    }
}
