using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Client returned information scherma.
    /// </summary>
    [BsonIgnoreExtraElements]
    public sealed class ClientReturnedInformationSchema : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the IsCancelled.
        /// </summary>
        [BsonElement("is_canceled")]
        public bool IsCanceled { get; set; }

        /// <summary>
        /// Gets or sets the request id.
        /// </summary>
        [BsonElement("request_id")]
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the username.
        /// </summary>
        [BsonElement("username")]
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets the requested_at.
        /// </summary>
        [BsonElement("requested_at")]
        public DateTime? RequestedAt { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonElement("comment")]
        public string Comment { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonElement("files")]
        public List<FileSchema> Files { get; set; }
    }
}
