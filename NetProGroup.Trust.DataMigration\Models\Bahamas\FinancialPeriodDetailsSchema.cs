using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Financial period details schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class FinancialPeriodDetailsSchema
    {
        /// <summary>
        /// Gets or sets the date and time when the financial period begins.
        /// </summary>
        [BsonElement("financial_period_begins")]
        public DateTime FinancialPeriodBegins { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the financial period ends.
        /// </summary>
        [BsonElement("financial_period_ends")]
        public DateTime FinancialPeriodEnds { get; set; }

        /// <summary>
        /// Gets or sets has_financial_period_changed.
        /// </summary>
        [BsonElement("has_financial_period_changed")]
        public bool HasFinancialPeriodChanged { get; set; }

        /// <summary>
        /// Gets or sets has_reclassification.
        /// </summary>
        [BsonElement("has_reclassification")]
        public bool? HasReclassification { get; set; }

        /// <summary>
        /// Gets or sets has_otas.
        /// </summary>
        [BsonElement("has_otas")]
        public bool? HasOtas { get; set; }

        /// <summary>
        /// Gets or sets has_otas_receipt.
        /// </summary>
        [BsonElement("has_otas_receipt")]
        public bool? HasOtasReceipt { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonElement("files")]
        public List<FileSchema> Files { get; set; }
    }
}
