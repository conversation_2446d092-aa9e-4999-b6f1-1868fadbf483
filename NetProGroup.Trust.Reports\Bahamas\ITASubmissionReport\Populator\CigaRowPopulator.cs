// <copyright file="CigaRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Repository.Migrations;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class CigaRowPopulator : LinePopulatorBase, ICigaRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;
            var relevantActivityIndexes = GetRelevantActivityIndexes(form!.DataSet);
            
            List<int> GetRelevantActivityIndexes(Dictionary<string, string> dataSet)
            {
                var pattern = @"relevant-activity-declaration\.relevantActivities\.(\d+)\.";
                return BaseExportSubmissionsIRDGenerator.GetFormIndex(dataSet, pattern);
            }

            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.RelevantActivities).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.RelevantActivities)[1].Split(".")[0]);

            foreach (var index in relevantActivityIndexes)
            {
                // Check if the activity was selected
                var isSelected = bool.Parse(GetValueOrDefault(form, FormKeys.RelevantActivitiesIsSelected(index), "false"));

                if (isSelected)
                {
                    // Retrieve the name
                    var relevantActivity = GetValueOrDefault(form, FormKeys.RelevantActivitiesLabel(index));

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Retrieve the relevant activity data
                    var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                    // Retrieve the created premises
                    var cigas = relevantActivityData.GetAttributesWithKey(WellKnownFormDocumentAttibuteKeys.CigaActivity).ToList();

                    // Group the premises
                    var cigaGroups = cigas.GroupBy(a => a.Key.Split(relevantActivityKey + WellKnownFormDocumentAttibuteKeys.CigaActivity)[1].Split(".")[0]);

                    foreach (var cigaGroup in cigaGroups)
                    {
                        // Retrieve the entity unique id
                        var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                        SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                        // Retrieve the name
                        var activityName = cigaGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                        SetCellValueAndStyle(worksheet, currentRow, 2, activityName);

                        var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                        SetCellValueAndStyle(worksheet, currentRow, 3, financialPeriodEndDate);

                        // Retrieve the ciga code
                        var cigaCode = cigaGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Description);

                        SetCellValueAndStyle(worksheet, currentRow, 4, cigaCode);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}