// <copyright file="IReportsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Trust.Application.Contracts.Reports;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Reports
{
    /// <summary>
    /// Interface for the ReportDataManager.
    /// </summary>
    public interface IReportsDataManager : IScopedService
    {
        /// <summary>
        ///  Adds a new report.
        /// </summary>
        /// <param name="report">The report entity to add to the system.</param>
        /// <returns>A new report entity.</returns>
        public Task<Domain.Report.Report> AddReport(Domain.Report.Report report);

        /// <summary>
        /// Checks if a report exists.
        /// </summary>
        /// <param name="report">The report to check for existence.</param>
        /// <returns>True if exists.</returns>
        public Task<bool> ReportExists(Domain.Report.Report report);

        /// <summary>
        /// Downloads a financial report.
        /// </summary>
        /// <param name="reportId">The id of the report to download.</param>
        /// <returns>The financial report download response.</returns>
        Task<Document> GetDocument(Guid reportId);

        /// <summary>
        /// Gets a paginated list of reports by specified types.
        /// </summary>
        /// <param name="request">The request containing the parameters for the search.</param>
        /// <param name="createdAfter">Limit reports to created after this date.</param>
        /// <returns>The list of reports matching the criteria.</returns>
        Task<IPagedList<ReportDTO>> GetReportsByTypeAsync(ReportRequestDTO request, DateTime? createdAfter = null);

        /// <summary>
        /// Checks if a report exists by its id.
        /// </summary>
        /// <param name="reportId">The id of the report to check.</param>
        /// <returns>The report entity if it exists.</returns>
        Task<Domain.Report.Report> CheckReportByIdAsync(Guid reportId);
    }
}