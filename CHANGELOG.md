# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.5.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GTv1.5.0&targetVersion=GTv1.5.1&_a=files) (2025-08-18)

## [1.5.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GTv1.4.0&targetVersion=GTv1.5.0&_a=files) (2025-08-18)


### Features

* Bahamas submission migration ([fe1d04b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/fe1d04b92f55103a19a025003a52f2ddbad476ec)), closes [#16031](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16031)
* **FeatureFlags:** Add FeatureFlag implementation ([2f05ccd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/2f05ccd99a50e5defc372b5b2ea1899d6897d39e)), closes [#17869](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17869) [#17872](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17872)

## [1.4.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GTv1.3.1&targetVersion=GTv1.4.0&_a=files) (2025-08-13)


### Features

* Added deployment pipeline for ACC2/PRD2 environments ([5af6303](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/5af6303eb280ab437e75d5c2af68700801982011)), closes [#18644](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18644)
* Added LegalEntityVPStatus to ListSubmissionBahamasDTO ([b72712f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b72712f379ffbf4a2679217e538cda0a1c9028c2)), closes [#11763](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11763) [#18670](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18670)
* Removed SyncExcludedLegalEntities table ([7f9ab50](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/7f9ab5083e6e0e8bedbf11a6df726d56bcd9cf8e)), closes [#18553](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18553)


### Bug Fixes

* Addded missing sortable columns to management/submissions request ([57dd03b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/57dd03b7d97d54d8f286d2ff4e4d0058adef1259)), closes [#18353](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18353)
* Added missing properties to Announcement endpoints ([027a1cb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/027a1cbe71084c77278e78c0fd2980a06d74e69c)), closes [#18545](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18545)
* Incorrect activity log messages in company sync ([ba0313e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ba0313eabd232b17726723d02aa9713024c4e053)), closes [#17332](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17332) [#18727](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18727)

## [1.3.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GTv1.3.0&targetVersion=GTv1.3.1&_a=files) (2025-08-07)

## [1.3.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GTv1.2.0&targetVersion=GTv1.3.0&_a=files) (2025-08-06)


### Features

* Bahamas company migration logic implemented ([9d1b187](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9d1b187e777d67deafd0eaf596e4952c72892757)), closes [#16030](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16030)


### Bug Fixes

* Added IsIndividual to SearchBoDirRequestDTO ([70f5694](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/70f5694c8a46adc45ea9367fe79cc5d7aa64ad13))
* Added script and pipeline to truncate SyncExcludedLegalEntities table ([24e30de](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/24e30de6c31c7eaa972c3076ec5235143abe42ef)), closes [#18552](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18552)
* Annoucement sorting fields ([23e54f3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/23e54f3576ede7977cf0170b04eab4cb9254f12b)), closes [#18375](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18375)
* Fixed line exchange rate value ([7722608](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/77226084ccc74c541ec66760dfe0a1db22943293)), closes [#18535](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18535) [#18535](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18535)
* MasterClient search issue ([1cecdf0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/1cecdf02c08114e6e9edb5ffd3ac8b4909b72e1b))
* Output datetimes from the API according to ISO 8601. Removed "local" fields to allow the front-end to handle presentation of times, refers [#18324](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18324), [#18325](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18325), [#18328](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18328) ([79b873f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/79b873f984219ca5121409b7ce19ce83aae1c583))
* Reset ExportedAt on submission reopen; add tests. fixes [#18150](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18150) ([c1bd9b3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/c1bd9b3731224e297ed4d0de661e1ba13dca07d3))

## [1.2.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GTv1.1.2&targetVersion=GTv1.2.0&_a=files) (2025-07-24)


### Features

* Added missing sorting fields on user list table. Refers to [#18269](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18269). ([bdac56b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/bdac56b9a198c8e85749a0a0757ad443ed47aaca))
* Updated Permissions V12. Refers [#14322](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14322) ([8d0d565](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/8d0d56500d194200e9717fbd9160af14ddcce709))


### Bug Fixes

* Converting the StartAt and EndAt values from the data object (UpdateSubmissionInformationDTO data) to UTC using .ToUniversalTime(). ([6944b56](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6944b563ddb611f6d27027c73a35a2476492c742)), closes [#18131](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18131) [#17794](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17794) [#17794](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17794) [#18131](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18131)
* Fixed Query to get only Companies with submissions for Submission Not Paid Report (18186). Fixes Fix: Fixed Query to get only Companies with submissions for Submission Not Paid Report (18186). Fixes [#18186](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18186) ([a9a2b4f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a9a2b4fc78f538eb244b5adf2350508b122f1c69))
* Fixes the timestamp text in the activity log. Fixes [#18200](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18200). ([d0b7363](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d0b7363911a2537256c460d5f2c75a0a597abdf9))
* Marking a message as read does not work for a client user. fixes [#18163](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18163) ([171e14d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/171e14d03877f730c50575991787250abcccf8d0))
* Not showing Submission form content updated.  fixes [#17666](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17666) ([026350a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/026350a76dcaab92569810e9a36347e522acda32))
* Remove audit logging for submission financial period change. fixes [#18317](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18317) ([2f5970e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/2f5970e86f6347cbdef18298196758af337bc21f))

## [1.1.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.1.1&targetVersion=GTv1.1.2&_a=files) (2025-07-11)


### Bug Fixes

* Fixing permissions check for Annoucements feature (16807). Fixes [#16807](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16807). ([5999d0d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/5999d0d0cea6a4fba572443d5238023356f94541))

## [1.1.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.1.0&targetVersion=GTv1.1.1&_a=files) (2025-07-07)


### Bug Fixes

* Fix sync issues regarding onboarding status, fixes [#17332](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17332) ([42076ec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/42076eca3a1fd4af61ce852574e70ff7d54d8dd5))
* Include deleted submissions for Bah reports. fixes [#17968](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17968) ([f9698ec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f9698ec56f7db67d3def1443283f97114dc1d8c0))

## [1.1.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.0.9&targetVersion=GTv1.1.0&_a=files) (2025-07-04)


### Features

* Make possible to sync VP for bahamas and panama, refers [#17547](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17547) ([b0eef5c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b0eef5c18293e5fc662e822ba33990ccf99cecf2))


### Bug Fixes

* Announcements status does not update after marked as read action called. fixes [#17999](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17999) ([a67cfe2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a67cfe21a32fd854fb928412e64e7ddb5af9bee3))
* Submission reopened is recorded twice on an ES Submission log. ([f756400](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f7564008724b810060be951ace91bccca17bb182)), closes [#17929](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17929) [#17929](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17929)

## [1.0.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.0.8&targetVersion=GTv1.0.9&_a=files) (2025-07-03)


### Bug Fixes

* Fixed missing cuntry codes for Company Representative phones. Fixes 17608. ([cf5794b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/cf5794b5ddd7e510420d4899e9008be88b99869a))

## [1.0.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.0.7&targetVersion=GTv1.0.8&_a=files) (2025-06-25)


### Bug Fixes

* Fixed the mapping method for missing country codes. Refers to [#17608](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17608). ([b9005c9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b9005c91e5c9324346a081da89234904d5ecb082))
* Fixed the row populator method to generate ES Excel Report with correct RFI Completed Value. Fixes 16269 ([2024731](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/20247315212599fc87dbabffd29d014a7250f142))

## [1.0.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.0.6&targetVersion=GTv1.0.7&_a=files) (2025-06-18)


### Bug Fixes

* Update financial period in form and relevant activities, fixes [#16182](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16182), [#17794](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17794), [#17890](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17890), [#16482](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16482) ([26332b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/26332b9d8d272e6b7ae89217def9aa261180ff56))

## [1.0.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.0.5&targetVersion=GTv1.0.6&_a=files) (2025-06-16)


### Bug Fixes

* Fixed the mapping method for BO and Directors to tolarate ReceivedAt with null values, fixes [#17755](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17755) ([cf9e46f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/cf9e46f9bf19efca508ce51cff814142d5db8d62))

## [1.0.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.4&targetVersion=GTv1.0.5&_a=files) (2025-06-11)


### Bug Fixes

* Fixed issue where ReopenedAt as being mapped when submission was not reopened, refers [#17671](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17671) ([3f10a45](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3f10a45d530bce1b2b568416feafcafa4925eb2a))

## [1.0.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.3&targetVersion=GTv1.0.4&_a=files) (2025-06-06)


### Bug Fixes

* Removed unused fields ReopenedAt and ResubmittedAt from database and fixed the DTO to show ReopenedAt and InitialSubmittedAt in the DTO, refers [#16215](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16215) ([f53fa87](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f53fa87bd6a84ebb2c566bf92483662955f9e109))
* Show CreatedByEmail for ES submissions, fixes [#16129](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16129) ([7fe8e0c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/7fe8e0cbd0e439022be581ba37cb5ec8e1a325d1))
* Use AutoMapper projection to imrpove performance of submission export, refers [#17611](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17611) ([5251852](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/525185215ff13d57f54c6986d45a03cb7d455c18))

## [1.0.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.12&targetVersion=GTv1.0.3&_a=files) (2025-05-26)

## [1.0.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.10&targetVersion=GTv1.0.2&_a=files) (2025-05-22)

## [1.0.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.10&targetVersion=GTv1.0.2&_a=files) (2025-05-22)

## [1.0.2-alpha.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.6&targetVersion=GTv1.0.2-alpha.10&_a=files) (2025-05-22)

## [1.0.2-alpha.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.6&targetVersion=GTv1.0.2-alpha.9&_a=files) (2025-05-22)

## [1.0.2-alpha.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.6&targetVersion=GTv1.0.2-alpha.8&_a=files) (2025-05-22)

## [1.0.2-alpha.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.6&targetVersion=GTv1.0.2-alpha.7&_a=files) (2025-05-22)

## [1.0.2-alpha.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.5&targetVersion=GTv1.0.2-alpha.6&_a=files) (2025-05-22)

## [1.0.2-alpha.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.4&targetVersion=GTv1.0.2-alpha.5&_a=files) (2025-05-22)

## [1.0.2-alpha.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.3&targetVersion=GTv1.0.2-alpha.4&_a=files) (2025-05-22)

## [1.0.2-alpha.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.2&targetVersion=GTv1.0.2-alpha.3&_a=files) (2025-05-22)

## [1.0.2-alpha.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.1&targetVersion=GTv1.0.2-alpha.2&_a=files) (2025-05-22)

## [1.0.2-alpha.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.2-alpha.0&targetVersion=GTv1.0.2-alpha.1&_a=files) (2025-05-22)

## [1.0.2-alpha.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0&targetVersion=GTv1.0.2-alpha.0&_a=files) (2025-05-21)

## 1.0.1 (2025-05-21)


### ⚠ BREAKING CHANGES

* Reset of migrations (requires drop of database)

### Features

* **!:** Renamed the controller for OnAttributeCollectionSubmitController to adhere the naming convention. ([a583441](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a5834419e43ec1cb16286ddd9661daac0db98e15))
* Add support for "No Country" in submission search and IRD export ([504f393](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/504f393caea0674fcd0d7dd75d14e7b564e964cf)), closes [#16901](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16901)
* Added [Area] to ExternalId controllers ([892dc06](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/892dc06c2e368deaae007843092a12d07a3a216e))
* Added entities for jurisdiction, masterclient and legalentities ([a2793f6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a2793f6ff4c8ac37d4a82067d0942097f4685731))
* Added exception handling around app.Run() (startup) ([3eb3f68](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3eb3f68522553b7e0d831b3ebe7ab9415e73d0f1))
* Added import of company ([49d605a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/49d605af65a168be40ccc5690af2c6f296fd683a))
* Added recipient override for announcement emails, refers [#17108](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17108) ([e9d31c5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e9d31c52901f435161f3b0aad65f59de989cc5cd))
* Added SecurityManager ([0ab4fff](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0ab4fff59e90e88928bbb63e960482e378392709))
* Added versioning ([feca015](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/feca015c9488cb22a6ca96135fa74922cdd93174))
* Allow filtering for IsDeleted when retrieving submissions, resolves  [#17209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17209) ([387cd2d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/387cd2db0131d5af5d0cc5e44c548246cf598820))
* **BO/Dir:** Check on missing data in Bo/Dir ([0a9f3d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0a9f3d871ca8158d5c752ef59a3aba7b7016ceaa))
* **bo/dir:** option to include metadata on getting BO/dir from company ([7fddedc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/7fddedc6b5e198df4bdf3e07017ca4ac9bd2c10c))
* **bodir:** Added endpoint for simulation of Sync ([6b6c576](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6b6c576304cc66ece408ef709c09903374088559))
* BODirector ([832c08f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/832c08ffcd9e4a7d357bfee27101146ac45195c9))
* **client:** Added endpoint for searching masterclients ([a2375d0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a2375d058cee93025c14d9baf773b78bc89c4c11))
* **client:** Added endpoints to search for companies ([93e402c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/93e402cd602fae048251332e7273d9d4805e8dfb))
* **company:** added jurisdiction and masterclient info ([f09ff14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f09ff14484060797d82609c5ecef920363b466bf))
* Configuration depending on environment from settings ([e148804](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e1488043229815a0ba7c21f2a123d29e65c00441))
* **data:** addign users (monica) as testdata ([d103adf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d103adf71493d71422990c537ed4f3939d00cd66))
* **directors:** started with directors ([c207265](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/c207265ac7bf7ef658467231130a8d5092b1cbfa))
* **email:** Check for mail protocol (sendgrid/smtp) at startup. ([1270d08](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/1270d081fe8ed07d516fd646feaf4f1b685197d7))
* **externalid:** Added authentication and generic retrieval of MasterClientCode in custom extension. ([0232f34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0232f346e34b3a669f1aca9374219769c50b60d2))
* first setup for import MCC ([d5eb11c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d5eb11c14b1c50120a46ada987868ca20c545029))
* **framework:** Update to 1.3.14 ([fa040cf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/fa040cfd957848e3c107745d037be5128a222dfa))
* Improved GetCompanyModules performance ([d7807b1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d7807b12e31a58f8f8f124ee17e09c0a59192ce3)), closes [#17054](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17054)
* **invitation:** added wildcard check for sending invitation (if not in production ([491c3b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/491c3b988c211d1beacac11eff3da916eebf72c3))
* **masterclient:** implemented sync based on assumed layout of staging table ([77d234a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/77d234a6ecd2c4c79eee9177351563d7c7080e22))
* **masterclients:** getting masterclients will split the users into owners (MasterClientUsers) and managers (MasterClientManagers) ([f734112](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f734112532c7cee63b3e597ebb2b223cae78eb4d))
* **mfa:** Added MFAEmailCodeExpiresIn in MFAInfoDTO ([71012dc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/71012dc8f5f33d451188fe7a8f2ff5a9e162ecdb))
* **mfa:** Configurable expiration for emailcode ([74794b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/74794b90b98e61779e20af6ba647d2c5ab4c8472))
* **mfa:** Configurable label for Authenticator QR url ([da621b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/da621b9c34c8390b57f76544e7edac43e53fde78))
* **migration:** added migration for BO/Director ([7915aeb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/7915aeb12c69e57d0f57296c591284415556f524))
* **migrations:** Added migrations for Forms, BODirectors and UserAttributes ([3af99fd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3af99fd8750a161e75add6c4ae4ee2b80d9e6fd3))
* Reset of migrations (requires drop of database) ([820e3a4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/820e3a4bf2d5042901267abb504fa5fd16e6a600))
* **settings:** Added endpoints for combined settings (SettingsDTO) ([db77486](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/db77486ad8c08c7cebd29c0435bcd182f5c0faf8))
* **settings:** added financial settings for jurisdiction. ([d9066db](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d9066db22e2842c848231d1565314e73fd20db83))
* **settings:** added service to setup settings at startup (no migration) ([26651ee](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/26651ee9806a675f94b5fea156fd20fb2df42fd4))
* **settings:** separted Nevis seeding, added payment provider config ([ae2937a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ae2937af3afd403db4152fa3d2f5d9aedef15d3c))
* **startup:** AddApplicationInsightsTelemetry only when not 'development' ([ed21894](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ed21894699728e2e5c4be577edaf1d06a7099fa2))
* **submission:** added endpioint to only update the dataset ([8648b00](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/8648b00a5264f81eba11b4508328f475849edc49))
* **submission:** only update DataSet on update of submission ([cd1d4a7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/cd1d4a7eef3304e0e3b54e0b366634d4688b6214))
* **swagger:** Added authentication logic for swagger. ([d5ddf2b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d5ddf2b5589a140f94ae3744315c68f042f46cea))
* **swagger:** Added SwaggerFilter (x-user-id) ([9c65888](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9c658881f3e19b734ebf470f266f6d038e3e8760))
* **testdata:** add testdata on startup (no swagger) ([42a4b8b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/42a4b8b499438c5a9d9d5a08bca220427f0c5e7c))
* **users:** Added UserAttributes repository ([513587d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/513587d50d949830335c80210b2f538a76329467))
* Working on FormBuilder ([170b221](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/170b221a820d6f0ead2da2339e05e83311b42f0a))


### Bug Fixes

* 15940 A deleted ES draft is not visible on the management portal ([78f19f3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/78f19f3dac8478ac6be30fb247e348609784ac78)), closes [#15940](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15940)
* add nuget config file with reference to private NetPro nuget repo ([9433ebf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9433ebfb3bbc064df8223d644131379da7ca71cc))
* add user secret ID to project configuration ([3c20589](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3c20589e73f226f78cd1952793354a918030bc5b))
* Allow empty comments for reopen-request, refers [#17139](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17139) ([256e94a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/256e94ac49e3bbf6d3a2a2e720ede39ffd3448e0))
* Allow update of UserIdentityId ([f4a0425](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f4a0425e5992e324a43a3f37d7fb3f62a6803076))
* build failed ([4a5bb22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4a5bb22a54c610655722faf9afc02a6f3424071e))
* **CICD:** Fixed service connection for production environment ([5704ff0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/5704ff074d42c135f54a35787942b3e9f18d7fe9))
* Configuration in IUsersDataManager ([745a3b5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/745a3b52ade3de592e08d2b2e48edd6b83484263))
* copy id from path to id in passed DTO ([5b9b80f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/5b9b80f2cd197374b220b9ac69fd5be5a5a43ade))
* Corrected naam of Swagger page in ConfigureSwaggerUI ([35dab1c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/35dab1cbeacb922588582552327eaf2d83165a14))
* **DataMigration:** Additional SaveChanges caused errors and was not necessary ([d606ea2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d606ea2db66476f73437985c40f24d3ccecb2d0e))
* **DataMigration:** UnprocessedRecords were being saved with hardcoded value for the entity type. ([6d10d5d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6d10d5d5f6816e1992dd393f28d2af9e475a4779))
* **Docker:** use non-generic database server name ([a47dc5c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a47dc5cf2aca161cc715071479244a7eb1326c8d))
* **externalid:** Email from issuerAssignedId ([57b8df0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/57b8df08773c7b79a8b4ffae802d8b8939dd7ccd))
* Fix build for merge ([bb894d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/bb894d8a1d4d5c79cb0c3a7e4f3980702134d9f8))
* Fixed endpoint method for SetUserSignedIn ([962a759](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/962a7593c3e10fef9a855d0c1a75c7e06d42dadb))
* Fixed namespace of UserController (Security) ([6d5aed6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6d5aed64ae30480e2163d8ed70f1738242ae1f2e))
* fixed tests after changes in settings ([4801edc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4801edc021b27c6ae129f793eb2fb25ccf559ec9))
* **forms:** added seeding of form templates for Nevis ([c8cb060](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/c8cb0600811ca520614063f0d2a8da8539f20fa4))
* **mfa:** Empty method only when not enabled yet AND authenticator selected ([15303c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/15303c7f48368a36bf4b908b485048ab951d6393))
* **mfa:** GetMFAMethodAsync ([0f211cc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0f211cc3dcfe012c08badc7a71d7a028dd4a0453))
* **mfa:** if not enabled, return always empy method. ([b66a391](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b66a39196bb0f325c41e3d0dd01cac6ca208e62b))
* **mfa:** Return empty string for 'method' if authenticator method selected but not enabled yet ([0193232](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/01932321d74c03de1463d3e7239d192459c0db5b))
* Migrations ([9b01461](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9b0146120848aca1b3553d9f5d0056edb8db141c))
* **migrations:** Commented out stored procedures (StoredProcs20241014) ([18f9c51](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/18f9c5138ac5398fd46e2017481c3967c9337c45))
* **migrations:** Dropped migration data and created new ([01c77d7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/01c77d72d76719d9785a4e8da0311e9ee6351c69))
* Only add activity log for annual fees that are actually updated. Fixes [#17232](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17232) ([d00353f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d00353f0fa64d080a13e885c63ee794dc814b5ec))
* Panama seeded production office was wrong, fixes [#17185](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17185) ([d33771e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d33771eca33607568683e3e4c3eae2175609626f))
* **pipelines:** Project path/name for SQL Migration ([dcc35eb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/dcc35eba7ae4c676c274c3dd35bf88272d31bcd8))
* **pipelines:** project pattern in 'publish' ([40388e8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/40388e86a7608344d48b1805c2d60f84c719b3d6))
* Removed SQL Server Data Tools (SSDT) settings from Domain.Repository project ([2268631](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/2268631088ef9bc5f969a989f36f60c948a5ed49))
* Request for information log message ([728af22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/728af224713e4112dc156a8fc8b27d6191819e34)), closes [#16184](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16184)
* RequestAssistance retuning 204 instead of 200. ([f1d23b1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f1d23b101c52ab4a0d0f61d27c8d1fca45b74ead))
* **seed:** fixed duplicate documentqueue exception in CreateDummyReports ([447c783](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/447c783832c517b5a279fb1150e963d24160b893))
* **Seeding:** Module BFR was not created ([f0d8298](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f0d82988cff1b701ac9643673af5a9ccbdeb24e6))
* settings for externalid 'testing' ([a08f0c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a08f0c1a1afd9892705f244ff5397ca5f14db09d))
* **settings:** fixed error when settings does not exist and value = null ([4f1fab0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4f1fab090901399a0e649c115e4133951ac7638f))
* setup of data ([1d45652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/1d456520207b89b68fa84e848f6ff4300a2e7dad))
* **sql:** StoredProcedure issues when in migration sql ([9cee4c3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9cee4c35ac68b5e2fc7f7f2a9bf1bc2e3a218584))
* **sql:** StoredProcedure issues when in migration sql ([e6b303f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e6b303f822dd5b8c6e2d3ee955b34fb6b3c3e0de))
* StagingTableExistsAsync ([bcdea2f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/bcdea2f861c277139586ab73f3199bf280c0bc7d))
* Startup logging ([05ad655](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/05ad655392c80ffeecbf00ba80d5264d6f0cb846))
* status 204 in management controllers if no data returned ([78591a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/78591a23bb79eeb2f36f447d877428286b1c90f3))
* **submissions:** first year was not included in GetAvailableSubmissionYears ([61848fa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/61848facd2788b4b9de89cf2c26dbbdd50dffa36))
* **swagger:** Updated name ([b6f2039](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b6f20398960c458084aa28bc63249d1a7c90f648))
* **sync:** masterclient ids were not loaded correct ([6e007d4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6e007d4c86c66b55412daa5d680faf50479b461f))
* **sync:** masterclient was not set on the company ([e66f49a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e66f49afe765ac01249da3813486cbb9d2995954))
* **sync:** Set IsActive for a new user ([623cab5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/623cab5ad661317bcd2c9cbc611ca67cc2623e79))
* **sync:** skip user from create if email does not contain @ ([0a2658d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0a2658dddbb6cac5c2e879b919c88f332052476c))
* **sync:** use truncate/insert into instead of drop/select into ([ea6920d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ea6920d4616fc2bc63f848ffead54c6a805d5aa6))
* **sync:** UseTempDB in MasterClient sync ([e12fa42](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e12fa42903aee97a9f5c605c4a8a66a4929a2c38))
* tests ([ecb2e21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ecb2e21fd6a94b34c52631038a4785dd03de67ea))
* tests failed bacause of lockmanager and in-memory database ([0df3f85](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0df3f853d754a8e5aa3f8ff2c958f17c5f1ac5c4))
* Tests for settings manager ([4bcc9c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4bcc9c72cf31187154d8ab21e931910f4161e456))
* Version did not include prerelease tag, refers [#16900](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16900) ([f3bd166](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f3bd16668719ea6ea9714c6e7ac10853ff33965f))
* VP Sync was not creating users correctly, fixes [#17391](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17391) ([0ee8051](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0ee8051895764ea6cb398a8bab07e7068cfe2a22))

## 1.0.0 (2025-05-21)


### ⚠ BREAKING CHANGES

* Reset of migrations (requires drop of database)

### Features

* **!:** Renamed the controller for OnAttributeCollectionSubmitController to adhere the naming convention. ([a583441](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a5834419e43ec1cb16286ddd9661daac0db98e15))
* Add support for "No Country" in submission search and IRD export ([504f393](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/504f393caea0674fcd0d7dd75d14e7b564e964cf)), closes [#16901](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16901)
* Added [Area] to ExternalId controllers ([892dc06](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/892dc06c2e368deaae007843092a12d07a3a216e))
* Added entities for jurisdiction, masterclient and legalentities ([a2793f6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a2793f6ff4c8ac37d4a82067d0942097f4685731))
* Added exception handling around app.Run() (startup) ([3eb3f68](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3eb3f68522553b7e0d831b3ebe7ab9415e73d0f1))
* Added import of company ([49d605a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/49d605af65a168be40ccc5690af2c6f296fd683a))
* Added recipient override for announcement emails, refers [#17108](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17108) ([e9d31c5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e9d31c52901f435161f3b0aad65f59de989cc5cd))
* Added SecurityManager ([0ab4fff](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0ab4fff59e90e88928bbb63e960482e378392709))
* Added versioning ([feca015](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/feca015c9488cb22a6ca96135fa74922cdd93174))
* Allow filtering for IsDeleted when retrieving submissions, resolves  [#17209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17209) ([387cd2d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/387cd2db0131d5af5d0cc5e44c548246cf598820))
* **BO/Dir:** Check on missing data in Bo/Dir ([0a9f3d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0a9f3d871ca8158d5c752ef59a3aba7b7016ceaa))
* **bo/dir:** option to include metadata on getting BO/dir from company ([7fddedc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/7fddedc6b5e198df4bdf3e07017ca4ac9bd2c10c))
* **bodir:** Added endpoint for simulation of Sync ([6b6c576](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6b6c576304cc66ece408ef709c09903374088559))
* BODirector ([832c08f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/832c08ffcd9e4a7d357bfee27101146ac45195c9))
* **client:** Added endpoint for searching masterclients ([a2375d0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a2375d058cee93025c14d9baf773b78bc89c4c11))
* **client:** Added endpoints to search for companies ([93e402c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/93e402cd602fae048251332e7273d9d4805e8dfb))
* **company:** added jurisdiction and masterclient info ([f09ff14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f09ff14484060797d82609c5ecef920363b466bf))
* Configuration depending on environment from settings ([e148804](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e1488043229815a0ba7c21f2a123d29e65c00441))
* **data:** addign users (monica) as testdata ([d103adf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d103adf71493d71422990c537ed4f3939d00cd66))
* **directors:** started with directors ([c207265](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/c207265ac7bf7ef658467231130a8d5092b1cbfa))
* **email:** Check for mail protocol (sendgrid/smtp) at startup. ([1270d08](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/1270d081fe8ed07d516fd646feaf4f1b685197d7))
* **externalid:** Added authentication and generic retrieval of MasterClientCode in custom extension. ([0232f34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0232f346e34b3a669f1aca9374219769c50b60d2))
* first setup for import MCC ([d5eb11c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d5eb11c14b1c50120a46ada987868ca20c545029))
* **framework:** Update to 1.3.14 ([fa040cf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/fa040cfd957848e3c107745d037be5128a222dfa))
* Improved GetCompanyModules performance ([d7807b1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d7807b12e31a58f8f8f124ee17e09c0a59192ce3)), closes [#17054](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17054)
* **invitation:** added wildcard check for sending invitation (if not in production ([491c3b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/491c3b988c211d1beacac11eff3da916eebf72c3))
* **masterclient:** implemented sync based on assumed layout of staging table ([77d234a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/77d234a6ecd2c4c79eee9177351563d7c7080e22))
* **masterclients:** getting masterclients will split the users into owners (MasterClientUsers) and managers (MasterClientManagers) ([f734112](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f734112532c7cee63b3e597ebb2b223cae78eb4d))
* **mfa:** Added MFAEmailCodeExpiresIn in MFAInfoDTO ([71012dc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/71012dc8f5f33d451188fe7a8f2ff5a9e162ecdb))
* **mfa:** Configurable expiration for emailcode ([74794b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/74794b90b98e61779e20af6ba647d2c5ab4c8472))
* **mfa:** Configurable label for Authenticator QR url ([da621b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/da621b9c34c8390b57f76544e7edac43e53fde78))
* **migration:** added migration for BO/Director ([7915aeb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/7915aeb12c69e57d0f57296c591284415556f524))
* **migrations:** Added migrations for Forms, BODirectors and UserAttributes ([3af99fd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3af99fd8750a161e75add6c4ae4ee2b80d9e6fd3))
* Reset of migrations (requires drop of database) ([820e3a4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/820e3a4bf2d5042901267abb504fa5fd16e6a600))
* **settings:** Added endpoints for combined settings (SettingsDTO) ([db77486](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/db77486ad8c08c7cebd29c0435bcd182f5c0faf8))
* **settings:** added financial settings for jurisdiction. ([d9066db](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d9066db22e2842c848231d1565314e73fd20db83))
* **settings:** added service to setup settings at startup (no migration) ([26651ee](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/26651ee9806a675f94b5fea156fd20fb2df42fd4))
* **settings:** separted Nevis seeding, added payment provider config ([ae2937a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ae2937af3afd403db4152fa3d2f5d9aedef15d3c))
* **startup:** AddApplicationInsightsTelemetry only when not 'development' ([ed21894](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ed21894699728e2e5c4be577edaf1d06a7099fa2))
* **submission:** added endpioint to only update the dataset ([8648b00](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/8648b00a5264f81eba11b4508328f475849edc49))
* **submission:** only update DataSet on update of submission ([cd1d4a7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/cd1d4a7eef3304e0e3b54e0b366634d4688b6214))
* **swagger:** Added authentication logic for swagger. ([d5ddf2b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d5ddf2b5589a140f94ae3744315c68f042f46cea))
* **swagger:** Added SwaggerFilter (x-user-id) ([9c65888](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9c658881f3e19b734ebf470f266f6d038e3e8760))
* **testdata:** add testdata on startup (no swagger) ([42a4b8b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/42a4b8b499438c5a9d9d5a08bca220427f0c5e7c))
* **users:** Added UserAttributes repository ([513587d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/513587d50d949830335c80210b2f538a76329467))
* Working on FormBuilder ([170b221](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/170b221a820d6f0ead2da2339e05e83311b42f0a))


### Bug Fixes

* 15940 A deleted ES draft is not visible on the management portal ([78f19f3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/78f19f3dac8478ac6be30fb247e348609784ac78)), closes [#15940](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15940)
* add nuget config file with reference to private NetPro nuget repo ([9433ebf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9433ebfb3bbc064df8223d644131379da7ca71cc))
* add user secret ID to project configuration ([3c20589](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/3c20589e73f226f78cd1952793354a918030bc5b))
* Allow empty comments for reopen-request, refers [#17139](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17139) ([256e94a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/256e94ac49e3bbf6d3a2a2e720ede39ffd3448e0))
* Allow update of UserIdentityId ([f4a0425](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f4a0425e5992e324a43a3f37d7fb3f62a6803076))
* build failed ([4a5bb22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4a5bb22a54c610655722faf9afc02a6f3424071e))
* **CICD:** Fixed service connection for production environment ([5704ff0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/5704ff074d42c135f54a35787942b3e9f18d7fe9))
* Configuration in IUsersDataManager ([745a3b5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/745a3b52ade3de592e08d2b2e48edd6b83484263))
* copy id from path to id in passed DTO ([5b9b80f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/5b9b80f2cd197374b220b9ac69fd5be5a5a43ade))
* Corrected naam of Swagger page in ConfigureSwaggerUI ([35dab1c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/35dab1cbeacb922588582552327eaf2d83165a14))
* **DataMigration:** Additional SaveChanges caused errors and was not necessary ([d606ea2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d606ea2db66476f73437985c40f24d3ccecb2d0e))
* **DataMigration:** UnprocessedRecords were being saved with hardcoded value for the entity type. ([6d10d5d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6d10d5d5f6816e1992dd393f28d2af9e475a4779))
* **Docker:** use non-generic database server name ([a47dc5c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a47dc5cf2aca161cc715071479244a7eb1326c8d))
* **externalid:** Email from issuerAssignedId ([57b8df0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/57b8df08773c7b79a8b4ffae802d8b8939dd7ccd))
* Fix build for merge ([bb894d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/bb894d8a1d4d5c79cb0c3a7e4f3980702134d9f8))
* Fixed endpoint method for SetUserSignedIn ([962a759](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/962a7593c3e10fef9a855d0c1a75c7e06d42dadb))
* Fixed namespace of UserController (Security) ([6d5aed6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6d5aed64ae30480e2163d8ed70f1738242ae1f2e))
* fixed tests after changes in settings ([4801edc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4801edc021b27c6ae129f793eb2fb25ccf559ec9))
* **forms:** added seeding of form templates for Nevis ([c8cb060](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/c8cb0600811ca520614063f0d2a8da8539f20fa4))
* **mfa:** Empty method only when not enabled yet AND authenticator selected ([15303c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/15303c7f48368a36bf4b908b485048ab951d6393))
* **mfa:** GetMFAMethodAsync ([0f211cc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0f211cc3dcfe012c08badc7a71d7a028dd4a0453))
* **mfa:** if not enabled, return always empy method. ([b66a391](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b66a39196bb0f325c41e3d0dd01cac6ca208e62b))
* **mfa:** Return empty string for 'method' if authenticator method selected but not enabled yet ([0193232](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/01932321d74c03de1463d3e7239d192459c0db5b))
* Migrations ([9b01461](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9b0146120848aca1b3553d9f5d0056edb8db141c))
* **migrations:** Commented out stored procedures (StoredProcs20241014) ([18f9c51](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/18f9c5138ac5398fd46e2017481c3967c9337c45))
* **migrations:** Dropped migration data and created new ([01c77d7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/01c77d72d76719d9785a4e8da0311e9ee6351c69))
* Only add activity log for annual fees that are actually updated. Fixes [#17232](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17232) ([d00353f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d00353f0fa64d080a13e885c63ee794dc814b5ec))
* Panama seeded production office was wrong, fixes [#17185](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17185) ([d33771e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/d33771eca33607568683e3e4c3eae2175609626f))
* **pipelines:** Project path/name for SQL Migration ([dcc35eb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/dcc35eba7ae4c676c274c3dd35bf88272d31bcd8))
* **pipelines:** project pattern in 'publish' ([40388e8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/40388e86a7608344d48b1805c2d60f84c719b3d6))
* Removed SQL Server Data Tools (SSDT) settings from Domain.Repository project ([2268631](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/2268631088ef9bc5f969a989f36f60c948a5ed49))
* Request for information log message ([728af22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/728af224713e4112dc156a8fc8b27d6191819e34)), closes [#16184](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16184)
* RequestAssistance retuning 204 instead of 200. ([f1d23b1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f1d23b101c52ab4a0d0f61d27c8d1fca45b74ead))
* **seed:** fixed duplicate documentqueue exception in CreateDummyReports ([447c783](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/447c783832c517b5a279fb1150e963d24160b893))
* **Seeding:** Module BFR was not created ([f0d8298](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f0d82988cff1b701ac9643673af5a9ccbdeb24e6))
* settings for externalid 'testing' ([a08f0c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/a08f0c1a1afd9892705f244ff5397ca5f14db09d))
* **settings:** fixed error when settings does not exist and value = null ([4f1fab0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4f1fab090901399a0e649c115e4133951ac7638f))
* setup of data ([1d45652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/1d456520207b89b68fa84e848f6ff4300a2e7dad))
* **sql:** StoredProcedure issues when in migration sql ([9cee4c3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/9cee4c35ac68b5e2fc7f7f2a9bf1bc2e3a218584))
* **sql:** StoredProcedure issues when in migration sql ([e6b303f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e6b303f822dd5b8c6e2d3ee955b34fb6b3c3e0de))
* StagingTableExistsAsync ([bcdea2f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/bcdea2f861c277139586ab73f3199bf280c0bc7d))
* Startup logging ([05ad655](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/05ad655392c80ffeecbf00ba80d5264d6f0cb846))
* status 204 in management controllers if no data returned ([78591a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/78591a23bb79eeb2f36f447d877428286b1c90f3))
* **submissions:** first year was not included in GetAvailableSubmissionYears ([61848fa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/61848facd2788b4b9de89cf2c26dbbdd50dffa36))
* **swagger:** Updated name ([b6f2039](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/b6f20398960c458084aa28bc63249d1a7c90f648))
* **sync:** masterclient ids were not loaded correct ([6e007d4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/6e007d4c86c66b55412daa5d680faf50479b461f))
* **sync:** masterclient was not set on the company ([e66f49a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e66f49afe765ac01249da3813486cbb9d2995954))
* **sync:** Set IsActive for a new user ([623cab5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/623cab5ad661317bcd2c9cbc611ca67cc2623e79))
* **sync:** skip user from create if email does not contain @ ([0a2658d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0a2658dddbb6cac5c2e879b919c88f332052476c))
* **sync:** use truncate/insert into instead of drop/select into ([ea6920d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ea6920d4616fc2bc63f848ffead54c6a805d5aa6))
* **sync:** UseTempDB in MasterClient sync ([e12fa42](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/e12fa42903aee97a9f5c605c4a8a66a4929a2c38))
* tests ([ecb2e21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/ecb2e21fd6a94b34c52631038a4785dd03de67ea))
* tests failed bacause of lockmanager and in-memory database ([0df3f85](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0df3f853d754a8e5aa3f8ff2c958f17c5f1ac5c4))
* Tests for settings manager ([4bcc9c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/4bcc9c72cf31187154d8ab21e931910f4161e456))
* Version did not include prerelease tag, refers [#16900](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16900) ([f3bd166](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/f3bd16668719ea6ea9714c6e7ac10853ff33965f))
* VP Sync was not creating users correctly, fixes [#17391](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17391) ([0ee8051](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/0ee8051895764ea6cb398a8bab07e7068cfe2a22))
