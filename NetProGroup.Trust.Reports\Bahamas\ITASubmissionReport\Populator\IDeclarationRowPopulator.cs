// <copyright file="IDeclarationRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Interface for the IRD submission report declaration row populator.
    /// </summary>
    public interface IDeclarationRowPopulator : ITemplateRowPopulator<Submission>, ITransientService
    {
    }
}