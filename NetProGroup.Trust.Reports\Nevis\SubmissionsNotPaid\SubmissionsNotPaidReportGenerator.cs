// <copyright file="SubmissionsNotPaidReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Reports.Nevis.SubmissionsNotPaid
{
    /// <summary>
    /// Generates a submissions not paid report.
    /// </summary>
    public class SubmissionsNotPaidReportGenerator : ISubmissionsNotPaidReportGenerator
    {
        private const string TemplateName = "submissions-not-paid";
        private const string ReportName = "submissions-not-paid";

        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;
        private readonly IModulesRepository _modulesRepository;
        private readonly ISubmissionReportsDataManager _submissionReportsDataManager;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsNotPaidReportGenerator"/> class.
        /// </summary>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="legalEntitiesDataManager">The legal entities data manager.</param>
        /// <param name="modulesRepository">The modules repository.</param>
        /// <param name="templateProvider">The template provider.</param>
        /// <param name="submissionReportsDataManager">The submission reports datamanager.</param>
        public SubmissionsNotPaidReportGenerator(
            IDateTimeProvider dateTimeProvider,
            ILegalEntitiesDataManager legalEntitiesDataManager,
            IModulesRepository modulesRepository,
            IReportTemplateProvider templateProvider,
            ISubmissionReportsDataManager submissionReportsDataManager)
        {
            _dateTimeProvider = dateTimeProvider;
            _legalEntitiesDataManager = legalEntitiesDataManager;
            _modulesRepository = modulesRepository;
            _templateProvider = templateProvider;
            _submissionReportsDataManager = submissionReportsDataManager;
        }

        /// <inheritdoc />
        public async Task<SubmissionsNotPaidReportOutput> GenerateSubmissionsNotPaidReportAsync()
        {
            var submissions = (await GetUnpaidSubmissions()).ToList();
            var legalEntities = submissions.Select(submission => submission.LegalEntity).Distinct().ToList();

            var allSubmissionYears = await GetSubmissionYears();

            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(TemplateName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            var submissionsByYear = submissions
                .GroupBy(s => s.FinancialYear)
                .ToDictionary(g => g.Key, g => g.ToList());

            CreateSubmissionExcelReport(workbook, submissionsByYear, allSubmissionYears);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new SubmissionsNotPaidReportOutput(stream.ToArray(), legalEntities);
        }

        /// <inheritdoc />
        public string GenerateReportNameForSubmissionsNotPaidAsync()
        {
            return $"{ReportName}-{_dateTimeProvider.Now:yyyy-MM-dd}";
        }

        /// <summary>
        /// Creates an Excel report for the given submissions.
        /// </summary>
        /// <param name="workbook">The workbook to add the submissions to.</param>
        /// <param name="legalEntities">The legal entities to add to the workbook.</param>
        /// <param name="allSubmissionYears">The years to generate the report for.</param>
        private static void CreateSubmissionExcelReport(XLWorkbook workbook, Dictionary<int?, List<Submission>> legalEntities, IReadOnlyCollection<int> allSubmissionYears)
        {
            for (var i = 0; i < allSubmissionYears.Count; i++)
            {
                var year = allSubmissionYears.ElementAt(i);
                var worksheet = workbook.Worksheet(i + 1);

                // If there is a next year, copy the current worksheet so that the blank template is available for the next year
                if (i + 1 < allSubmissionYears.Count)
                {
                    var nextYear = allSubmissionYears.ElementAt(i + 1);
                    worksheet.CopyTo(workbook, $"{nextYear}");
                }

                if (legalEntities.TryGetValue(year, out var submissions))
                {
                    GeneratePerYearReport(worksheet, submissions);
                }
            }
        }

        private static void GeneratePerYearReport(IXLWorksheet worksheet, List<Submission> legalEntities)
        {
            int row = 2; // Starting row (assuming the first row is for headers)

            foreach (var submission in legalEntities)
            {
                // Column 1: Company Name
                worksheet.Cell(row, 1).Value = submission.LegalEntity.Name;

                // Column 2: MCC
                worksheet.Cell(row, 2).Value = submission.LegalEntity.MasterClient.Code;

                // Column 3: Submission status
                worksheet.Cell(row, 3).Value = submission.Status.ToString();

                // Column 4: Deleted
                worksheet.Cell(row, 4).Value = submission.LegalEntity.InactiveSetAt?.ToString("dd-MM-yyyy");

                // Column 5: Email submitter
                worksheet.Cell(row, 5).Value = submission.Attributes.GetAttributeValue<string>(SubmissionAttributeKeys.SubmittedByEmail);

                // Column 6-13: E-Mail MCC (0-7) Index 0-7
                var masterClientUsers = submission.LegalEntity.MasterClient.MasterClientUsers.Take(8).ToList();

                for (int i = 0; i < masterClientUsers.Count; i++)
                {
                    // Column 6+Index: E-Mail MCC (Index)
                    worksheet.Cell(row, 6 + i).Value = masterClientUsers[i].User.Email;
                }

                // Increment the row for the next submission
                row++;
            }
        }

        private async Task<IReadOnlyCollection<int>> GetSubmissionYears()
        {
            var strModule = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.SimplifiedTaxReturn);
            var allSubmissionYears = (await _submissionReportsDataManager.GetAllSubmissionYears(new AllSubmissionYearsRequest { ModuleId = strModule.Id })).Years;
            return allSubmissionYears;
        }

        private async Task<IEnumerable<Submission>> GetUnpaidSubmissions()
        {
            // Get the submissions for today an pending export
            return await _legalEntitiesDataManager.GetNevisLegalEntitiesForSubmissionNotPaidReportAsync();
        }
    }
}