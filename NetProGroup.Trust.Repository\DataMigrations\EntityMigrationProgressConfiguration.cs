// <copyright file="EntityMigrationProgressConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.DataMigrations;
using NetProGroup.Trust.Domain.Repository;

namespace NetProGroup.Trust.Repository.DataMigrations
{
    /// <summary>
    /// Configuration class for the EntityMigrationProgress entity.
    /// </summary>
    public class EntityMigrationProgressConfiguration : IEntityTypeConfiguration<EntityMigrationProgress>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the entity mapping for EntityMigrationProgress.
        /// </summary>
        /// <param name="builder">The entity type builder.</param>
        public void Configure(EntityTypeBuilder<EntityMigrationProgress> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "EntityMigrationProgresses", TrustDbContext.DbSchema);
            builder.HasKey(dm => dm.Id);
            builder.Property(dm => dm.Id).ValueGeneratedOnAdd();

            builder.HasKey(e => e.Id);
            builder.Property(e => e.EntityName).IsRequired().HasMaxLength(100);
            builder.Property(e => e.SourceCount).IsRequired();
            builder.Property(e => e.ProcessedCount).IsRequired();
            builder.Property(e => e.SuccessCount).IsRequired();
            builder.Property(e => e.LastUpdated).IsRequired();

            builder.HasOne(e => e.DataMigration)
                .WithMany(dm => dm.EntityMigrationProgresses)
                .HasForeignKey(e => e.DataMigrationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_DataMigration_EntityMigrationProgress");
        }
    }
}
