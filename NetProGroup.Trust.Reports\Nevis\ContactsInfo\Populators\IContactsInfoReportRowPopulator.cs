// <copyright file="IContactsInfoReportRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Nevis.ContactsInfo.Populators
{
    /// <summary>
    /// Interface for populating contact information report rows.
    /// </summary>
    public interface IContactsInfoReportRowPopulator : ITemplateRowPopulator<ContactsInfoData>, ITransientService;
}
