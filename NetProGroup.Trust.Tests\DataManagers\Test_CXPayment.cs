using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Payments.PaymentProvider;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Services;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    [TestFixture]
    public class Test_CXPayment : TestBase
    {
        private ICxPaymentService _paymentService;

        [SetUp]
        public void Setup()
        {
            _paymentService = _server.Services.GetRequiredService<ICxPaymentService>();

            //payments
            var payment = new Domain.Payments.Payment(new Guid())
            {
                LegalEntityId = new Guid("b4f2b7a4-7c0b-47e3-9a6a-935f5eae9f42"),
                CurrencyId = new Guid("3f8d5cba-3df4-49a7-a1ab-42c4e55f9b5c"),
                Amount = 1500.00M,
                Status = PaymentStatus.Pending,
                CreatedAt = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                ConcurrencyStamp = Guid.NewGuid(),
                LegalEntity = new LegalEntity
                {
                    Name = "Company LLC",
                    Code = "E-LL",
                    EntityType = LegalEntityType.Company,
                    IncorporationDate = DateTime.UtcNow.AddYears(-2),
                    JurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9")
                },
                PaymentInvoices = new List<PaymentInvoice>
                {
                    new()
                    {
                        Invoice = new Invoice(Guid.NewGuid())
                        {
                            InvoiceNr = "*********",
                            Date = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            FinancialYear = 2023,
                            Layout = LayoutConsts.TridentTrust
                        }
                    }
                }
            };

            // Payment Provider
            var paymentProviderService = _server.Services.GetRequiredService<IPaymentProviderRepository>();

            var paymentProvider = new PaymentProvider(Guid.NewGuid())
            {
                Name = "CXPAYKEY",
                Key = "CXPAYKEY",
                BaseUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                ApiKey = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                ApiSecret = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                ConcurrencyStamp = Guid.NewGuid()
            };

            paymentProviderService.Insert(paymentProvider);
            paymentProviderService.SaveChanges();

            // Payment Transaction
            var paymentTransactionService = _server.Services.GetRequiredService<IPaymentTransactionRepository>();

            var paymentTransaction = new PaymentTransaction(new Guid("78fcb820-4ba6-4383-a07e-a798658ad020"))
            {
                Payment = payment,
                PaymentProvider = paymentProvider,
                Result = "Pending",
                ResultCode = "100", // Assuming 100 represents 'In Progress'
                ResultMessage = "Transaction is currently in progress",
                TransactionId = "TXN*********0", // Unique transaction ID
                Status = "In Progress", // Transaction status
                CardDigits = "1234", // Last 4 digits of the card used
                ProcessCreatedAt = DateTime.UtcNow, // Date and time when the transaction was created
                PaidAt = null, // Set when payment is completed
                IsFinished = false,
                FirstName = "John", // First name of the customer
                LastName = "Doe", // Last name of the customer
                Address = "123 Main St", // Customer address
                City = "New York", // Customer city
                State = "NY", // Customer state
                ZipCode = "10001", // Customer ZIP code
                Company = "Customer's Company", // Company name if applicable
                PhoneNumber = "555-1234", // Customer phone number
                PaymentProviderId = paymentProvider.Id,
                Email = "<EMAIL>", // Customer email
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                ConcurrencyStamp = Guid.NewGuid()
            };

            // Insert the new PaymentTransaction into the repository
            paymentTransactionService.Insert(paymentTransaction);
            paymentTransactionService.SaveChanges();
        }

        [Test]
        public void ProcessRequest_ShouldReturn_ValidResponse_OnValidRequest()
        {
            // Arrange

            var paymentRequest = new PaymentRequest
            {
                RedirectUrl = "https://valid-url.com",
                Amount = 100.50M,
                OrderId = "78fcb820-4ba6-4383-a07e-a798658ad020",
                Email = "<EMAIL>",
                Company = "Test Company",
                PaymentGatewaytUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                ApiKey = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
            };

            // Act
            var response = _paymentService.ProcessRequest(paymentRequest);

            // Assert
            response.Should().NotBeNull();
            response.Transaction.Should().NotBeNull();
            response.ResultMessage.Should().Be("Step 1 completed"); // Assuming success based on response text
        }

        [Test]
        public void ProcessRequest_ReturnsErrorResponse_OnInvalidAmount()
        {
            // Arrange
            var paymentRequest = new PaymentRequest
            {
                RedirectUrl = "https://valid-url.com",
                Amount = -5.00M, // Invalid amount
                OrderId = "78fcb820-4ba6-4383-a07e-a798658ad020",
                Email = "<EMAIL>",
                Company = "Test Company",
                PaymentGatewaytUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
            };

            // Act
            var response = _paymentService.ProcessRequest(paymentRequest);

            // Assert
            response.Should().NotBeNull();
            response.ResultCode.Should().Be(3);
        }

        [Test]
        public void CompleteRequest_ThrowsException_OnInvalidToken()
        {
            // Arrange
            var paymentCompleteRequest = new PaymentComplete
            {
                ApiGatewayUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                IdTransaction = "78fcb820-4ba6-4383-a07e-a798658ad020",
                Token = "invalid-token" // Invalid token
            };

            // Act
            var response = _paymentService.CompleteRequest(paymentCompleteRequest);

            // Assert
            response.Should().NotBeNull();
            response.ResultCode.Should().Be(3);
        }

        [Test]
        public void CompleteRequest_ThrowsApplicationException_OnNonExistentTransaction()
        {
            // Arrange
            var paymentCompleteRequest = new PaymentComplete
            {
                IdTransaction = Guid.NewGuid().ToString(), // Non-existent transaction
                Token = "valid-token"
            };

            // Act & Assert
            var action = () => _paymentService.CompleteRequest(paymentCompleteRequest);

            action.Should().Throw<ApplicationException>()
                .WithMessage("No transaction found with transaction id: " + paymentCompleteRequest.IdTransaction);
        }


        [Test]
        public void ProcessRequest_ThrowsException_OnNullPaymentRequest()
        {
            // Arrange, Act & Assert
            var action = () => _paymentService.ProcessRequest(null!);
            action.Should().Throw<ArgumentNullException>();
        }

        [Test]
        public void CompleteRequest_ThrowsException_OnNullCompleteRequest()
        {
            // Arrange, Act & Assert
            var action = () => _paymentService.CompleteRequest(null!);
            action.Should().Throw<ArgumentNullException>();
        }
    }
}