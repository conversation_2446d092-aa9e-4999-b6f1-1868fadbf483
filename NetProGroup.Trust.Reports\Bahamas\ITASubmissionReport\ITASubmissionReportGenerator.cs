// <copyright file="ITASubmissionReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.Bahamas.IRDSubmissionReport;
using NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport
{
    /// <summary>
    /// Generates the IRD submission report.
    /// </summary>
    public class ITASubmissionReportGenerator : IITASubmissionReportGenerator
    {
        private const string ReportName = "_EconomicSubstanceBahamas_Bahamas";
        private readonly IDeclarationRowPopulator _declarationRowPopulator;
        private readonly IDirectionRowPopulator _directionRowPopulator;
        private readonly IQuorumOfDirectorsRowPopulator _quorumOfDirectorsRowPopulator;
        private readonly IEmployeesRowPopulator _employeesRowPopulator;
        private readonly IPremisesRowPopulator _premisesRowPopulator;
        private readonly ICigaRowPopulator _cigaRowPopulator;
        private readonly IOutsourcingRowPopulator _outsourcingRowPopulator;
        private readonly IParentEntitiesRowPopulator _parentEntitiesRowPopulator;
        private readonly IExcelTemplateService<Submission> _excelTemplateService;
        private readonly IReportTemplateProvider _templateProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="ITASubmissionReportGenerator"/> class.
        /// </summary>
        /// <param name="excelTemplateService">The excel template service.</param>
        /// <param name="templateProvider">The template provider.</param>
        /// <param name="declarationRowPopulator">An instance of IDeclarationRowPopulator.</param>
        /// <param name="directionRowPopulator">An instance of IDirectionRowPopulator.</param>
        /// <param name="quorumOfDirectorsRowPopulator">An instance of IQuorumOfDirectorsRowPopulator.</param>
        /// <param name="employeesRowPopulator">An instance of IEmployeesRowPopulator.</param>
        /// <param name="premisesRowPopulator">An instance of IPremisesRowPopulator.</param>
        /// <param name="cigaRowPopulator">An instance of ICigaRowPopulator.</param>
        /// <param name="outsourcingRowPopulator">An instance of IOutsourcingRowPopulator.</param>
        /// <param name="parentEntitiesRowPopulator">An instance of IParentEntitiesRowPopulator.</param>
        public ITASubmissionReportGenerator(
            IExcelTemplateService<Submission> excelTemplateService,
            IReportTemplateProvider templateProvider,
            IDeclarationRowPopulator declarationRowPopulator,
            IDirectionRowPopulator directionRowPopulator,
            IQuorumOfDirectorsRowPopulator quorumOfDirectorsRowPopulator,
            IEmployeesRowPopulator employeesRowPopulator,
            IPremisesRowPopulator premisesRowPopulator,
            ICigaRowPopulator cigaRowPopulator,
            IOutsourcingRowPopulator outsourcingRowPopulator,
            IParentEntitiesRowPopulator parentEntitiesRowPopulator
            )
        {
            _declarationRowPopulator = declarationRowPopulator;
            _directionRowPopulator = directionRowPopulator;
            _quorumOfDirectorsRowPopulator = quorumOfDirectorsRowPopulator;
            _employeesRowPopulator = employeesRowPopulator;
            _premisesRowPopulator = premisesRowPopulator;
            _cigaRowPopulator = cigaRowPopulator;
            _outsourcingRowPopulator = outsourcingRowPopulator;
            _parentEntitiesRowPopulator = parentEntitiesRowPopulator;
            _excelTemplateService = excelTemplateService;
            _templateProvider = templateProvider;
        }

        /// <inheritdoc/>
        public async Task<ReportOutput> GenerateIRDSubmissionReportAsync(List<Submission> submissions)
        {
            var excelFileTemplate = await _templateProvider.GetExcelTemplateAsync(ReportName);

            using var workbook = new XLWorkbook(excelFileTemplate);

            CreateSubmissionsReport(workbook, submissions);

            var stream = new MemoryStream();
            workbook.SaveAs(stream);

            return new ReportOutput(stream.ToArray());
        }

        /// <summary>
        /// Creates an Excel report for the given companies.
        /// </summary>
        /// <param name="workbook">The workbook to add the companies to.</param>
        /// <param name="submissions">The submissions to add in the report.</param>
        private void CreateSubmissionsReport(XLWorkbook workbook, List<Submission> submissions)
        {
            var templateConfig = new TemplateConfiguration
            {
                TemplateRowCount = 1, // We have a single row per company
                HeaderRowCount = 1, // We have a single header row
                StartingRow = 3
            };

            // Fill the main sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_declarationRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 0);

            // Fill the director sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_directionRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 1);

            // Fill the quorum sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_quorumOfDirectorsRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 2);

            // Fill the employees sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_employeesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 3);

            // Fill the liabilities sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_premisesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 4);

            // Fill the assets sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_cigaRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 5);

            // Fill the bank sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_outsourcingRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 6);

            // Fill the bank sheet
            _excelTemplateService.ClearRowPopulator();
            _excelTemplateService.RegisterRowPopulator(_parentEntitiesRowPopulator);

            _excelTemplateService.ApplyTemplate(workbook, submissions, templateConfig, 7);
        }
    }
}