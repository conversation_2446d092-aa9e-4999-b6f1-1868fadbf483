// <copyright file="CompaniesStrSubmissionStatusRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Nevis.CompaniesSTRSubmissionStatus.Populators
{
    /// <summary>
    /// Populate line 1 of the Excel template with the submission status.
    /// </summary>
    public class CompaniesStrSubmissionStatusRowPopulator : LinePopulatorBase, ICompaniesStrSubmissionStatusRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, LegalEntity data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var year = int.Parse(worksheet.Name);
            var masterClientUsers = data.MasterClient.MasterClientUsers.OrderBy(user => user.CreatedAt).ToList();

            // Company Name
            SetCellValueAndStyle(worksheet, currentRow, 1, data.Name);

            // MCC
            SetCellValueAndStyle(worksheet, currentRow, 2, data.MasterClient.Code);

            // Submission Available?
            SetCellValueAndStyle(worksheet, currentRow, 3,
                data.Submissions.Where(submission => submission.FinancialYear == year).Any(submission => submission.Status != SubmissionStatus.Draft) ? "YES" : "NO");

            // Deleted
            SetCellValueAndStyle(worksheet, currentRow, 4, !data.IsActive ? data.InactiveSetAt?.ToString("dd-MM-yyyy") : "");

            // E-Mail MCC {i}
            for (int i = 0; i < masterClientUsers.Count && i < 8; i++)
            {
                SetCellValueAndStyle(worksheet, currentRow, 5 + i, masterClientUsers[i].User.Email);
            }
        }
    }
}