// <copyright file="PaymentsRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.DataManager.Payments.RequestResponses
{
    /// <summary>
    /// Represents a request for retrieving payments with filtering, sorting, and pagination options.
    /// </summary>
    public class PaymentsRequest : IUserIdFilteredRequest
    {
        /// <summary>
        /// Gets or sets the search term to filter payments by.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the start date for filtering payments.
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Gets or sets the end date for filtering payments.
        /// </summary>
        public DateTime? ToDate { get; set; }

        /// <summary>
        /// Gets or sets the company identifier to filter payments by.
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the payment status to filter by.
        /// </summary>
        public PaymentStatus? Status { get; set; }

        /// <summary>
        /// Gets or sets the financial year to filter payments by.
        /// </summary>
        public int? FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the field to sort payments by. Default is "InvoiceDate".
        /// </summary>
        public string SortBy { get; set; } = "InvoiceDate";

        /// <summary>
        /// Gets or sets the sort order for payments. Default is "desc".
        /// </summary>
        public string SortOrder { get; set; } = "desc";

        /// <summary>
        /// Gets or sets the page number for pagination. Default is 1.
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Gets or sets the page size for pagination. Default is 10.
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <inheritdoc />
        public Guid UserId { get; set; }
    }
}