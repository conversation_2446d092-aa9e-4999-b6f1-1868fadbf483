// <copyright file="ContactsInfoReportRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Nevis.ContactsInfo.Populators
{
    /// <summary>
    /// Populates Excel rows with contact information.
    /// </summary>
    public class ContactsInfoReportRowPopulator : LinePopulatorBase, IContactsInfoReportRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, ContactsInfoData data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));
            ArgumentNullException.ThrowIfNull(data.User, nameof(data.User));
            ArgumentNullException.ThrowIfNull(data.MasterClientCodes, nameof(data.MasterClientCodes));

            // Email
            SetCellValueAndStyle(worksheet, currentRow, 1, data.User.Email);

            // Master Client Codes (comma separated)
            var masterClientCodes = string.Join(", ", data.MasterClientCodes.OrderBy(code => code));
            SetCellValueAndStyle(worksheet, currentRow, 2, masterClientCodes);

            // Total number of companies
            var totalCompanies = data.TotalCompanies;
            SetCellValueAndStyle(worksheet, currentRow, 3, totalCompanies);
        }

        /// <inheritdoc />
        protected override XLCellValue ConvertToCellValue(object value)
        {
            return value switch
            {
                null => string.Empty,
                string str => str,
                int intValue => intValue,
                _ => value.ToString()
            };
        }
    }
}
