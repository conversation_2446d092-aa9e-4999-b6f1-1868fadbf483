using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Financial period changes scherma.
    /// </summary>
    [BsonIgnoreExtraElements]
    public sealed class FinancialPeriodChangesSchema : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the date changed of the entry.
        /// </summary>
        [BsonElement("date_changed")]
        public DateTime DateChanged { get; set; }

        /// <summary>
        /// Gets or sets the changed by.
        /// </summary>
        [BsonElement("changed_by")]
        public string ChangedBy { get; set; }

        /// <summary>
        /// Gets or sets the old_start_date of the entry.
        /// </summary>
        [BsonElement("old_start_date")]
        public DateTime? OldStartDate { get; set; }

        /// <summary>
        /// Gets or sets the old_end_date of the entry.
        /// </summary>
        [BsonElement("old_end_date")]
        public DateTime? OldEndDate { get; set; }

        /// <summary>
        /// Gets or sets the new_start_date of the entry.
        /// </summary>
        [BsonElement("new_start_date")]
        public DateTime? NewStartDate { get; set; }

        /// <summary>
        /// Gets or sets the new_end_date of the entry.
        /// </summary>
        [BsonElement("new_end_date")]
        public DateTime? NewEndDate { get; set; }
    }
}
