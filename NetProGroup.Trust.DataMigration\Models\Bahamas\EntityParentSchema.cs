using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents parent entity information
    /// </summary>
    [BsonIgnoreExtraElements]
    public class EntityParentSchema
    {
        /// <summary>
        /// Type of parent entity
        /// </summary>
        [BsonElement("parent_type")]
        [Required]
        public string ParentType { get; set; }

        /// <summary>
        /// Name of the parent entity
        /// </summary>
        [BsonElement("parent_name")]
        [Required]
        public string ParentName { get; set; }

        /// <summary>
        /// Alternative name for the parent entity
        /// </summary>
        [BsonElement("alternative_name")]
        public string AlternativeName { get; set; }

        /// <summary>
        /// Jurisdiction where the parent entity is incorporated
        /// </summary>
        [BsonElement("jurisdiction")]
        [Required]
        public string Jurisdiction { get; set; }

        /// <summary>
        /// Incorporation number of the parent entity
        /// </summary>
        [BsonElement("incorporation_number")]
        [Required]
        public string IncorporationNumber { get; set; }

        /// <summary>
        /// Tax Identification Number (TIN) of the parent entity
        /// </summary>
        [BsonElement("TIN")]
        [Required]
        public string TIN { get; set; }
    }
}