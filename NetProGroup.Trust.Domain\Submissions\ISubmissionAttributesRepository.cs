﻿// <copyright file="ISubmissionAttributesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Submissions
{
    /// <summary>
    /// Interface for the Submission repository.
    /// </summary>
    public interface ISubmissionAttributesRepository : IRepository<SubmissionAttribute, Guid>, IRepositoryService
    {
        /// <summary>
        /// Gets the DbContext of the repository.
        /// </summary>
        DbContext DbContext { get; }
    }
}
