// <copyright file="EntryMigrationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataMigration.Factories;
using NetProGroup.Trust.DataMigration.Models.Bahamas;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Shared.Jurisdictions;
using static NetProGroup.Trust.DataMigration.Models.Bahamas.MigrationConsts;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// Service for migrating entry data.
    /// </summary>
    public class EntryMigrationService
    {
        private readonly ILogger<EntryMigrationService> _logger;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IModulesRepository _modulesRepository;
        private readonly SubmissionMigrationService _submissionMigrationService;
        private readonly ActivityLogMigrationService _activityLogMigrationService;
        private readonly RequestForInformationMigrationService _requestForInformationMigrationService;
        private readonly FileMigrationService _fileMigrationService;
        private readonly IMongoDbFactory _mongoDbFactory;
        private readonly IFormDocumentAttributesRepository _formDocumentAttributesRepository;
        private readonly TrustDbContext _dbContext;
        private readonly IOptions<DataMigrationAppSettings> _appSettings;
        private Module _module;

        /// <summary>
        /// Initializes a new instance of the <see cref="EntryMigrationService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="legalEntitiesRepository">The legal entities repository.</param>
        /// <param name="modulesRepository">The modules repository.</param>
        /// <param name="submissionMigrationService">The submission service.</param>
        /// <param name="activityLogMigrationService">The activity log service.</param>
        /// <param name="requestForInformationMigrationService">The request for information service.</param>
        /// <param name="fileMigrationService">The file migration service.</param>
        /// <param name="mongoDbFactory">The MongoDB factory.</param>
        /// <param name="formDocumentAttributesRepository">The form document attributes repository.</param>
        /// <param name="dbContext">The database context.</param>
        /// <param name="appSettings">The application settings.</param>
        public EntryMigrationService(
            ILogger<EntryMigrationService> logger,
            ILegalEntitiesRepository legalEntitiesRepository,
            IModulesRepository modulesRepository,
            SubmissionMigrationService submissionMigrationService,
            ActivityLogMigrationService activityLogMigrationService,
            RequestForInformationMigrationService requestForInformationMigrationService,
            FileMigrationService fileMigrationService,
            IMongoDbFactory mongoDbFactory,
            IFormDocumentAttributesRepository formDocumentAttributesRepository,
            TrustDbContext dbContext,
            IOptions<DataMigrationAppSettings> appSettings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _legalEntitiesRepository = legalEntitiesRepository ?? throw new ArgumentNullException(nameof(legalEntitiesRepository));
            _modulesRepository = modulesRepository ?? throw new ArgumentNullException(nameof(modulesRepository));
            _submissionMigrationService = submissionMigrationService ?? throw new ArgumentNullException(nameof(submissionMigrationService));
            _activityLogMigrationService = activityLogMigrationService ?? throw new ArgumentNullException(nameof(activityLogMigrationService));
            _requestForInformationMigrationService = requestForInformationMigrationService ?? throw new ArgumentNullException(nameof(requestForInformationMigrationService));
            _fileMigrationService = fileMigrationService ?? throw new ArgumentNullException(nameof(fileMigrationService));
            _mongoDbFactory = mongoDbFactory ?? throw new ArgumentNullException(nameof(mongoDbFactory));
            _formDocumentAttributesRepository = formDocumentAttributesRepository;
            _dbContext = dbContext;
            _appSettings = appSettings;
        }

        /// <summary>
        /// Handles the processing of a single entry.
        /// </summary>
        /// <param name="entry">The entry to process.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="moduleId">The module id.</param>
        /// <param name="moduleName">The name id.</param>
        /// <param name="jurisdictionId">The id of the jurisdiction.</param>
        /// <param name="jurisdictionName">The name of the jurisdiction.</param>
        /// <returns>A tuple indicating success and any errors encountered.</returns>
        public async Task<(bool Success, List<string> Errors)> HandleEntry(Entry entry,
            ApplicationUser migrationStartedByUser,
            string region,
            Guid moduleId,
            string moduleName,
            Guid jurisdictionId,
            string jurisdictionName)
        {
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            var errors = new List<string>();

            if (entry.FinancialPeriodDetails == null)
            {
                errors.Add($"financial_period_details not available!");
                return (false, errors);
            }

            int financialYear = entry.FinancialPeriodDetails.FinancialPeriodEnds.Year;

            if (entry.Version == null)
            {
                errors.Add("Submission version may not be null.");
                return (false, errors);
            }

            var legalEntity = await GetMatchingLegalEntity(entry, jurisdictionName);
            var formTemplateVersion = await _submissionMigrationService.GetOrCreateTemplateVersionAsync(financialYear, entry.Version, jurisdictionId, jurisdictionName, moduleName, moduleId);
            var submission = await _submissionMigrationService.GetOrCreateSubmissionAsync(legalEntity, financialYear, moduleId);

            var (submissionStatus, submissionIsPaid, formDocumentStatus, formDocumentRevisionStatus) = DetermineStatuses(entry);

            SubmissionMigrationService.SetSubmissionProperties(submission, entry, formTemplateVersion, financialYear, legalEntity, submissionStatus, submissionIsPaid, moduleId);
            SubmissionMigrationService.SetSubmissionAttributes(submission, entry);
            SubmissionMigrationService.SetFormDocumentProperties(submission, entry, formTemplateVersion, legalEntity, financialYear, formDocumentStatus, moduleId);

            var formDocumentRevision = _submissionMigrationService.GetOrCreateFormDocumentRevision(entry, submission, formTemplateVersion, formDocumentRevisionStatus);
            var (mappingSuccess, mappingErrors, form) = MapEntryToForm(entry, formDocumentRevision);

            if (!mappingSuccess)
            {
                errors.AddRange(mappingErrors);
                return (false, errors);
            }

            await UpdateFormAttributes(form, submission.FormDocument);

            // We have to save here because otherwise the activity log creation will fail because the entity ID will be empty.
            await _dbContext.SaveChangesAsync();

            await _activityLogMigrationService.CreateMigrationActivityLogs(entry, migrationStartedByUser, region, submission, invoice: null, payment: null, financialYear);

            await _requestForInformationMigrationService.CreateMigrationRequestedInforations(entry, migrationStartedByUser, region, submission, financialYear, errors);
            await _requestForInformationMigrationService.CreateMigrationClientReturnedInformations(entry, migrationStartedByUser, submission, region, financialYear, errors);

            await _dbContext.SaveChangesAsync();

            // Migrate files:
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.BankingBusiness, FormKeys.BusinessPrefixBanking, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.InsuranceBusiness, FormKeys.BusinessPrefixInsurance, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.FundManagementBusiness, FormKeys.BusinessPrefixFundmanagement, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.FinanceLeasingBusiness, FormKeys.BusinessPrefixFinancialLeasing, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.HeadquartersBusiness, FormKeys.BusinessPrefixHeadquarters, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.ShippingBusiness, FormKeys.BusinessPrefixShipping, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.HoldingBusiness, FormKeys.BusinessPrefixHolding, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.IntellectualPropertyBusiness, FormKeys.BusinessPrefixIntellectualProperty, errors);
            await _fileMigrationService.MigrateBusinessFiles(submission, entry.ServiceCentreBusiness, FormKeys.BusinessPrefixServiceCentre, errors);

            await _fileMigrationService.MigrateFinancialPeriodFiles(submission, entry.FinancialPeriodDetails, errors);
            await _fileMigrationService.MigrateTaxResidencyFiles(submission, entry.TaxResidency, errors);
            await _fileMigrationService.MigrateRelevantActivitiesFiles(submission, entry.RelevantActivities, errors);
            await _fileMigrationService.MigrateSupportingDetailsFiles(submission, entry.SupportingDetails, errors);

            return (errors.Count == 0, errors);
        }

        /// <summary>
        /// Determines the statuses for a given entry.
        /// </summary>
        /// <param name="entry">The entry to determine statuses for.</param>
        /// <returns>A tuple containing the determined statuses.</returns>
        private static (SubmissionStatus submissionStatus, bool submissionIsPaid, FormDocumentStatus formDocumentStatus,
            FormDocumentRevisionStatus formDocumentRevisionStatus) DetermineStatuses(Entry entry)
        {
            SubmissionStatus submissionStatus;
            FormDocumentStatus formDocumentStatus;
            FormDocumentRevisionStatus formDocumentRevisionStatus;

            if (entry.Status is EntryStatus.Submitted or EntryStatus.Paid)
            {
                formDocumentStatus = FormDocumentStatus.Finalized;
                submissionStatus = SubmissionStatus.Submitted;
                formDocumentRevisionStatus = FormDocumentRevisionStatus.Finalized;
            }
            else if (entry.Status == EntryStatus.Saved)
            {
                if (entry.Reopened?.Details == null || entry.Reopened?.Details.Count == 0)
                {
                    formDocumentStatus = FormDocumentStatus.Draft;
                    submissionStatus = SubmissionStatus.Draft;
                }
                else
                {
                    formDocumentStatus = FormDocumentStatus.Revision;
                    submissionStatus = SubmissionStatus.Revision;
                }

                formDocumentRevisionStatus = FormDocumentRevisionStatus.Draft;
            }
            else if (entry.Status == EntryStatus.ReOpen)
            {
                formDocumentStatus = FormDocumentStatus.Revision;
                submissionStatus = SubmissionStatus.Revision;

                formDocumentRevisionStatus = FormDocumentRevisionStatus.Draft;
            }
            //else if(entry.Status == EntryStatus.InformationRequest)
            //{
            //    // TODO INFORMATION REQUEST
            //}
            else
            {
                throw new ConstraintException($"Unknown entry status: {entry.Status}");
            }

            var submissionIsPaid = entry.Payment != null;

            return (submissionStatus, submissionIsPaid, formDocumentStatus, formDocumentRevisionStatus);
        }

        /// <summary>
        /// Maps an entry to a form.
        /// </summary>
        /// <param name="entry">The entry to map.</param>
        /// <param name="formDocumentRevision">The form document revision to map to.</param>
        /// <returns>A tuple indicating success and any errors encountered.</returns>
        private (bool Success, List<string> Errors, KeyValueForm Form) MapEntryToForm(Entry entry, FormDocumentRevision formDocumentRevision)
        {
            _logger.LogTrace("Mapping entry {EntryId} to form", entry.Id);
            var formBuilder = formDocumentRevision.GetFormBuilder();
            var form = new KeyValueForm();
            formBuilder.Form = form;

            var (mappingSuccess, mappingErrors) = EntryToFormMapper.MapEntryToForm(entry, form, _appSettings.Value.CountryOverrides);

            if (mappingSuccess)
            {
                formDocumentRevision.DataAsJson = formBuilder.ToJson();
            }

            _logger.LogTrace("Mapping entry {EntryId} to form completed with success: {Success}", entry.Id, mappingSuccess);

            return (mappingSuccess, mappingErrors, form);
        }

        /// <summary>
        /// Gets the matching legal entity for an entry.
        /// </summary>
        /// <param name="entry">The entry to find a matching legal entity for.</param>
        /// <param name="jurisdictionName">The name of the jurisdiction.</param>
        /// <returns>The matching legal entity.</returns>
        private async Task<LegalEntity> GetMatchingLegalEntity(Entry entry, string jurisdictionName)
        {
            var companyCode = entry.Company;

            var (company, legalEntity) = await FindLegalEntity(companyCode);

            if (legalEntity == null)
            {
                if (company == null)
                {
                    _logger.LogError("Company with code '{CompanyCode}' not found in {JurisdictionName} database", companyCode, jurisdictionName);
                    throw new ConstraintException($"Company with code '{companyCode}' not found in {jurisdictionName} database.");
                }

                _logger.LogError("Company with code '{CompanyCode}' found in {JurisdictionName} database, but LegalEntity with code '{CompanyCode}' not found in PCP database", company.Code, jurisdictionName, company.Code);
                throw new ConstraintException($"Company with code '{companyCode}' found in {jurisdictionName} database, but LegalEntity with code '{company.Code}' not found in PCP database.");
            }

            _logger.LogInformation("Found legal entity with code '{LegalEntityCode}' for company code '{CompanyCode}'", legalEntity.Code, companyCode);

            return legalEntity;
        }

        private async Task<(Company tbahCompany, LegalEntity pcpLegalEntity)> FindLegalEntity(string companyCode)
        {
            // Get company from mongo database - always use Bahamas for services in the Bahamas folder
            var mongoDatabase = _mongoDbFactory.GetMongoDatabase(JurisdictionCodes.Bahamas);
            var company = await mongoDatabase.GetCollection<Company>("companies")
                .Find(c => c.Code == companyCode)
                .SingleOrDefaultAsync();

            var legalEntity = await _legalEntitiesRepository.FindFirstOrDefaultByConditionAsync(
                entity => entity.Code == companyCode,
                entities => entities);

            return (company, legalEntity);
        }

        /// <summary>
        /// Gets the module for the migration.
        /// </summary>
        /// <returns>The module.</returns>
        private async Task<Module> GetModule()
        {
            if (_module != null)
            {
                return _module;
            }

            _logger.LogDebug("Fetching module with key {ModuleKey}", ModuleKeyConsts.SimplifiedTaxReturn);
            _module = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.EconomicSubstanceBahamas);

            if (_module == null)
            {
                _logger.LogError("Module with key '{ModuleKey}' not found", ModuleKeyConsts.SimplifiedTaxReturn);
                throw new ConstraintException($"Module with key '{ModuleKeyConsts.SimplifiedTaxReturn}' not found");
            }

            return _module;
        }

        private async Task UpdateFormAttributes(KeyValueForm form, FormDocument formDocument)
        {
            // Update attributes
            var existingAttributes = formDocument.Attributes.ToList();

            var formValues = form.GetFormValues();
            foreach (var fieldValue in formValues)
            {
                var attr = existingAttributes.FirstOrDefault(x => x.Key == fieldValue.Key);
                if (attr != null)
                {
                    attr.Value = fieldValue.Value;
                    existingAttributes.Remove(attr);
                }
                else
                {
                    formDocument.Attributes.Add(new FormDocumentAttribute(fieldValue.Key, fieldValue.Key) { Value = fieldValue.Value });
                }
            }

            if (existingAttributes.Count > 0)
            {
                await _formDocumentAttributesRepository.DeleteAsync(existingAttributes, false);
            }
        }
    }
}