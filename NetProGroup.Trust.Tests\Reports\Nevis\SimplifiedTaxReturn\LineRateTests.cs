﻿using ClosedXML.Excel;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.Nevis.Financial;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports.Nevis.SimplifiedTaxReturn
{
    [TestFixture]
    public sealed class LineRateTests : TestBase
    {
        /// <summary>
        /// Tests that the referral office is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task LineExchangeRate_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);

            // Arrange
            const string ExpectedLineExchangeRate = "1";
            await CreateSubmission();

            // Act & Assert
            await AssertCellValue(20, ExpectedLineExchangeRate, $"Cell (5, 20) should contain '{NetProGroup.Trust.Reports.Nevis.Financial.Values.InvoiceExportValues.LExchange}'");
        }

        /// <summary>
        /// Tests that the referral office is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task LateLineExchangeRate_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);

            // Arrange
            const string ExpectedLineExchangeRate = "1";
            const string ExpectedLateLineExchangeRate = "1";
            await CreateSubmission(setLate: true);


            // Act & Assert
            await AssertCellValue(20, ExpectedLineExchangeRate, $"Cell (5, 20) should contain '{NetProGroup.Trust.Reports.Nevis.Financial.Values.InvoiceExportValues.LExchange}'");
            await AssertCellValue(20, ExpectedLateLineExchangeRate, $"Cell (6, 20) should contain '{NetProGroup.Trust.Reports.Nevis.Financial.Values.InvoiceExportValues.LExchange}'", rowNumber: 6);
        }

        /// <summary>
        /// Creates a submission for testing with customizable properties.
        /// </summary>
        /// <param name="legalEntitySetup">Optional action to customize the legal entity.</param>
        /// <param name="submissionDTOSetup">Optional action to customize the submission DTO.</param>
        /// <param name="dataSet">Optional dataset to update the submission with.</param>
        /// <param name="submissionSetup">Optional action to perform additional setup on the submission after creation.</param>
        /// <param name="markSubmissionAsPaid">Whether to mark the submission as paid.</param>
        /// <returns>The created submission.</returns>
        private async Task<SubmissionDTO> CreateSubmission(
            Action<LegalEntity> legalEntitySetup = null,
            Action<StartSubmissionDTO> submissionDTOSetup = null,
            Dictionary<string, string> dataSet = null,
            Func<ISubmissionsManager, SubmissionDTO, Task> submissionSetup = null,
            bool markSubmissionAsPaid = true,
            bool setLate = false)
        {
            // Create a legal entity
            var legalEntity = new LegalEntity()
            {
                Name = "Test Legal Entity",
                Code = "TEST_LEGAL_ENTITY",
                JurisdictionId = JurisdictionNevisId,
                MasterClientId = _masterClient.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                EntityTypeCode = LegalEntityTypes.IBC,
                EntityType = LegalEntityType.Company,
                EntityStatus = LegalEntityStatusNames.Active,
                ExternalUniqueId = "asdf",
                IncorporationNr = "1234",
                LegacyCode = "1234",
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                EntityTypeName = "IBC",
                ReferralOffice = "Test Office"
            };

            // Apply custom setup to the legal entity if provided
            legalEntitySetup?.Invoke(legalEntity);

            // Insert the legal entity
            legalEntity = await _server.Services.GetRequiredService<ILegalEntitiesRepository>().InsertAsync(legalEntity, true);

            // Create a submission
            var submissionsManager = _server.Services.GetService<ISubmissionsManager>();

            // Create a default submission DTO
            var startSubmissionDTO = new StartSubmissionDTO()
            {
                FinancialYear = 2019,
                ModuleId = ModuleStrId,
                LegalEntityId = legalEntity.Id,
            };

            // Apply custom setup to the submission DTO if provided
            submissionDTOSetup?.Invoke(startSubmissionDTO);

            // Ensure LegalEntityId is set
            if (startSubmissionDTO.LegalEntityId == Guid.Empty)
            {
                startSubmissionDTO.LegalEntityId = legalEntity.Id;
            }

            var submission = await submissionsManager.StartSubmissionAsync(startSubmissionDTO);

            // Update submission dataset if provided
            if (dataSet != null)
            {
                await submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO()
                {
                    Id = submission.Id,
                    DataSet = dataSet
                });
            }

            // Apply custom setup to the submission if provided
            if (submissionSetup != null)
            {
                await submissionSetup(submissionsManager, submission);
            }

            if (setLate)
            {
                var settingsManager = _server.Services.GetRequiredService<ISettingsManager>();
                //settingsManager.GetDerivedSettingsForCompany<>
                var latePaymentFeeDTO = new STRLatePaymentFeeDTO
                {
                    FinancialYear = 2019,
                    Amount = 100,
                    CurrencyCode = "USD",
                    Charge = true,
                    Description = "Test",
                    InvoiceText = "Test",
                    StartAt = DateTime.Today,
                    EndAt = DateTime.Today,
                };
                await settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, JurisdictionNevisId);
            }

            // Submit the submission
            await submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO() { SubmissionId = submission.Id });

            if (markSubmissionAsPaid)
            {
                await submissionsManager.MarkSubmissionsAsPaidAsync([submission.Id], true, [JurisdictionNevisId]);
            }

            return submission;
        }

        /// <summary>
        /// Helper method to generate the Nevis report.
        /// </summary>
        /// <returns>The generated report.</returns>
        private async Task<FinancialReportOutput> GenerateReport()
        {
            // Create a generator with the mocked template provider
            var reportGenerator = _server.Services.GetRequiredService<IFinancialReportGenerator>();

            SetWorkContextUser(ManagementUser);

            // Generate the report
            return await reportGenerator.GenerateTodayFinancialReportAsync();
        }

        /// <summary>
        /// Helper method to assert a cell value in the Excel report.
        /// </summary>
        /// <param name="columnIndex">The column index (1-based).</param>
        /// <param name="expectedValue">The expected value.</param>
        /// <param name="message">The assertion message.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task AssertCellValue(int columnIndex, string expectedValue, string message, int rowNumber = 5)
        {
            // Generate the report
            var result = await GenerateReport();
            result.Should().NotBeNull();

            // Read the generated Excel file to verify the content
            using (var resultWorkbook = new XLWorkbook(new MemoryStream(result.FileContent)))
            {
                var worksheet = resultWorkbook.Worksheet(1);
                var cellValue = worksheet.Cell(rowNumber, columnIndex).Value.ToString();

                // Verify that the value is correctly populated
                cellValue.Should().Be(expectedValue, message);
            }
        }
    }
}
