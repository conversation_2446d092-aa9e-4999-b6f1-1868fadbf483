﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class RemoveSyncExcludedLegalEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SyncExcludedLegalEntities");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SyncExcludedLegalEntities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "newid()"),
                    DeletedByMigrationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ConcurrencyStamp = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    LegacyCode = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncExcludedLegalEntities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SyncExcludedLegalEntity_DataMigration",
                        column: x => x.DeletedByMigrationId,
                        principalTable: "DataMigrations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SyncExcludedLegalEntities_Code",
                table: "SyncExcludedLegalEntities",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_SyncExcludedLegalEntities_Code_DeletedByMigrationId",
                table: "SyncExcludedLegalEntities",
                columns: new[] { "Code", "DeletedByMigrationId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SyncExcludedLegalEntities_DeletedByMigrationId",
                table: "SyncExcludedLegalEntities",
                column: "DeletedByMigrationId");
        }
    }
}
