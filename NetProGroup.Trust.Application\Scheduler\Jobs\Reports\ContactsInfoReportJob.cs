// <copyright file="ContactsInfoReportJob.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.ApplicationInsights;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.AppServices.Reports.Nevis.ContactsInfoReports;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Jobs.Reports
{
    /// <summary>
    /// Report job implementation for generating contact information reports.
    /// </summary>
    public class ContactsInfoReportJob(ILogger<ContactsInfoReportJob> logger, IServiceProvider serviceProvider)
        : JobBase<object>(logger, serviceProvider), IContactsInfoReportJob
    {
        /// <inheritdoc/>
        public Guid ScheduledJobId => new Guid("{A1B45E7F-8A12-4C89-B6E3-1F2A9D5B0C8D}");

        /// <inheritdoc/>
        public string ScheduledJobKey => "reports.nevis.contacts-info";

        /// <inheritdoc/>
        public string ScheduledJobName => "Contact Information Report Job - Nevis";

        /// <inheritdoc/>
        protected override async Task DoWorkAsync(object data, CancellationToken token = default)
        {
            var jobLock = await AcquireLockAsync(ScheduledJobId);

            if (!jobLock.Id.HasValue)
            {
                throw new NoLockException();
            }

            try
            {
                var contactsInfoReportService = ServiceProvider.GetRequiredService<IContactsInfoReportService>();

                Logger.LogInformation("Starting report generation for {JobName}", ScheduledJobName);
                await contactsInfoReportService.GenerateReportAsync(jobLock);
                Logger.LogInformation("Finished report generation for {JobName}", ScheduledJobName);
            }
            finally
            {
                await ReleaseLockAsync(jobLock);
            }
        }
    }
}
