using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a reopened schema in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class DetailsSchema<T> 
    {
        /// <summary>
        /// Gets or sets the unique identifier for the reopened schema.
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the list of details for each time the entry was reopened.
        /// </summary>
        [BsonElement("details")]
        public List<T> Details { get; set; }
    }
}
