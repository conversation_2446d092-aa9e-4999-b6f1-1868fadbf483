// <copyright file="ITASubmissionRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Request for exporting the IRD submission report.
    /// </summary>
    public class ITASubmissionRequestDTO
    {
        /// <summary>
        /// Gets or sets the list of submission ids to export.
        /// </summary>
        [Required]
        public IEnumerable<Guid> SubmissionIds { get; set; }
    }
}