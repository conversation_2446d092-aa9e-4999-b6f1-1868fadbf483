﻿// <copyright file="IFormDocumentDocumentsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Forms
{
    /// <summary>
    /// Interface for the FormDocument repository.
    /// </summary>
    public interface IFormDocumentDocumentsRepository : IRepository<FormDocumentDocument, Guid>, IRepositoryService
    {
        /// <summary>
        /// Gets the DbContext of the repository.
        /// </summary>
        DbContext DbContext { get; }
    }
}
