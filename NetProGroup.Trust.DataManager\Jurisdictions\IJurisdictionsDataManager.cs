﻿// <copyright file="IJurisdictionsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.DataManager.Jurisdictions.RequestResponses;

namespace NetProGroup.Trust.DataManager.Jurisdictions
{
    /// <summary>
    /// Interface for the datamanager for Jurisdictions.
    /// </summary>
    public interface IJurisdictionsDataManager : IScopedService
    {
        /// <summary>
        /// Creates the Jurisdiction from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether  to save the changes immediately.</param>
        /// <returns>A <see cref="Task{JurisdictionDTO}"/> representing the asynchronous operation.</returns>
        Task<JurisdictionDTO> CreateJurisdictionAsync(CreateJurisdictionDTO model, bool saveChanges = false);

        /// <summary>
        /// Updates the Jurisdiction from the model.
        /// </summary>
        /// <param name="model">The inbound model.</param>
        /// <param name="saveChanges">Whether to save the changes immediately.</param>
        /// <returns>A <see cref="Task{JurisdictionDTO}"/> representing the asynchronous operation.</returns>
        Task<JurisdictionDTO> UpdateJurisdictionAsync(JurisdictionDTO model, bool saveChanges = false);

        /// <summary>
        /// Gets a paged list with jurisdictions.
        /// </summary>
        /// <param name="request">Request with optional parameters to search for.</param>
        /// <returns>A <see cref="Task{ListJurisdictionsResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ListJurisdictionsResponse> ListJurisdictionsAsync(ListJurisdictionsRequest request);

        /// <summary>
        /// Gets a Jurisdiction by its code.
        /// </summary>
        /// <param name="jurisdictionCode">The code of the jurisdiction.</param>
        /// <returns>A <see cref="Task{JurisdictionDTO}"/> representing the result of the asynchronous operation.</returns>
        Task<JurisdictionDTO> GetByCodeAsync(string jurisdictionCode);

        /// <summary>
        /// Gets a Jurisdiction by its id.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction as Guid.</param>
        /// <returns>A <see cref="Task{JurisdictionDTO}"/> representing the result of the asynchronous operation.</returns>
        Task<JurisdictionDTO> GetByIdAsync(Guid jurisdictionId);

        /// <summary>
        /// Gets the Jurisdiction related to the given submission id.
        /// </summary>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <returns>A <see cref="Task{JurisdictionDTO}"/> representing the result of the asynchronous operation.</returns>
        Task<JurisdictionDTO> GetBySubmissionIdAsync(Guid submissionId);

        /// <summary>
        /// Gets a Jurisdiction by its id.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction.</param>
        /// <returns>A <see cref="Task{JurisdictionDTO}"/> representing the result of the asynchronous operation.</returns>
        Task SetInitialSyncCompletedAsync(Guid jurisdictionId);
    }
}