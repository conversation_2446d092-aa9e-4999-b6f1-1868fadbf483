﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Modules
{
    public class ModuleAppServiceTests : TestBase
    {
        private IModulesAppService _moduleAppService;

        [SetUp]
        public void Setup()
        {
            _moduleAppService = _server.Services.GetRequiredService<IModulesAppService>();
        }

        [Test]
        public async Task GetAllModulesAsync_ShouldGetModules()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            // Act
            var response = await _moduleAppService.GetAllModulesAsync();

            // Assert
            response.Should().NotBeNull();
            response.Modules.Count.Should().Be(5);
        }

        [Test]
        public async Task GetAllModulesAsync_ShouldThrowPermissionException()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            // Act
            Func<Task> act = async () => await _moduleAppService.GetAllModulesAsync();

            // Assert
            await act.Should().ThrowAsync<ForbiddenException>();
        }
    }
}