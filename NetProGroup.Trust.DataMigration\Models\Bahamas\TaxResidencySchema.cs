using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Tax residenty schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class TaxResidencySchema
    {
        /// <summary>
        /// Gets or sets the resident_in_BHA.
        /// </summary>
        [BsonElement("resident_in_BHA")]
        public bool? ResidentInBha { get; set; }

        /// <summary>
        /// Gets or sets the is_investment_fund_2019.
        /// </summary>
        [BsonElement("is_investment_fund_2019")]
        public bool? IsInvestmentFund2019 { get; set; }

        /// <summary>
        /// Gets or sets the evidence_non_residency.
        /// </summary>
        [BsonElement("evidence_non_residency")]
        public List<FileSchema> EvidenceNonResidency { get; set; }

        /// <summary>
        /// Gets or sets the claim_tax_residency_outside.
        /// </summary>
        [BsonElement("claim_tax_residency_outside")]
        public bool? ClaimTaxResidencyOutside { get; set; }

        /// <summary>
        /// Gets or sets the entity_jurisdiction.
        /// </summary>
        [BsonElement("entity_jurisdiction")]
        public string EntityJurisdiction { get; set; }

        /// <summary>
        /// Gets or sets the has_immediate_parents.
        /// </summary>
        [BsonElement("foreign_tax_id_number")]
        public string ForeignTaxIdNumber { get; set; }

        /// <summary>
        /// Gets or sets the has_immediate_parents.
        /// </summary>
        [BsonElement("has_ultimate_parents")]
        public bool? HasUltimateParents { get; set; }

        /// <summary>
        /// Gets or sets the has_immediate_parents.
        /// </summary>
        [BsonElement("has_immediate_parents")]
        public bool? HasImmediateParents { get; set; }

        /// <summary>
        /// Gets or sets the additional_parents.
        /// </summary>
        [BsonElement("additional_parents")]
        public List<AdditionalParentsSchema> AdditionalParents { get; set; }
    }
}
