using ClosedXML.Excel;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter
{
    /// <summary>
    /// The data processor.
    /// </summary>
    /// <typeparam name="T"> The type of the data.</typeparam>
    public class DataProcessor<T>
    {
        private readonly Dictionary<int, ITemplateRowPopulator<T>> _rowPopulators;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataProcessor{T}"/> class.
        /// </summary>
        /// <param name="rowPopulators">The row populators.</param>
        public DataProcessor(Dictionary<int, ITemplateRowPopulator<T>> rowPopulators)
        {
            _rowPopulators = rowPopulators;
        }

        /// <summary>
        /// The method to process the data.
        /// </summary>
        /// <param name="worksheet">The worksheet to process the data on.</param>
        /// <param name="currentRow">The current row to start processing on.</param>
        /// <param name="data">The data to process.</param>
        public void ProcessData(IXLWorksheet worksheet, int currentRow, T data)
        {
            foreach (var (item, index) in _rowPopulators.Select((item, index) => (item, index)))
            {
                var row = currentRow + index - _rowPopulators.Count;
                item.Value.PopulateRow(worksheet, row, data);
            }
        }
    }
}