﻿// <copyright file="FeatureFlagsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.Contracts.FeatureFlags;
using NetProGroup.Trust.Application.Contracts.FeatureFlags.Models;

namespace NetProGroup.Trust.Application.AppServices.FeatureFlags
{
    /// <summary>
    /// Application service for managing feature flags.
    /// </summary>
    public class FeatureFlagsAppService : IFeatureFlagsAppService
    {
        private readonly List<FeatureFlagDTO> _featureFlagSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="FeatureFlagsAppService"/> class.
        /// </summary>
        /// <param name="flags">The feature flag settings.</param>
        /// <exception cref="ArgumentNullException">Thrown when the flags parameter is null.</exception>
        public FeatureFlagsAppService(IOptions<FeatureFlagSettings> flags)
        {
            if (flags == null)
            {
                throw new ArgumentNullException(nameof(flags), "The flags parameter cannot be null.");
            }

            _featureFlagSettings = flags.Value
                .Select(kvp => new FeatureFlagDTO { Name = kvp.Key.ToString(), IsEnabled = kvp.Value })
                .ToList();
        }

        /// <inheritdoc/>
        public Task<List<FeatureFlagDTO>> GetFeatureFlagsAsync()
        {
            return Task.FromResult(_featureFlagSettings);
        }
    }
}
