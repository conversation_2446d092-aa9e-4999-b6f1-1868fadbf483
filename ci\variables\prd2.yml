# Production environment variables
variables:
  # SQL Server configuration
  serverName: 'sqlsrv-pcp-prd2-prd-weu.database.windows.net'
  databaseName: 'sqldb-pcp-prd2-prd'
  
  # Azure App Service configuration
  webAppName: 'app-pcp-prd2-api-prd-weu'
  resourceGroup: 'rg-ttg-pcp-prd2-app-weu'
  slotName: 'production'
  
  # Agent pool
  pool: 'TT PCP - WindowsAgents prd2'
  
  # Environment settings
  environment: 'Production 2'
