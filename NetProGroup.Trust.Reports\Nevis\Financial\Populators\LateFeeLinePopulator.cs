using ClosedXML.Excel;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Reports.Nevis.Financial.Values;

namespace NetProGroup.Trust.Reports.Nevis.Financial.Populators
{
    /// <summary>
    /// Interface for the regular fee line populator.
    /// </summary>
    public interface ILateFeeLinePopulator : ITemplateRowPopulator<Submission>, ITransientService;

    /// <summary>
    /// Populate line 2 of the Excel template with the late fees.
    /// </summary>
    public class LateFeeLinePopulator : LinePopulatorBase, ILateFeeLinePopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 8;

        /// <inheritdoc />
        protected override string FontName => "Arial";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var invoiceLineFee = data.Invoice?.InvoiceLines
                .FirstOrDefault(il => il.ArticleNr == InvoiceLineFee.LateFee);

            if (invoiceLineFee is null) return;

            // Line number
            SetCellValueAndStyle(worksheet, currentRow, 2, "2");

            // RefCode - Legal Entity Legacy Code
            SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity?.Code ?? data.LegalEntity?.LegacyCode);

            // InvoiceType
            SetCellValueAndStyle(worksheet, currentRow, 4, InvoiceLateFeeExportValues.InvoiceType);

            // InvoiceRef - Invoice Number
            SetCellValueAndStyle(worksheet, currentRow, 5, data.Invoice.InvoiceNr);

            // BillAddress - Legal Entity Legacy Code
            SetCellValueAndStyle(worksheet, currentRow, 6, data.LegalEntity?.Code ?? data.LegalEntity?.LegacyCode);

            // MatterCode
            SetCellValueAndStyle(worksheet, currentRow, 7, InvoiceLateFeeExportValues.MatterCode);

            // CurCode - Currency Code
            SetCellValueAndStyle(worksheet, currentRow, 8, InvoiceLateFeeExportValues.CurCode);

            // DateInvoice - data Date
            SetCellValueAndStyle(worksheet, currentRow, 9, FormatDateAsLocalTime(data.SubmittedAt, data));

            // DateVoucher - data Date
            SetCellValueAndStyle(worksheet, currentRow, 10, FormatDateAsLocalTime(data.SubmittedAt, data));

            // Exchange Rate
            SetCellValueAndStyle(worksheet, currentRow, 11, InvoiceLateFeeExportValues.Exchange);

            // HeaderNote
            SetCellValueAndStyle(worksheet, currentRow, 12, string.Empty);

            // FooterNote
            SetCellValueAndStyle(worksheet, currentRow, 13, string.Empty);

            // Note
            SetCellValueAndStyle(worksheet, currentRow, 14, string.Empty);

            // PayTerm - Payment Terms
            SetCellValueAndStyle(worksheet, currentRow, 15, InvoiceLateFeeExportValues.PayTerm);

            // ServCode - Service Code
            SetCellValueAndStyle(worksheet, currentRow, 16, InvoiceLateFeeExportValues.ServCode);

            // Description
            SetCellValueAndStyle(worksheet, currentRow, 17, InvoiceLateFeeExportValues.Description);

            // OrgUnit - Organization Unit
            SetCellValueAndStyle(worksheet, currentRow, 18, string.Empty);

            // LCurCode - Local Currency Code
            SetCellValueAndStyle(worksheet, currentRow, 19, InvoiceLateFeeExportValues.LCurCode);

            // LExchange - Local Exchange Rate
            SetCellValueAndStyle(worksheet, currentRow, 20, InvoiceLateFeeExportValues.LExchange);

            // NrUnits - Number of Units
            SetCellValueAndStyle(worksheet, currentRow, 21, InvoiceLateFeeExportValues.NrUnits);

            // UnitPrice
            SetCellValueAndStyle(worksheet, currentRow, 22, invoiceLineFee.Amount);

            // LinePrice
            SetCellValueAndStyle(worksheet, currentRow, 23, invoiceLineFee.Amount);

            // VATperc - VAT Percentage
            SetCellValueAndStyle(worksheet, currentRow, 24, InvoiceLateFeeExportValues.VATPerc);

            // FromDate - data Date
            SetCellValueAndStyle(worksheet, currentRow, 25, FormatDateAsLocalTime(data.SubmittedAt, data));

            // ToDate - data Date
            SetCellValueAndStyle(worksheet, currentRow, 26, FormatDateAsLocalTime(data.SubmittedAt, data));

            // TaxCode
            SetCellValueAndStyle(worksheet, currentRow, 27, InvoiceLateFeeExportValues.TaxCode);

            // InterRevEntity - Internal Revenue Entity
            SetCellValueAndStyle(worksheet, currentRow, 28, string.Empty);

            // OrgCode - Organization Code
            SetCellValueAndStyle(worksheet, currentRow, 29, string.Empty);

            // UnitCode5
            SetCellValueAndStyle(worksheet, currentRow, 30, string.Empty);
        }
    }
}