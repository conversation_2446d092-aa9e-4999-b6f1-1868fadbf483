using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.DataMigrations;

namespace NetProGroup.Trust.DataMigration;

/// <summary>
/// Provides data management operations for data migration processes.
/// </summary>
public interface IDataMigrationsDataManager : IScopedService
{
    /// <summary>
    /// Gets or creates a migration record for the specified region.
    /// </summary>
    /// <param name="region">The region for the migration record.</param>
    /// <param name="startedByUserId">The user ID that started the migration.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task<Domain.DataMigrations.DataMigration> GetOrCreateMigrationRecordAsync(string region, Guid startedByUserId);

    /// <summary>
    /// Updates the migration record.
    /// </summary>
    /// <param name="migrationRecord">The migration record to update.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task UpdateMigrationRecordAsync(Domain.DataMigrations.DataMigration migrationRecord);

    /// <summary>
    /// Gets or creates an entity progress record.
    /// </summary>
    /// <param name="migrationRecord">The associated migration record.</param>
    /// <param name="entityName">The name of the entity.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task<EntityMigrationProgress> GetOrCreateEntityProgressAsync(Domain.DataMigrations.DataMigration migrationRecord, string entityName);

    /// <summary>
    /// Updates the entity progress record.
    /// </summary>
    /// <param name="entityProgress">The entity progress record to update.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task UpdateEntityProgressAsync(EntityMigrationProgress entityProgress);

    /// <summary>
    /// Stores unprocessed records for a migration.
    /// </summary>
    /// <param name="migrationRecord">The migration record.</param>
    /// <param name="unprocessedRecords">The list of unprocessed records.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task StoreUnprocessedRecordsAsync(Domain.DataMigrations.DataMigration migrationRecord, List<UnprocessedRecord> unprocessedRecords);

    /// <summary>
    /// Gets the last data migration for a jurisdiction.
    /// </summary>
    /// <param name="jurisdictionCode">The jurisdiction code.</param>
    /// <returns>A <see cref="Task{DataMigration}"/> representing the asynchronous operation.</returns>
    Task<Domain.DataMigrations.DataMigration> GetLastDataMigrationForJurisdiction(string jurisdictionCode);

    /// <summary>
    /// Gets the migration record by ID.
    /// </summary>
    /// <param name="dataMigrationRecord">The migration ID.</param>
    /// <returns>A <see cref="Task{DataMigration}"/> representing the asynchronous operation.</returns>
    Task<Domain.DataMigrations.DataMigration> RefreshMigrationRecordAsync(
        Domain.DataMigrations.DataMigration dataMigrationRecord);
}