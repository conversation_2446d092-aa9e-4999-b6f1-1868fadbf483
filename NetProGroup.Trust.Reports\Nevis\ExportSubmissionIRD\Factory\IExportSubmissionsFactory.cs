using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Factory
{
    /// <summary>
    /// Factory for creating exporters.
    /// </summary>
    public interface IExportSubmissionsFactory : IScopedService
    {
        /// <summary>
        /// Creates an exporter for the given year.
        /// </summary>
        /// <returns>An exporter for the given year.</returns>
        IExportSubmissionsIRDGenerator CreateExportGenerator(ExportSubmissionDTO request);
    }
}