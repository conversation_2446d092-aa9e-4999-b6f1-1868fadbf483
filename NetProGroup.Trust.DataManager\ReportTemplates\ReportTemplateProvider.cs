// <copyright file="ReportTemplateProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Trust.DataManager.Exceptions;
using System.Reflection;

namespace NetProGroup.Trust.DataManager.ReportTemplates
{
    /// <summary>
    /// Provider for getting the report templates from the embedded resources.
    /// </summary>
    public class ReportTemplateProvider : IReportTemplateProvider
    {
        private readonly ILogger _logger;
        private readonly Assembly _currentAssembly;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReportTemplateProvider"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        public ReportTemplateProvider(ILogger<ReportTemplateProvider> logger)
        {
            _logger = logger;
            _currentAssembly = typeof(ReportTemplateProvider).Assembly;
        }

        /// <inheritdoc/>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Filenames are in lowercase")]
        public async Task<MemoryStream> GetExcelTemplateAsync(string templateName)
        {
            var templateResourceMapping = InitializeTemplateMapping();

            _logger.LogInformation("Attempting to load template: {TemplateName}", templateName);

            if (string.IsNullOrWhiteSpace(templateName))
            {
                throw new ArgumentNullException(nameof(templateName));
            }

            // Get the full resource name from the mapping
            if (!templateResourceMapping.TryGetValue(templateName.ToLowerInvariant(), out var resourceName))
            {
                throw new TemplateNotFoundException($"Template '{templateName}' not found in available resources.");
            }

            try
            {
                // Load the embedded resource as a stream
                await using var resourceStream = _currentAssembly.GetManifestResourceStream(resourceName) ?? throw new FileNotFoundException($"Embedded resource '{resourceName}' not found.");

                // Copy the content to a MemoryStream
                var memoryStream = new MemoryStream();
                await resourceStream.CopyToAsync(memoryStream);

                // Reset the position to the beginning of the MemoryStream
                memoryStream.Position = 0;

                _logger.LogInformation("Successfully loaded template: {TemplateName}", templateName);
                return memoryStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading template {TemplateName}: {ErrorMessage}",
                    templateName, ex.Message);

                throw new Framework.Exceptions.APIException($"Error loading template '{templateName}'", ex);
            }
        }

        /// <summary>
        /// Initializes the mapping between template names and resource names.
        /// </summary>
        private Dictionary<string, string> InitializeTemplateMapping()
        {
            try
            {
                var mapping = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                var resources = _currentAssembly.GetManifestResourceNames()
                    .Where(x => (x.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) || x.EndsWith(".xls", StringComparison.OrdinalIgnoreCase)));

                foreach (var resource in resources)
                {
                    // Extract template name from resource path
#pragma warning disable CA1308 // Normalize strings to uppercase
                    var templateName = Path.GetFileNameWithoutExtension(resource).Split('.').Last().ToLowerInvariant();
#pragma warning restore CA1308 // Normalize strings to uppercase

                    mapping[templateName] = resource;
                    _logger.LogInformation("Mapped template {TemplateName} to resource {ResourceName}",
                        templateName, resource);
                }

                if (!mapping.Any())
                {
                    _logger.LogWarning("No Excel templates found in embedded resources");
                }

                return mapping;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing template mapping");
                throw new InvalidOperationException("Failed to initialize template provider", ex);
            }
        }
    }
}