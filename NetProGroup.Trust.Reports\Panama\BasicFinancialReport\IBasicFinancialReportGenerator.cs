// <copyright file="IBasicFinancialReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Reports.Panama.BasicFinancialReport
{
    /// <summary>
    /// Interface for basic financial report generator.
    /// </summary>
    public interface IBasicFinancialReportGenerator : ITransientService
    {
        /// <summary>
        /// Generates a submissions report for the given submissions.
        /// </summary>
        /// <param name="submissions">The list of submissions to be included in the report.</param>
        /// <returns>A <see cref="Task{ReportOutput}"/> representing the asynchronous operation.</returns>
        Task<Trust.Reports.ReportOutput> GenerateSubmissionsReportAsync(List<Submission> submissions);

        /// <summary>
        /// Generates the financial report for today.
        /// </summary>
        /// <returns>A <see cref="Task{FinancialReportOutput}"/> representing the asynchronous operation.</returns>
        public Task<FinancialReportOutput> GenerateTodayFinancialReportAsync();

        /// <summary>
        /// Generates the name of the report for today.
        /// </summary>
        /// <returns>A Task{string} representing the asynchronous operation.</returns>
        public string GenerateReportNameForTodayAsync();

        /// <summary>
        /// Completes the financial export for the given submissions.
        /// </summary>
        /// <param name="submissions">The submissions to complete.</param>
        /// <param name="reportId">The ID of the report to complete.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public Task CompleteFinancialExport(IEnumerable<Submission> submissions, Guid reportId);
    }
}
