﻿// <copyright file="BVIPermissionsBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Builder for permissions for BVI.
    /// </summary>
    public class BVIPermissionsBuilder : PermissionsBuilderBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BVIPermissionsBuilder"/> class.
        /// </summary>
        public BVIPermissionsBuilder()
        {
            SetupCompanyPermissions();
            SetupBODIRPermissions();
            SetupAnnouncementPermissions();
            SetupEconomicSubstancePermissions();
        }

        /// <summary>
        /// Setup the company permissions for BVI roles.
        /// </summary>
        private void SetupCompanyPermissions()
        {
            SetupPermissions(WellKnownBVIRoleNames.BVI_Owner,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.ESBVIModule_Companies_View_Custom_ES_Fee,
                WellKnownPermissionNames.ESBVIModule_Companies_Set_Custom_ES_Fee,
                WellKnownPermissionNames.Companies_View_Log);

            SetupPermissions(WellKnownBVIRoleNames.BVI_CMU_SuperUser,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.ESBVIModule_Companies_View_Custom_ES_Fee,
                WellKnownPermissionNames.ESBVIModule_Companies_Set_Custom_ES_Fee,
                WellKnownPermissionNames.Companies_View_Log);

            SetupPermissions(WellKnownBVIRoleNames.BVI_Basic_User,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules);
        }

        /// <summary>
        /// Setup the BODIR permissions for BVI roles.
        /// </summary>
        private void SetupBODIRPermissions()
        {
            SetupPermissions(WellKnownBVIRoleNames.BVI_Owner,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);

            SetupPermissions(WellKnownBVIRoleNames.BVI_Officers_SuperUser,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);
        }

        /// <summary>
        /// Setup the announcement permissions for BVI roles.
        /// </summary>
        private void SetupAnnouncementPermissions()
        {
            SetupPermissions(WellKnownBVIRoleNames.BVI_Owner,
                WellKnownPermissionNames.AnnouncementModule_Search,
                WellKnownPermissionNames.AnnouncementModule_View,
                WellKnownPermissionNames.AnnouncementModule_Delete,
                WellKnownPermissionNames.AnnouncementModule_Create_Limited);

            SetupPermissions(WellKnownBVIRoleNames.BVI_COM_SuperUser,
               WellKnownPermissionNames.AnnouncementModule_Search,
               WellKnownPermissionNames.AnnouncementModule_View,
               WellKnownPermissionNames.AnnouncementModule_Delete);
        }

        /// <summary>
        /// Setup the Economic Substance permissions for BVI roles.
        /// </summary>
        private void SetupEconomicSubstancePermissions()
        {
            SetupPermissions(WellKnownBVIRoleNames.BVI_Owner,
                WellKnownPermissionNames.ESBVIModule_Submissions_View,
                WellKnownPermissionNames.ESBVIModule_Submissions_Search,
                WellKnownPermissionNames.ESBVIModule_Submissions_Export,
                WellKnownPermissionNames.ESBVIModule_Submissions_Reset,
                WellKnownPermissionNames.ESBVIModule_Submissions_Delete_Completed,
                WellKnownPermissionNames.ESBVIModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.ESBVIModule_Submissions_View_Paid,
                WellKnownPermissionNames.ESBVIModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.ESBVIModule_Payments_Import,
                WellKnownPermissionNames.ESBVIModule_Submissions_Export_ITA,
                WellKnownPermissionNames.ESBVIModule_Invoices_Export,
                WellKnownPermissionNames.ESBVIModule_Start_RFI_Request);

            SetupPermissions(WellKnownBVIRoleNames.BVI_ES_SuperUser,
                WellKnownPermissionNames.ESBVIModule_Submissions_View,
                WellKnownPermissionNames.ESBVIModule_Submissions_Search,
                WellKnownPermissionNames.ESBVIModule_Submissions_Export,
                WellKnownPermissionNames.ESBVIModule_Submissions_Reset,
                WellKnownPermissionNames.ESBVIModule_Submissions_Delete_Completed,
                WellKnownPermissionNames.ESBVIModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.ESBVIModule_Submissions_View_Paid,
                WellKnownPermissionNames.ESBVIModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.ESBVIModule_Payments_Import,
                WellKnownPermissionNames.ESBVIModule_Submissions_Export_ITA,
                WellKnownPermissionNames.ESBVIModule_Invoices_Export,
                WellKnownPermissionNames.ESBVIModule_Start_RFI_Request);

            SetupPermissions(WellKnownBVIRoleNames.BVI_Basic_User,
                WellKnownPermissionNames.ESBVIModule_Submissions_View,
                WellKnownPermissionNames.ESBVIModule_Submissions_Search,
                WellKnownPermissionNames.ESBVIModule_Submissions_Export,
                WellKnownPermissionNames.ESBVIModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.ESBVIModule_Submissions_View_Paid,
                WellKnownPermissionNames.ESBVIModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.ESBVIModule_Start_RFI_Request);
        }
    }
}
