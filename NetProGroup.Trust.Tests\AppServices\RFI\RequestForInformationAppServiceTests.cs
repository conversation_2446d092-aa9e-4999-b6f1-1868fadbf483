﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.RFI
{
    public class RequestForInformationAppServiceTests : TestBase
    {
        private IRequestsForInformationAppService _requestForInformationAppService;
        private ISubmissionsRepository _submissionsRepository;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private IRequestForInformationRepository _requestForInformationRepository;
        private Guid _submissionId;
        private Guid _rfiId;

        [SetUp]
        public async Task SetUp()
        {
            _requestForInformationAppService = _server.Services.GetRequiredService<IRequestsForInformationAppService>();
            _submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _requestForInformationRepository = _server.Services.GetRequiredService<IRequestForInformationRepository>();

            await Seed();
        }

        public async Task Seed()
        {
            var testLegalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = "Test",
                Code = "Test",
                EntityType = LegalEntityType.Company,
                MasterClient = _masterClient,
                Jurisdiction = JurisdictionNevis,
                EntityTypeName = LegalEntityTypes.IBC,
            };

            var legalEntity = await _legalEntitiesRepository.InsertAsync(testLegalEntity, true);
            await CreateTestSubmissionAsync();
            await CreateRFIAsync();
        }

        [Test]
        public async Task CompleteRequestForInformationFromManagementAsync_WithValidStatus_ShouldCompleteRFI()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var completeRFIData = new CompleteRequestForInformationManagementDTO
            {
                Remark = "RFI completed by management",
                SubmittedToRegulator = DateTime.Now
            };

            // Act
            await _requestForInformationAppService.CompleteRequestForInformationFromManagementAsync(_rfiId, completeRFIData);
            var updatedRFI = await _requestForInformationRepository.GetByIdAsync(_rfiId);

            // Assert
            updatedRFI.Status.Should().Be(RequestForInformationStatus.Completed);
            updatedRFI.Remark.Should().Be(completeRFIData.Remark);
            updatedRFI.SubmittedToRegulator.Should().Be(completeRFIData.SubmittedToRegulator);
            updatedRFI.Submission.Status.Should().Be(SubmissionStatus.Submitted);
        }

        [Test]
        public async Task CompleteRequestForInformationFromManagementAsync_WithInvalidStatus_ShouldThrowException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var rfi = await _requestForInformationRepository.GetByIdAsync(_rfiId);
            rfi.Status = RequestForInformationStatus.Completed;
            await _requestForInformationRepository.SaveChangesAsync();
            var completeRFIData = new CompleteRequestForInformationManagementDTO
            {
                Remark = "Attempt to complete already completed RFI",
                SubmittedToRegulator = DateTime.Now
            };

            // Act
            Func<Task> act = async () => await _requestForInformationAppService.CompleteRequestForInformationFromManagementAsync(_rfiId, completeRFIData);
            
            // Assert
            await act.Should().ThrowAsync<PreconditionFailedException>().WithMessage($"The request for information with id '{_rfiId}' is already completed.");
        }

        [Test]
        public async Task CompleteRequestForInformation_WithValidStatus_ShouldCompleteRFI()
        {
            // Arrange
            SetWorkContextUser(ClientUser);
            var dto = new CompleteRequestForInformationDTO
            {
                Response = "Client response to RFI"
            };

            // Act
            await _requestForInformationAppService.CompleteRequestForInformationAsync(_rfiId, dto);
            var updatedRFI = await _requestForInformationRepository.GetByIdAsync(_rfiId);

            // Assert
            updatedRFI.Status.Should().Be(RequestForInformationStatus.Completed);
            updatedRFI.Response.Should().Be(dto.Response);
            updatedRFI.Submission.Status.Should().Be(SubmissionStatus.Submitted);
        }

        [Test]
        public async Task CompleteRequestForInformation_WithInvalidStatus_ShouldThrowException()
        {
            // Arrange
            SetWorkContextUser(ClientUser);
            var rfi = await _requestForInformationRepository.GetByIdAsync(_rfiId);
            rfi.Status = RequestForInformationStatus.Completed;
            await _requestForInformationRepository.SaveChangesAsync();
            var dto = new CompleteRequestForInformationDTO
            {
                Response = "Attempt to respond to already completed RFI"
            };

            // Act
            Func<Task> act = async () => await _requestForInformationAppService.CompleteRequestForInformationAsync(_rfiId, dto);
            
            // Assert
            await act.Should().ThrowAsync<PreconditionFailedException>().WithMessage($"The request for information with id '{_rfiId}' is already completed.");
        }

        private async Task CreateTestSubmissionAsync()
        {
            // Create a test submission
            var submission = new Submission
            {
                Name = "Test Submission",
                Status = SubmissionStatus.Submitted,
                LegalEntityId = Guid.NewGuid(),
                ModuleId = Guid.NewGuid(),
                FinancialYear = 2024,
                StartsAt = new DateTime(2024, 1, 1),
                EndsAt = new DateTime(2024, 12, 31),
                Layout = "TridentTrust", // Required property
                ReportId = Guid.NewGuid().ToString(), // Required property
                LegalEntity = new LegalEntity
                {
                    Name = "Test Company",
                    Code = "TEST",
                    Jurisdiction = new Domain.Jurisdictions.Jurisdiction
                    {
                        Code = JurisdictionCodes.Bahamas,
                        Name = "Bahamas"
                    },
                    MasterClient = _masterClient,
                }
            };

            await _submissionsRepository.InsertAsync(submission);
            await _submissionsRepository.SaveChangesAsync();

            _submissionId = submission.Id;
        }

        private async Task CreateRFIAsync()
        {
            var rfi = new RequestForInformation()
            {
                SubmissionId = _submissionId,
                DeadLine = DateTime.UtcNow.AddDays(7),
                Comments = "Initial RFI comments",
                Status = RequestForInformationStatus.Active,
                CreatedBy = ManagementUser.Id
            };

            rfi = await _requestForInformationRepository.InsertAsync(rfi, true);
            _rfiId = rfi.Id;
        }
    }
}
