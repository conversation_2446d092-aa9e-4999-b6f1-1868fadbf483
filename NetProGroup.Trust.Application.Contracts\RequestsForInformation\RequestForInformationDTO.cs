// <copyright file="RequestForInformationDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.DomainShared.Enums;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represents a request for information entity.
    /// </summary>
    public class RequestForInformationDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the name of the legal entity.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the code of the legal entity.
        /// </summary>
        public string LegalEntityCode { get; set; }

        /// <summary>
        /// Gets or sets the name of the master client.
        /// </summary>
        public string MasterClientName { get; set; }

        /// <summary>
        /// Gets or sets the code of the master client.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the regulation code.
        /// </summary>
        public string JurisdictionCode { get; set; }

        /// <summary>
        /// Gets or sets the Jurisdiction name.
        /// </summary>
        public string JurisdictionName { get; set; }

        /// <summary>
        /// Gets or sets the id of the submission.
        /// </summary>
        public Guid SubmissionId { get; set; }

        /// <summary>
        /// Gets or sets the deadline date for the request for information.
        /// </summary>
        public DateTime DeadLine { get; set; }

        /// <summary>
        /// Gets or sets the comments for the request for information.
        /// </summary>
        [JsonPropertyName("requestComment")]
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the client response for the request for information.
        /// </summary>
        public string Response { get; set; }

        /// <summary>
        /// Gets or sets the comments for the completed action for the request for information.
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the request for information.
        /// </summary>
        public RequestForInformationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the date of the last reminder sent to the user.
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// Gets or sets the date when the request was created.
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        ///  Gets or sets the date/time that the request for information was replied to.
        /// </summary>
        public DateTime? RepliedAt { get; set; }

        /// <summary>
        /// Gets or sets the email of the user who replied to the request for information.
        /// </summary>
        public string RepliedBy { get; set; }

        /// <summary>
        /// Gets or sets the email of the user who completed the request for information.
        /// </summary>
        public string CompletedBy { get; set; }

        /// <summary>
        /// Gets or sets the email of the user who created the request for information.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets the collection with RequestForInformationDocument.
        /// </summary>
        /// <value>A collection of <see cref="SubmissionRFIDocumentDTO"/>.</value>
        public virtual ICollection<SubmissionRFIDocumentDTO> Documents { get; } = new List<SubmissionRFIDocumentDTO>();
    }
}