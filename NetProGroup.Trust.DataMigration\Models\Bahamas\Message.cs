﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a Message entity for data migration purposes.
    /// </summary>
    [BsonIgnoreExtraElements]
    public class Message : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the master client codes associated with the message.
        /// </summary>
        [BsonElement("masterClientCodes")]
        public List<string> MasterClientCodes { get; set; } = new();

        /// <summary>
        /// Gets or sets the subject of the message.
        /// </summary>
        [BsonElement("subject")]
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets the send to all flag indicating whether the message should be sent to all master clients.
        /// </summary>
        [BsonElement("sendToAll")]
        public bool SendToAll { get; set; }

        /// <summary>
        /// Gets or sets the email subject of the message.
        /// </summary>
        [BsonElement("emailSubject")]
        public string EmailSubject { get; set; }

        /// <summary>
        /// Gets or sets the content of the message.
        /// </summary>
        [BsonElement("content")]
        public string Content { get; set; }

        /// <summary>
        /// Gets or sets the status of the message.
        /// </summary>
        [BsonElement("status")]
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the message.
        /// </summary>
        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the schedule date of the message.
        /// </summary>
        [BsonElement("scheduledAt")]
        public DateTime ScheduledAt { get; set; }

        /// <summary>
        /// Gets or sets the update date of the message.
        /// </summary>
        [BsonElement("updatedAt")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets or sets the files associated with the message.
        /// </summary>
        [BsonElement("files")]
        public List<FileSchema> Files { get; set; }

        /// <summary>
        /// Gets or sets the list of URLs associated with the message.
        /// </summary>
        [BsonElement("urls")]
        public List<UrlSchema> Urls { get; set; } = new();
    }
}
