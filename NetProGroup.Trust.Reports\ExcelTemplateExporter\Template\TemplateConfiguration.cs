// <copyright file="TemplateConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter.Template
{
    /// <summary>
    /// Represents the configuration for a template.
    /// </summary>
    public class TemplateConfiguration
    {
        /// <summary>
        /// Gets the number of rows in the template section that should be replicated.
        /// </summary>
        public int TemplateRowCount { get; init; }

        /// <summary>
        /// Gets the number of header rows in the template that should not be replicated.
        /// </summary>
        public int HeaderRowCount { get; init; }

        /// <summary>
        /// Gets the starting row number where the data population should begin.
        /// </summary>
        public int StartingRow { get; init; }
    }
}
