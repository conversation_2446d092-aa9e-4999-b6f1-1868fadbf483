﻿// <copyright file="MessageMigrationServiceWrapper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.DataMigration.Models.Bahamas;

namespace NetProGroup.Trust.DataMigration.Services.Bahamas
{
    /// <summary>
    /// Service for migrating message data.
    /// </summary>
    public class MessageMigrationServiceWrapper : IEntityMigrator
    {
        private readonly ILogger<MessageMigrationServiceWrapper> _logger;
        private readonly EntityMigrationService _entityMigrationService;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IServiceProvider _serviceProvider;
        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageMigrationServiceWrapper"/> class.
        /// </summary>
        /// <param name="logger">The Logger.</param>
        /// <param name="entityMigrationService">The entity migration service.</param>
        /// <param name="jurisdictionsRepository">Instance of the JurisdictionsRepository.</param>
        /// <param name="serviceProvider">The service provider.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public MessageMigrationServiceWrapper(
            ILogger<MessageMigrationServiceWrapper> logger,
            EntityMigrationService entityMigrationService,
            IJurisdictionsRepository jurisdictionsRepository,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _entityMigrationService = entityMigrationService ?? throw new ArgumentNullException(nameof(entityMigrationService));
            _jurisdictionsRepository = jurisdictionsRepository ?? throw new ArgumentNullException(nameof(jurisdictionsRepository));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Migrates messages asynchronously.
        /// </summary>
        /// <param name="migrationRecord">The migration record.</param>
        /// <param name="jobLock">The job lock.</param>
        public async Task MigrateEntitiesAsync(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            ArgumentNullException.ThrowIfNull(jobLock, nameof(jobLock));

            _jurisdiction ??= await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == migrationRecord.Region);
            _logger.LogInformation("Retrieved jurisdiction {JurisdictionName}.", _jurisdiction.Name);

            await _entityMigrationService.MigrateEntityAsync<Message>(
                migrationRecord,
                jobLock,
                MigrationConsts.Messages.CollectionName,
                MigrationConsts.Messages.DisplayName,
                async (message) =>
                {
                    using var scope = _serviceProvider.CreateScope();
                    var messageMigrationService = scope.ServiceProvider.GetRequiredService<MessageMigrationService>();
                    var (success, errors) = await messageMigrationService.HandleMessage(
                        message,
                        _jurisdiction.Id);

                    return (success, errors);
                },
                message => new
                {
                    message.Id,
                    message.EmailSubject,
                    message.Status,
                    MasterClientCodes = string.Join(",", message.MasterClientCodes)
                });
        }
    }
}