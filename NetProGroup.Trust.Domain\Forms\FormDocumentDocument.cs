// <copyright file="FormDocumentDocument.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Framework.Services.EFAuditing;

namespace NetProGroup.Trust.Domain.Forms
{
    /// <summary>
    /// Represents a formDocument document.
    /// </summary>
    public class FormDocumentDocument : StampedEntity<Guid>, IAuditableEntity
    {
        /// <summary>
        /// Gets or sets the id of the form document.
        /// </summary>
        public Guid FormDocumentId { get; set; }

        /// <summary>
        /// Gets or sets the FormDocument.
        /// </summary>
        public virtual FormDocument FormDocument { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the document.
        /// </summary>
        public Guid DocumentId { get; set; }

        /// <summary>
        ///  Gets or sets the document.
        /// </summary>
        public virtual Document Document { get; set; }
    }
}