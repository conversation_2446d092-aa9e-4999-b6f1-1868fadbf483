﻿// <copyright file="SyncStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Enums
{
    /// <summary>
    /// Represents the synchronization status.
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// Indicates that the item is inactive.
        /// </summary>
        Inactive = 0,

        /// <summary>
        /// Indicates that the item is active.
        /// </summary>
        Active = 1

    }
}
