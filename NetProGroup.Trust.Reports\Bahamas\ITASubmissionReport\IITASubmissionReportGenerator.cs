// <copyright file="IITASubmissionReportGenerator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Reports.Bahamas.IRDSubmissionReport
{
    /// <summary>
    /// Interface for the IRD submission report generator.
    /// </summary>
    public interface IITASubmissionReportGenerator : ITransientService
    {
        /// <summary>
        /// Generates a submissions report for the module.
        /// </summary>
        /// <param name="submissions">The list of submissions to be included in the report.</param>
        /// <returns>A <see cref="Task{ReportOutput}"/> representing the asynchronous operation.</returns>
        Task<ReportOutput> GenerateIRDSubmissionReportAsync(List<Submission> submissions);
    }
}