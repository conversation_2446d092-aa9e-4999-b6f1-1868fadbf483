// <copyright file="SubmissionDataReportIncomesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportIncomesRowPopulator : LinePopulatorBase, ISubmissionDataReportIncomesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the other incomes
            var otherIncomes = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherIncomes, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (otherIncomes.Count > 1)
            {
                // Group the other incomes by the index
                var otherIncomeGroups = otherIncomes.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherIncomes + ".")[1].Split(".")[0]);

                foreach (var group in otherIncomeGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the income type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Other incomes");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the income value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }

            // Retrieve the other income earned but not received
            var notReceivedIncomes = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherEndingInterestReceivable, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (notReceivedIncomes.Count > 1)
            {
                // Group the other incomes by the index
                var nonReceivedIncomeGroups = notReceivedIncomes.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherEndingInterestReceivable + ".")[1].Split(".")[0]);

                foreach (var group in nonReceivedIncomeGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the income type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Earned but not received");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the income value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }

            // Retrieve the other prior period income received
            var priorIncomes = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.OtherBeginningInterestreceivable, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (priorIncomes.Count > 1)
            {
                // Group the other incomes by the index
                var priorIncomeGroups = priorIncomes.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.OtherBeginningInterestreceivable + ".")[1].Split(".")[0]);

                foreach (var group in priorIncomeGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the income type
                    SetCellValueAndStyle(worksheet, currentRow, 4, "Prior income received");

                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the income value
                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Amount, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }
        }
    }
}