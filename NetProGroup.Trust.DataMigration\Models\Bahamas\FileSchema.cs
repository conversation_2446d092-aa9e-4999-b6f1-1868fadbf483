using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents file information for uploaded documents.
    /// </summary>
    [BsonIgnoreExtraElements]
    public sealed class FileSchema : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the file id.
        /// </summary>
        [BsonElement("fileId")]
        public string FileId { get; set; }

        /// <summary>
        /// Gets or sets the fieldname.
        /// </summary>
        [BsonElement("fieldname")]
        public string Fieldname { get; set; }

        /// <summary>
        /// Gets or sets the blob.
        /// </summary>
        [BsonElement("blob")]
        public string Blob { get; set; }

        /// <summary>
        /// Gets or sets the blobName.
        /// </summary>
        [BsonElement("blobName")]
        public string BlobName { get; set; }

        /// <summary>
        /// Gets or sets the url.
        /// </summary>
        [BsonElement("url")]
#pragma warning disable CA1056 // URI-like properties should not be strings
        public string Url { get; set; }
#pragma warning restore CA1056 // URI-like properties should not be strings

        /// <summary>
        /// Gets or sets the originalname.
        /// </summary>
        [BsonElement("originalname")]
        public string Originalname { get; set; }

        /// <summary>
        /// Gets or sets the encoding.
        /// </summary>
        [BsonElement("encoding")]
        public string Encoding { get; set; }

        /// <summary>
        /// Gets or sets the container.
        /// </summary>
        [BsonElement("container")]
        public string Container { get; set; }

        /// <summary>
        /// Gets or sets the blobType.
        /// </summary>
        [BsonElement("blobType")]
        public string BlobType { get; set; }

        /// <summary>
        /// Gets or sets the size.
        /// </summary>
        [BsonElement("size")]
        public string Size { get; set; }

        /// <summary>
        /// Gets or sets the etag.
        /// </summary>
        [BsonElement("etag")]
        public string Etag { get; set; }
    }
}
