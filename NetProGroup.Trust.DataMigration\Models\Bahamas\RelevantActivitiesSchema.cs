using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents all relevant activities for the entity
    /// </summary>
    [BsonIgnoreExtraElements]
    public class RelevantActivitiesSchema
    {
        /// <summary>
        /// Banking business activity
        /// </summary>
        [BsonElement("banking_business")]
        public RelevantActivitySchema BankingBusiness { get; set; }

        /// <summary>
        /// Insurance business activity
        /// </summary>
        [BsonElement("insurance_business")]
        public RelevantActivitySchema InsuranceBusiness { get; set; }

        /// <summary>
        /// Fund management business activity
        /// </summary>
        [BsonElement("fund_management_business")]
        public RelevantActivitySchema FundManagementBusiness { get; set; }

        /// <summary>
        /// Finance and leasing business activity
        /// </summary>
        [BsonElement("finance_leasing_business")]
        public RelevantActivitySchema FinanceLeasingBusiness { get; set; }

        /// <summary>
        /// Headquarters business activity
        /// </summary>
        [BsonElement("headquarters_business")]
        public RelevantActivitySchema HeadquartersBusiness { get; set; }

        /// <summary>
        /// Shipping business activity
        /// </summary>
        [BsonElement("shipping_business")]
        public RelevantActivitySchema ShippingBusiness { get; set; }

        /// <summary>
        /// Holding company business activity
        /// </summary>
        [BsonElement("holding_business")]
        public RelevantActivitySchema HoldingBusiness { get; set; }

        /// <summary>
        /// Intellectual property business activity
        /// </summary>
        [BsonElement("intellectual_property_business")]
        public RelevantActivitySchema IntellectualPropertyBusiness { get; set; }

        /// <summary>
        /// Service centre business activity
        /// </summary>
        [BsonElement("service_centre_business")]
        public RelevantActivitySchema ServiceCentreBusiness { get; set; }

        /// <summary>
        /// No relevant activities selected
        /// </summary>
        [BsonElement("none")]
        public RelevantActivitySchema None { get; set; }

        /// <summary>
        /// Remarks when no activities are selected
        /// </summary>
        [BsonElement("none_remarks")]
        public string NoneRemarks { get; set; }

        /// <summary>
        /// Evidence files when no activities are selected
        /// </summary>
        [BsonElement("evidence_none_activities")]
        public List<FileSchema> EvidenceNoneActivities { get; set; }
    }
}