﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Represents a Master Client Message entity for data migration purposes.
    /// </summary>
    [BsonIgnoreExtraElements]
    public class MasterClientMessage : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the reference to the associated message ID.
        /// </summary>
        [BsonElement("messageId")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string MessageId { get; set; }

        /// <summary>
        /// Gets or sets the master client code that received the message.
        /// </summary>
        [BsonElement("masterClientCode")]
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the creation date of this delivery record.
        /// </summary>
        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last update date of this delivery record.
        /// </summary>
        [BsonElement("updatedAt")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets or sets the date when the message was sent to the client.
        /// </summary>
        [BsonElement("sentAt")]
        public DateTime? SentAt { get; set; }

        /// <summary>
        /// Gets or sets the current status of the message for this client.
        /// </summary>
        [BsonElement("status")]
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the date when the message was opened by the client.
        /// </summary>
        [BsonElement("openedAt")]
        public DateTime? OpenedAt { get; set; }

        /// <summary>
        /// Gets or sets the user who opened the message (email).
        /// </summary>
        [BsonElement("openedBy")]
        public string OpenedBy { get; set; }
    }
}
