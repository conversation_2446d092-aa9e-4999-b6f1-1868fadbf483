namespace NetProGroup.Trust.DataMigration.DTO
{
    /// <summary>
    /// Represents a data migration entity with various properties to track the migration process.
    /// </summary>
    public class DataMigrationDTO
    {
        /// <summary>
        /// Defines the possible statuses for a data migration.
        /// </summary>
        public enum MigrationStatus
        {
            /// <summary>
            /// The migration has not been started yet.
            /// </summary>
            NotStarted = 0,
            /// <summary>
            /// The migration is currently in progress.
            /// </summary>
            InProgress = 1,
            /// <summary>
            /// The migration has been completed successfully.
            /// </summary>
            Completed = 2,
            /// <summary>
            /// The migration has failed due to an error.
            /// </summary>
            Failed = 3,
            /// <summary>
            /// The migration has been cancelled by the user.
            /// </summary>
            Cancelled = 4
        }

        /// <summary>
        /// Gets or sets the region for the data migration.
        /// </summary>
        public string Region { get; init; }


        /// <summary>
        /// Gets or sets the date and time when the migration was last updated.
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the initial sync has been completed.
        /// </summary>
        public bool InitialSyncCompleted { get; set; }

        /// <summary>
        /// Gets a value indicating whether the migration has been completed.
        /// </summary>
        public bool MigrationCompleted { get; set; }

        /// <summary>
        /// Gets the current status of the migration.
        /// </summary>
        public MigrationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a stop has been requested for the migration.
        /// </summary>
        public bool StopRequested { get; set; }

        /// <summary>
        /// Gets or sets a string representation of unprocessed records.
        /// </summary>
        public List<UnprocessedRecordDTO> UnprocessedRecords { get; set; }

        /// <summary>
        /// Gets or sets the collection of entity migration progresses.
        /// </summary>
        public ICollection<EntityMigrationProgressDTO> EntityMigrationProgresses { get; set; } = new List<EntityMigrationProgressDTO>();

        /// <summary>
        /// Gets the error message if the migration failed.
        /// </summary>
        public string Error { get; set; }
    }
}
