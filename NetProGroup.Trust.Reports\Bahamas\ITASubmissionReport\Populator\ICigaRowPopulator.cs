// <copyright file="ICigaRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Interface for the IRD submission report ciga row populator.
    /// </summary>
    public interface ICigaRowPopulator : ITemplateRowPopulator<Submission>, ITransientService
    {
    }
}