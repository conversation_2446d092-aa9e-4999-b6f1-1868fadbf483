// <copyright file="InvoiceConfigurationsMigrationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.DataMigration.Models.Bahamas;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for migrating Nevis invoice configurations.
    /// </summary>
    public class InvoiceConfigurationsMigrationService : IEntityMigrator
    {
        private const string UsdCurrencyCode = "USD";
        private const string InvoiceNumberPrefixFormat = "{yy}{mm}";
        private const string InvoiceNumberRangeFormat = "{yy}{mm}";
        private const string FullInvoiceNumberFormat = "{prefix}/{number}";

        private readonly ILogger<InvoiceConfigurationsMigrationService> _logger;
        private readonly EntityMigrationService _entityMigrationService;
        private readonly IDataMigrationsDataManager _dataMigrationsDataManager;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ISettingsManager _settingsManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceConfigurationsMigrationService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="entityMigrationService">Instance of the EntityMigrationService.</param>
        /// <param name="dataMigrationsDataManager">Instance of the DataMigrationsDataManager.</param>
        /// <param name="jurisdictionsRepository">Instance of the JurisdictionsRepository.</param>
        /// <param name="settingsManager">Instance of the SettingsManager.</param>
        public InvoiceConfigurationsMigrationService(
            ILogger<InvoiceConfigurationsMigrationService> logger,
            EntityMigrationService entityMigrationService,
            IDataMigrationsDataManager dataMigrationsDataManager,
            IJurisdictionsRepository jurisdictionsRepository,
            ISettingsManager settingsManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _entityMigrationService = entityMigrationService ?? throw new ArgumentNullException(nameof(entityMigrationService));
            _dataMigrationsDataManager = dataMigrationsDataManager ?? throw new ArgumentNullException(nameof(dataMigrationsDataManager));
            _jurisdictionsRepository = jurisdictionsRepository ?? throw new ArgumentNullException(nameof(jurisdictionsRepository));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
        }

        /// <summary>
        /// Migrates Nevis invoice configurations.
        /// </summary>
        /// <param name="migrationRecord">The migration record for the process.</param>
        /// <param name="jobLock">The job lock.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task MigrateEntitiesAsync(Domain.DataMigrations.DataMigration migrationRecord,
            LockDTO jobLock)
        {
            _logger.LogInformation("Starting migration of Nevis invoice configurations");
            var unprocessedRecords = new List<UnprocessedRecord>();

            await _entityMigrationService.MigrateEntityAsync<NevisInvoiceConfiguration>(migrationRecord, jobLock,
                MigrationConsts.NevisInvoiceConfigurations.CollectionName, MigrationConsts.NevisInvoiceConfigurations.DisplayName, async (configuration) =>
                {
                    await HandleNevisInvoiceConfiguration(configuration);

                    return (true, new List<string>());
                }, configuration => new
                {
                    configuration.Id,
                    configuration.InitialRangeNumber,
                    configuration.CurrentAvailableNumber
                });

            _logger.LogInformation("Finished processing Nevis invoice configurations. Storing unprocessed records.");
            await _dataMigrationsDataManager.StoreUnprocessedRecordsAsync(migrationRecord, unprocessedRecords);
            _logger.LogInformation("Migration of Nevis invoice configurations completed");
        }

        private async Task HandleNevisInvoiceConfiguration(NevisInvoiceConfiguration configuration)
        {
            _logger.LogDebug("Handling Nevis invoice configuration: {ConfigurationId}", configuration.Id);

            var jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Nevis);

            if (jurisdiction == null)
            {
                _logger.LogError("Nevis jurisdiction not found");
                throw new InvalidOperationException("Nevis jurisdiction not found");
            }

            await UpdateInvoiceNumberingSettings(jurisdiction, configuration);
            await MapLatePayments(configuration.LatePayments, jurisdiction);
        }

        private async Task UpdateInvoiceNumberingSettings(Jurisdiction jurisdiction, NevisInvoiceConfiguration configuration)
        {
            // Get existing invoice settings for jurisdiction
            var invoiceNumberingSettings = await _settingsManager.GetInvoiceNumberingSettingsAsync(jurisdiction.Id, null);

            invoiceNumberingSettings.InitialNumber = configuration.InitialRangeNumber;
            invoiceNumberingSettings.PrefixFormat = InvoiceNumberPrefixFormat;
            invoiceNumberingSettings.RangeFormat = InvoiceNumberRangeFormat;
            invoiceNumberingSettings.FullInvoiceNumberFormat = FullInvoiceNumberFormat;

            _logger.LogDebug("Saving invoice numbering settings for jurisdiction: {JurisdictionId}", jurisdiction.Id);
            await _settingsManager.SaveInvoiceNumberingSettingsAsync(jurisdiction.Id, null, invoiceNumberingSettings);
        }

        private async Task MapLatePayments(List<LatePaymentFeeConfiguration> latePayments, Jurisdiction jurisdiction)
        {
            _logger.LogDebug("Mapping late payments for jurisdiction: {JurisdictionId}", jurisdiction.Id);

            try
            {
                foreach (var latePayment in latePayments)
                {
                    // Get existing settings
                    var settings = await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(jurisdiction.Id, latePayment.Year);

                    var matchingSetting = settings.FirstOrDefault(fee => fee.StartAt == latePayment.LatePeriodStart) ?? new STRLatePaymentFeeDTO();

                    matchingSetting.Description = latePayment.Description;
                    matchingSetting.Amount = latePayment.Fee;
                    matchingSetting.CurrencyCode = UsdCurrencyCode;
                    matchingSetting.StartAt = latePayment.LatePeriodStart;
                    matchingSetting.EndAt = latePayment.LatePeriodEnd;
                    matchingSetting.FinancialYear = latePayment.Year;
                    matchingSetting.InvoiceText = "Late filing fee";
                    matchingSetting.Charge = latePayment.LatePaymentCharge ?? true;

                    _logger.LogDebug("Saving late payment fee settings for jurisdiction: {JurisdictionId} for year {Year}", jurisdiction.Id, latePayment.Year);

                    await _settingsManager.SaveSettingsForJurisdictionAsync(matchingSetting, jurisdiction.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error mapping late payments for jurisdiction: {JurisdictionId}", jurisdiction.Id);
                throw;
            }

            _logger.LogDebug("Finished mapping late payments for jurisdiction: {JurisdictionId}", jurisdiction.Id);
        }
    }
}
