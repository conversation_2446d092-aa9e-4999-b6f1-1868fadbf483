﻿using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Reports
{
    internal static class CountryCodeMapper
    {
        /// <summary>
        /// Attempts to get the country name for a given ISO 3166-1 alpha-3 code.
        /// </summary>
        /// <param name="alpha3Code">The ISO 3166-1 alpha-3 code.</param>
        /// <returns>The country name.</returns>
        public static string GetCountryName(string alpha3Code)
        {
            if (string.IsNullOrEmpty(alpha3Code))
            {
                return alpha3Code;
            }

            var countryName = CountryConsts.CountryAlpha3Codes.FirstOrDefault(x => x.Value == alpha3Code).Key;

            if (!String.IsNullOrEmpty(countryName))
            {
                return countryName;
            }

            throw new NotSupportedException($"Alpha 3 code {alpha3Code} not supported");
        }
    }
}
