// <copyright file="TemplateManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter.Template
{
    /// <summary>
    /// Manages the template for the Excel workbook.
    /// </summary>
    public class TemplateManager
    {
        private readonly IXLWorksheet _worksheet;
        private readonly TemplateConfiguration _config;

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplateManager"/> class.
        /// </summary>
        /// <param name="worksheet">The worksheet to use.</param>
        /// <param name="config">The configuration for the template.</param>
        public TemplateManager(IXLWorksheet worksheet, TemplateConfiguration config)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(config, nameof(config));

            _worksheet = worksheet;
            _config = config;
            CurrentRow = config.StartingRow;
            HeaderRowCount = config.HeaderRowCount;
        }

        /// <summary>
        /// Gets the current row being processed in the template.
        /// </summary>
        public int CurrentRow { get; private set; }

        /// <summary>
        /// Gets or sets the number of header rows in the template.
        /// </summary>
        public int HeaderRowCount { get; set; }

        /// <summary>
        /// Replicates the template rows for the next data item.
        /// </summary>
        public void ReplicateTemplate()
        {
            for (int i = HeaderRowCount + 1; i <= _config.TemplateRowCount; i++)
            {
                CopyRow(i, CurrentRow + (i - 1));
            }
        }

        /// <summary>
        /// Moves to the next section in the template.
        /// </summary>
        public void MoveToNextSection()
        {
            CurrentRow += _config.TemplateRowCount;
        }

        private static void CopyCell(IXLCell source, IXLCell target)
        {
            target.Value = source.Value;
            target.Style = source.Style;
        }

        private void CopyRow(int sourceRowIndex, int targetRowIndex)
        {
            var sourceRow = _worksheet.Row(sourceRowIndex);
            var targetRow = _worksheet.Row(targetRowIndex);
            var lastColumn = sourceRow.LastCellUsed()?.Address.ColumnNumber ?? 1;

            // First, copy cells and styles
            for (int col = 1; col <= lastColumn; col++)
            {
                CopyCell(sourceRow.Cell(col), targetRow.Cell(col));
            }

            // Then copy any merges
            CopyMerges(sourceRowIndex, targetRowIndex, lastColumn);
        }

        private void CopyMerges(int sourceRowIndex, int targetRowIndex, int lastColumn)
        {
            for (int col = 1; col <= lastColumn; col++)
            {
                var sourceCell = _worksheet.Cell(sourceRowIndex, col);
                if (sourceCell.IsMerged())
                {
                    var mergedRange = sourceCell.MergedRange();
                    var startCol = mergedRange.FirstColumn().ColumnNumber();
                    var endCol = mergedRange.LastColumn().ColumnNumber();

                    // Only process the merge once (from its first cell)
                    if (col == startCol)
                    {
                        _worksheet.Range(targetRowIndex, startCol, targetRowIndex, endCol).Merge();
                    }
                }
            }
        }
    }
}
