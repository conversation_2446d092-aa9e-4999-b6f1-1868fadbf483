﻿// <copyright file="BODirectorOfficerTypeCode.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Shared.Defines.BODirector
{
    /// <summary>
    /// Beneficial owner and director officer type codes for different jurisdictions.
    /// </summary>
    public static class BODirectorOfficerTypeCode
    {
        /// <summary>
        /// BVI officer type code VGTP01.
        /// </summary>
        public const string VGTP01 = LegalEntityOfficerTypes.VGTP01;

        /// <summary>
        /// BVI officer type code VGTP02.
        /// </summary>
        public const string VGTP02 = LegalEntityOfficerTypes.VGTP02;

        /// <summary>
        /// BVI officer type code VGTP03.
        /// </summary>
        public const string VGTP03 = LegalEntityOfficerTypes.VGTP03;

        /// <summary>
        /// BVI officer type code VGTP04.
        /// </summary>
        public const string VGTP04 = LegalEntityOfficerTypes.VGTP04;

        /// <summary>
        /// BVI officer type code VGTP05.
        /// </summary>
        public const string VGTP05 = LegalEntityOfficerTypes.VGTP05;

        /// <summary>
        /// BVI officer type code VGTP06.
        /// </summary>
        public const string VGTP06 = LegalEntityOfficerTypes.VGTP06;

        /// <summary>
        /// Nevis officer type code KNTP01.
        /// </summary>
        public const string KNTP01 = LegalEntityOfficerTypes.KNTP01;

        /// <summary>
        /// Nevis officer type code KNTP02.
        /// </summary>
        public const string KNTP02 = LegalEntityOfficerTypes.KNTP02;

        /// <summary>
        /// Nevis officer type code KNTP03.
        /// </summary>
        public const string KNTP03 = LegalEntityOfficerTypes.KNTP03;

        /// <summary>
        /// Nevis officer type code KNTP04.
        /// </summary>
        public const string KNTP04 = LegalEntityOfficerTypes.KNTP04;

        /// <summary>
        /// Nevis officer type code KNTP05.
        /// </summary>
        public const string KNTP05 = LegalEntityOfficerTypes.KNTP05;

        /// <summary>
        /// Nevis officer type code KNTP06.
        /// </summary>
        public const string KNTP06 = LegalEntityOfficerTypes.KNTP06;
    }
}
