// <copyright file="FeatureAvailability.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using NetProGroup.Trust.Domain.FeatureFlags.Enum;
using NetProGroup.Trust.Application.Contracts.FeatureFlags;
using NetProGroup.Trust.Application.Announcements;

namespace NetProGroup.Trust.Application.AppServices.FeatureFlags
{
    /// <inheritdoc/>
    public class FeatureAvailability : IFeatureAvailability
    {
        /// <summary>
        /// A thread-safe dictionary to store feature flags and their enabled status.
        /// </summary>
        private ConcurrentDictionary<FeatureFlag, bool> _flags;

        /// <summary>
        /// Initializes a new instance of the <see cref="FeatureAvailability"/> class with the provided feature flags.
        /// </summary>
        /// <param name="flags">Feature flags from app settings.</param>
        public FeatureAvailability(IOptions<FeatureFlagSettings> flags)
        {
            if (flags?.Value == null)
            {
                throw new ArgumentNullException(nameof(flags), "The flags parameter cannot be null.");
            }

            _flags = new ConcurrentDictionary<FeatureFlag, bool>(flags.Value.ToDictionary(kvp => kvp.Key, kvp => kvp.Value));
        }

        /// <inheritdoc/>
        public bool IsFeatureEnabled(FeatureFlag flag)
            => _flags.TryGetValue(flag, out var enabled) && enabled;
    }

}