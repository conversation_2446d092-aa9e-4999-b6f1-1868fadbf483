--=================================================================================================================
--    Author              Date            Description
 --   Sjuglenn  Martis    2025-07-23      Updates [FormDocumentAttributes] table by converting datetime-like strings
--                                        from various formats into ISO 8601 UTC format: 'yyyy-MM-ddTHH:mm:ssZ'
--================================================================================================================= 
DECLARE @id uniqueidentifier;
DECLARE @key NVARCHAR(255);
DECLARE @newValue VARCHAR(20);
DECLARE @format VARCHAR(20) = 'yyyy-MM-ddTHH:mm:ss';

--#1. Temp Table Creation:
--======================================================================================================
--Creates a temp table #FormDocumentAttributePreview containing all [FormDocumentAttributes] rows where [Value] is a valid datetime string (excluding digit-only and empty values).
--The temp table stores the original value and the new formatted value. 
--======================================================================================================

-- Drop Temp Table FormDocumentAttributePreview If It Exists
IF OBJECT_ID('tempdb..#FormDocumentAttributePreview') IS NOT NULL
    DROP TABLE #FormDocumentAttributePreview;

-- Copy Potential Update Values Into Temp Table Given Original And New Formatted values
SELECT [Id] AS ID, [FormDocumentId], [Key],  [Value] AS [OriginalFormat],
       FORMAT( TRY_CONVERT(datetime, REPLACE([Value], 'Z', '')), @format ) + 'Z' AS [NewFormat]
INTO #FormDocumentAttributePreview
FROM [dbo].[FormDocumentAttributes]
WHERE TRY_CONVERT(datetime, REPLACE([Value], 'Z', '')) IS NOT NULL
  AND [Value] LIKE '%[^0-9]%' -- Excludes digit-only values
  AND [Value] != ''; -- Excludes empty values

--#2. Update Attribute Values:
--======================================================================================================
--Updates [Value] in [FormDocumentAttributes] to the new ISO 8601 format using the temp table.
--======================================================================================================

UPDATE [fda]
SET [Value] = [temp].[NewFormat]
FROM [dbo].[FormDocumentAttributes] [fda]
JOIN #FormDocumentAttributePreview [temp]
  ON [fda].[Id] = [temp].[Id]; 

--#4. Update JSON Columns:
--======================================================================================================
--Uses a cursor to iterate over each affected attribute.
--For each, it updates the corresponding key in the DataAsJson column of [FormDocumentRevisions] (for the same FormDocumentId), using JSON_MODIFY to set the new formatted value.
-- Example: Replace the first occurrence of 'yyyy-MM-dd HH:mm:ss' with 'yyyy-MM-ddTHH:mm:ssZ'
--=====================================================================================================

-- Cursor over valid rows
DECLARE cur CURSOR FOR
SELECT DISTINCT [FormDocumentId], [Key], [NewFormat]
FROM #FormDocumentAttributePreview
WHERE ISNULL([Key], '') <> '' AND ISNULL([NewFormat], '') <> '';

OPEN cur;
FETCH NEXT FROM cur INTO @id, @key, @newValue;

WHILE @@FETCH_STATUS = 0
BEGIN

    UPDATE [revisions] 
        SET DataAsJson = JSON_MODIFY(
            DataAsJson,
            '$.form.dataSet."'+ @key +'"',
            @newValue
        )
     FROM FormDocumentRevisions [revisions]
    WHERE FormDocumentId = @id
    PRINT @id
    PRINT @key + ' - ' + @newValue;

    FETCH NEXT FROM cur INTO  @id, @key, @newValue;
END

CLOSE cur;
DEALLOCATE cur;

--#5. Safety Revert (Commented):
--=====================================================================================================
--Provides a way to revert changes using the temp table, restoring original values if needed.
--Only run this if temp table exists and we need to revert the data.
/*
--#4. Preview orginal, new value.
SELECT [fda].[FormDocumentId], [fda].[Key], [temp].[OriginalFormat], [fda].[Value] AS [NewValue] FROM FormDocumentAttributes [fda]
INNER JOIN #FormDocumentAttributePreview [temp] ON [temp].[Id] = [fda].[Id]; 


--#1. Revert FormDocumentAttributes table back 
IF OBJECT_ID('tempdb..#FormDocumentAttributePreview') IS NOT NULL
BEGIN
    UPDATE fda
    SET [Value] = p.Original
    FROM [dbo].[FormDocumentAttributes] fda
    JOIN #FormDocumentAttributePreview p
      ON [temp].[Id] = [fda].[Id]
END

 --#2. Preview table
SELECT [Id], [Key], [Value] AS [Original],
       FORMAT( TRY_CONVERT(datetime, REPLACE([Value], 'Z', '')), 'yyyy-MM-ddTHH:mm:ss' ) + 'Z' AS [NewFormat]
FROM [dbo].[FormDocumentAttributes]
WHERE TRY_CONVERT(datetime, REPLACE([Value], 'Z', '')) IS NOT NULL
  AND [Value] LIKE '%[^0-9]%' -- Excludes digit-only values
  AND [Value] != ''; -- Excludes empty values

  --#3. Preview Temp Table
  SELECT * FROM #FormDocumentAttributePreview
*/
