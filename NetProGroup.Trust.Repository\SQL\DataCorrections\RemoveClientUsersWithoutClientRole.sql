-- Delete all users which were synced from ViewPoint but were not assigned the Client role,
-- which causes them to not be linked to their master clients in the second step of the sync.
delete from NetPro.Users where Id in 
(select u.id from Staging_pcp_MasterClients s
join netpro.users u on s.UserEmailAddress = u.Email
left join netpro.userroles ur on ur.userid = u.id
left join netpro.roles r on r.id = ur.roleid
where u.ObjectId is null -- meaning it is an external id user
and ur.roleid is null
)
GO

-- Trigger a new sync to re-sync the deleted users, correctly this time.
UPDATE ScheduledJobs SET [Trigger] = 1 WHERE [Key] = 'sync.viewpoint'
GO