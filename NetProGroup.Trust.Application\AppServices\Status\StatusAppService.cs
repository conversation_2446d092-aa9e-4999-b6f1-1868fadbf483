// <copyright file="StatusAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Status;
using NetProGroup.Trust.Application.Contracts.Status.Models;
using NetProGroup.Trust.Application.Scheduler.Jobs.Sync;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Permissions;

namespace NetProGroup.Trust.Application.AppServices.Status
{
    /// <summary>
    /// Implementation of status application service.
    /// </summary>
    public class StatusAppService : IStatusAppService
    {
        private readonly IScheduledJobsRepository _scheduledJobsRepository;
        private readonly ISecurityManager _securityManager;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="StatusAppService"/> class.
        /// </summary>
        /// <param name="scheduledJobsRepository">The scheduled jobs repository.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="mapper">The mapper.</param>
        public StatusAppService(
            IScheduledJobsRepository scheduledJobsRepository,
            ISecurityManager securityManager,
            IMapper mapper)
        {
            _scheduledJobsRepository = scheduledJobsRepository;
            _securityManager = securityManager;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<ViewPointSyncStatusDTO> GetViewPointSyncStatusAsync()
        {
            await this._securityManager.RequireManagementPermissionAsync(
                WellKnownPermissionNames.General_Status_Page_Sync);

            var job = await _scheduledJobsRepository.GetByIdAsync(new Guid(ScheduledJobConsts.ViewPointSyncJobId));
            if (job == null)
            {
                throw new NotFoundException("ViewPoint sync job not found");
            }

            var syncData = job.GetData<ViewPointSyncJobData>() ?? new ViewPointSyncJobData();

            return _mapper.Map<ViewPointSyncStatusDTO>(syncData);
        }
    }
}