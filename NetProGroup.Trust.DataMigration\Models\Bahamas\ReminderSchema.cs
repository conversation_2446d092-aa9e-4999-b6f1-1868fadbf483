﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Requested information reminder scherma.
    /// </summary>
    [BsonIgnoreExtraElements]
    public sealed class ReminderSchema : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        [BsonElement("description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the reminder_date.
        /// </summary>
        [BsonElement("reminder_date")]
        public DateTime? ReminderDate { get; set; }

        /// <summary>
        /// Gets or sets the comment.
        /// </summary>
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("message_id")]
        public string MessageId { get; set; }
    }
}
