// <copyright file="SimplifiedTaxReturnRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.Nevis.SimplifiedTaxReturn.Populators
{
    /// <summary>
    /// Populate a row for the simplified tax return module report.
    /// </summary>
    public class SimplifiedTaxReturnRowPopulator : LinePopulatorBase, ISimplifiedTaxReturnRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, SubmissionNevisReportDTO data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Set the value if the submission is using the reporting tool
            SetCellValueAndStyle(worksheet, currentRow, 1, data.CreatedByEmail);

            // Company Name
            SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntityName);

            // Company Entity number
            SetCellValueAndStyle(worksheet, currentRow, 3, string.IsNullOrEmpty(data.LegalEntityLegacyCode) ? data.LegalEntityCode : data.LegalEntityLegacyCode);

            // Master client code
            SetCellValueAndStyle(worksheet, currentRow, 4, data.LegalEntityMasterClientCode);

            // Company number
            SetCellValueAndStyle(worksheet, currentRow, 5, data.LegalEntityIncorporationNr);

            // Set the VP number
            SetCellValueAndStyle(worksheet, currentRow, 6, data.LegalEntityCode);

            // Set the submission status
            SetCellValueAndStyle(worksheet, currentRow, 7, data.IsPaid ? "PAID" : data.Status.ToString());

            // Set the creation date
            SetCellValueAndStyle(worksheet, currentRow, 8, FormatDateAsLocalTime(data.CreatedAt, data.LegalEntityJurisdictionCode));

            // Set the submitted date
            SetCellValueAndStyle(worksheet, currentRow, 9, FormatDateAsLocalTime(data.SubmittedAt, data.LegalEntityJurisdictionCode));

            if (data.PaymentDate.HasValue)
            {
                // Set the payment date
                SetCellValueAndStyle(worksheet, currentRow, 10, FormatDateAsLocalTime(data.PaymentDate, data.LegalEntityJurisdictionCode));

                // Set the payment reference
                SetCellValueAndStyle(worksheet, currentRow, 11, data.PaymentReference);
            }

            // Set the submission financial year
            SetCellValueAndStyle(worksheet, currentRow, 12, data.FinancialYear);

            // Set the submission referral office
            SetCellValueAndStyle(worksheet, currentRow, 13, data.LegalEntityReferralOffice);
        }
    }
}
