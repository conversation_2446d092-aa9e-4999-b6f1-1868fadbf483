﻿using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Bahamas
{
    /// <summary>
    /// Financial period schema.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class FinancialPeriodSchema
    {
        /// <summary>
        /// Gets or sets the date and time when the financial period begins.
        /// </summary>
        [BsonElement("financial_period_begins")]
        public DateTime FinancialPeriodBegins { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the financial period ends.
        /// </summary>
        [BsonElement("financial_period_ends")]
        public DateTime FinancialPeriodEnds { get; set; }
    }
}
