﻿// <copyright file="WellKnownRFIPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the RFI module.
    /// </summary>
    public static partial class WellKnownPermissionNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore
        /// <summary>
        /// Request for information view.
        /// </summary>
        public const string RFI_Module_View = RFIModule + ".view";

        /// <summary>
        /// Request for information complete.
        /// </summary>
        public const string RFI_Complete = RFIModule + ".complete";

        /// <summary>
        /// Request for information start.
        /// </summary>
        public const string RFI_Start = RFIModule + ".start";

        /// <summary>
        /// Request for information cancel.
        /// </summary>
        public const string RFI_Cancel = RFIModule + ".cancel";

        /// <summary>
        /// Request for information module.
        /// </summary>
        private const string RFIModule = "rfi";

#pragma warning restore SA1310 // Field names should not contain underscore
    }
}
