// <copyright file="SubmissionDataReportAssetsRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Panama.SubmissionDataReport.Populators
{
    /// <summary>
    /// Populate a row for the submission data report.
    /// </summary>
    public class SubmissionDataReportAssetsRowPopulator : LinePopulatorBase, ISubmissionDataReportAssetsRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the transfer properties
            var assets = data.FormDocument.Attributes.Where(a =>
                a.Key.Contains(WellKnownFormDocumentAttibuteKeys.FixedAssets, StringComparison.InvariantCultureIgnoreCase)).ToList();

            if (assets.Count > 1)
            {
                // Group the properties by the index
                var assetGroups = assets.GroupBy(p => p.Key.Split(WellKnownFormDocumentAttibuteKeys.FixedAssets + ".")[1].Split(".")[0]);

                foreach (var group in assetGroups)
                {
                    // Set the submission id
                    SetCellValueAndStyle(worksheet, currentRow, 1, data.Id);

                    // Master client code
                    SetCellValueAndStyle(worksheet, currentRow, 2, data.LegalEntity.MasterClient.Code);

                    // Company Entity number
                    SetCellValueAndStyle(worksheet, currentRow, 3, data.LegalEntity.Code);

                    // Set the asset description
                    SetCellValueAndStyle(worksheet, currentRow, 4, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.Description, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the asset year and cost
                    SetCellValueAndStyle(worksheet, currentRow, 5, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.PurchaseYear, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    SetCellValueAndStyle(worksheet, currentRow, 6, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.PurchaseCost, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    // Set the asset value
                    SetCellValueAndStyle(worksheet, currentRow, 7, group.First(p => p.Key.Contains(WellKnownFormDocumentAttibuteKeys.AssessedValue, StringComparison.InvariantCultureIgnoreCase))?.Value);

                    currentRow += 1;
                }
            }
        }
    }
}