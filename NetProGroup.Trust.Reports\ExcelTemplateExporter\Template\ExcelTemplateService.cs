// <copyright file="ExcelTemplateService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter.Template
{
    /// <summary>
    /// Excel template service implementation.
    /// </summary>
    /// <typeparam name="T">The Type of the data.</typeparam>
    public class ExcelTemplateService<T> : IExcelTemplateService<T>
    {
        private readonly Dictionary<int, ITemplateRowPopulator<T>> _rowPopulators;

        /// <summary>
        /// Initializes a new instance of the <see cref="ExcelTemplateService{T}"/> class.
        /// </summary>
        public ExcelTemplateService()
        {
            _rowPopulators = [];
        }

        /// <summary>
        /// Registers a row populator for a specific line number.
        /// </summary>
        /// <param name="populator">The populator to register.</param>
        public void RegisterRowPopulator(ITemplateRowPopulator<T> populator)
        {
            _rowPopulators[_rowPopulators.Count] = populator;
        }

        /// <inheritdoc />
        public void ClearRowPopulator()
        {
            _rowPopulators.Clear();
        }

        /// <summary>
        /// Applies the template to the workbook.
        /// </summary>
        /// <param name="workbook">The workbook to apply the template to.</param>
        /// <param name="data">The data to apply the template to.</param>
        /// <param name="config">The configuration for the template.</param>
        /// <param name="worksheetIndex">The index of the worksheet to apply the template to.</param>
        public void ApplyTemplate(XLWorkbook workbook, IEnumerable<T> data, TemplateConfiguration config, int worksheetIndex = 0)
        {
            ArgumentNullException.ThrowIfNull(workbook, nameof(workbook));
            ArgumentNullException.ThrowIfNull(data, nameof(data));
            ArgumentNullException.ThrowIfNull(config, nameof(config));

            var worksheet = workbook.Worksheet(worksheetIndex + 1);
            var templateManager = new TemplateManager(worksheet, config);
            var dataProcessor = new DataProcessor<T>(_rowPopulators);

            var dataAsList = data.ToList();

            var dataCount = dataAsList.Count;
            for (int i = 0; i < dataCount; i++)
            {
                // Only replicate if this is not the last item
                if (i + 1 < dataCount)
                {
                    // First replicate the blank template, for the next data item
                    templateManager.ReplicateTemplate();
                }

                // Process the current data item
                dataProcessor.ProcessData(worksheet, templateManager.CurrentRow, dataAsList.ElementAt(i));

                // Move to the start of the replicated template
                templateManager.MoveToNextSection();
            }
        }
    }
}
