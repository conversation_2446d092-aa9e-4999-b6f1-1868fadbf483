// <copyright file="ContactsInfoData.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Identity.EFModels;

namespace NetProGroup.Trust.Reports.Nevis.ContactsInfo
{
    /// <summary>
    /// Data for the contacts info report.
    /// </summary>
    public class ContactsInfoData(ApplicationUser user, IEnumerable<string> masterClientCodes, int totalCompanies)
    {
        /// <summary>
        /// Gets the user associated with the data.
        /// </summary>
        public ApplicationUser User { get; init; } = user;

        /// <summary>
        /// Gets the master client codes associated with the user.
        /// </summary>
        public IEnumerable<string> MasterClientCodes { get; init; } = masterClientCodes;

        /// <summary>
        /// Gets the total number of companies associated with the user.
        /// </summary>
        public int TotalCompanies { get; init; } = totalCompanies;
    }
}