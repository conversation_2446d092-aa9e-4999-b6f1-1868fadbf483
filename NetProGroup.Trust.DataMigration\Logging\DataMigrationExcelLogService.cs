﻿using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Domain.Settings;
using System.Threading.Tasks.Dataflow;

namespace NetProGroup.Trust.DataMigration.Logging
{
    /// <summary>
    /// Provides Excel logging services for data migration operations.
    /// </summary>
    public class DataMigrationExcelLogService
    {
        private ISettingsRepository _settingsRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataMigrationExcelLogService"/> class.
        /// </summary>
        /// <param name="settingsRepository">The settings repository for accessing configuration data.</param>
        public DataMigrationExcelLogService(ISettingsRepository settingsRepository)
        {
            _settingsRepository = settingsRepository;
        }

        /// <summary>
        /// Gets a new or existing LogWorkbook for the migration record.
        /// </summary>
        /// <param name="migrationRecord"></param>
        /// <returns></returns>
        public async Task<LogWorkbook> GetForMigrationRecordAsync(Domain.DataMigrations.DataMigration migrationRecord)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));

            var key = $"logworkbook-{migrationRecord.Id}";
            var setting = await _settingsRepository.FindFirstOrDefaultByConditionAsync(s => s.Key == key);

            if (setting != null)
            {
                return LogWorkbook.FromBase64(setting.Value);
            }
            else
            {
                return new LogWorkbook();
            }
        }

        /// <summary>
        /// Saves the logworkbook for the migration record.
        /// </summary>
        /// <param name="migrationRecord">The record to use.</param>
        /// <param name="logWorkbook">The workbook to save.</param>
        /// <param name="saveChanges">True to save changes to the database.</param>
        /// <returns>The inserted or updated workbook.</returns>
        public async Task<LogWorkbook> SetForMigrationRecordAsync(Domain.DataMigrations.DataMigration migrationRecord, LogWorkbook logWorkbook, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            ArgumentNullException.ThrowIfNull(logWorkbook, nameof(logWorkbook));

            var key = $"logworkbook-{migrationRecord.Id}";
            var setting = await _settingsRepository.FindFirstOrDefaultByConditionAsync(s => s.Key == key);
            var jurisdiction = migrationRecord.JurisdictionId;

            if (setting != null)
            {
                setting.Value = logWorkbook.AsBase64();
                await _settingsRepository.UpdateAsync(setting, saveChanges);
            }
            else
            {
                var name = $"LogWorkBook for migration {migrationRecord.Id}";
                setting = new Setting(Guid.NewGuid(), key, name, jurisdictionId: jurisdiction);
                setting.Value = logWorkbook.AsBase64();
                await _settingsRepository.InsertAsync(setting, saveChanges);
            }

            return logWorkbook;
        }

        /// <summary>
        /// Deletes the logworkbook for the migration record.
        /// </summary>
        /// <param name="migrationRecord">Record to delete.</param>
        /// <param name="saveChanges">True to save changes to the database.</param>
        /// <returns>An awaitable task.</returns>
        public async Task DeleteForMigrationRecord(Domain.DataMigrations.DataMigration migrationRecord, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));

            var key = $"logworkbook-{migrationRecord.Id}";
            var setting = await _settingsRepository.FindFirstOrDefaultByConditionAsync(s => s.Key == key);

            if (setting != null)
            {
                await _settingsRepository.DeleteAsync(setting, saveChanges);
            }
        }

        /// <summary>
        /// Gets the logworkbook as ReportDownloadResponseDTO.
        /// </summary>
        /// <param name="migrationRecord">The record to use.</param>
        /// <returns>The found report for the migration record.</returns>
        public async Task<ReportDownloadResponseDTO> GetWorkbookForMigrationAsDownload(Domain.DataMigrations.DataMigration migrationRecord)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            
            using var logWorkbook = await GetForMigrationRecordAsync(migrationRecord);

            var fileName = $"migration-logbook-{migrationRecord.Id}.xlsx";
            var result = ReportDownloadResponseDTO.Create(fileName, logWorkbook.AsMemoryStream());

            return result;
        }
    }
}
