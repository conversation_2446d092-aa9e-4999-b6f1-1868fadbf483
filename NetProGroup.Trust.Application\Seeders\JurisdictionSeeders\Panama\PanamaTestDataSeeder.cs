﻿// <copyright file="PanamaTestDataSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Panama
{
    /// <summary>
    /// Seeder for Panama data.
    /// </summary>
    public class PanamaTestDataSeeder : SeederBase, IPanamaTestDataSeeder
    {
        private const string CompanyCodeBase = "VGPAN19123";
        private const string MasterClientCode2 = "MCGPAN59792";
        private const string _masterClientCode1 = "PAN-1";
        private const string _masterClientCode2 = "PAN-2";

        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;
        private readonly IBeneficialOwnersDataManager _beneficialOwnersDataManager;
        private readonly IDirectorsDataManager _directorsDataManager;

        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="PanamaTestDataSeeder"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="jurisdictionsRepository">Instance of the jurisdiction repository.</param>
        /// <param name="legalEntitiesDataManager">Instance of the legalentities datamanager.</param>
        /// <param name="beneficialOwnersDataManager">Instance of the beneficialowners datamanager.</param>
        /// <param name="directorsDataManager">Instance of the directors datamanager.</param>
        public PanamaTestDataSeeder(ILogger<PanamaTestDataSeeder> logger,
                           IServiceProvider serviceProvider,
                           IJurisdictionsRepository jurisdictionsRepository,
                           ILegalEntitiesDataManager legalEntitiesDataManager,
                           IBeneficialOwnersDataManager beneficialOwnersDataManager,
                           IDirectorsDataManager directorsDataManager)
            : base(logger, serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _jurisdictionsRepository = jurisdictionsRepository;

            _legalEntitiesDataManager = legalEntitiesDataManager;
            _beneficialOwnersDataManager = beneficialOwnersDataManager;
            _directorsDataManager = directorsDataManager;
        }

        /// <inheritdoc/>
        public async Task RunAsync()
        {
            _jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Panama);

            try
            {
                await SyncHelper.LockAsync();
                SyncHelper.JurisdictionCodes = new List<string> { JurisdictionVPCodes.Panama };

                await CreateMasterClientsAsync();
                await CreateCompaniesAsync();
            }
            finally
            {
                SyncHelper.Unlock();
            }
        }

        /// <summary>
        /// Converts a string of characters to their corresponding ASCII values.
        /// </summary>
        /// <param name="s">The input string to convert.</param>
        /// <returns>A string of concatenated ASCII values.</returns>
        private static string CharsToNumbers(string s)
        {
            string[] number = new string[s.Length];

            for (int index = 0; index < s.Length; index++)
            {
                char c = s[index];
                number[index] = ((int)c).ToString();
            }

            var incorporationNr = number.Aggregate((s1, s2) => s1 + s2);
            return incorporationNr;
        }

        private async Task CreateCompaniesAsync()
        {
            for (int i = 1; i < 20; i++)
            {
                await CreateCompanyAsync($"{CompanyCodeBase}{i}", $"Panama {i}", $"SDFJD{i.ToString().PadLeft(2, '0')}", MasterClientCode2);
            }

            // Retrieve the created companies for the jurisdiction
            var legalEntitiesRepository = _serviceProvider.GetRequiredService<ILegalEntitiesRepository>();
            var companies = await legalEntitiesRepository.FindByConditionAsync(le => le.Jurisdiction.Code == JurisdictionVPCodes.Panama);

            foreach (var company in companies)
            {
                EnableModules(company.Id, ModuleKeyConsts.BasicFinancialReportPanama);
                EnableModules(company.Id, ModuleKeyConsts.BODirectors);
            }
        }

        private async Task CreateMasterClientsAsync()
        {
            await CreateMasterClientAsync(_masterClientCode1);
            await AssignUsersToMasterClientAsync(_masterClientCode1);

            await CreateMasterClientAsync(_masterClientCode2);
            await AssignUsersToMasterClientAsync(_masterClientCode2);
        }

        private async Task CreateCompanyAsync(string code, string name, string incorporationNr, string masterClientCode)
        {
            var request = new DataManager.LegalEntities.Models.SyncLegalEntitiesRequest();

            var legalEntity = new DataManager.LegalEntities.Models.SyncLegalEntity
            {
                JurisdictionCode = _jurisdiction.Code,
                UniqueId = code,
                EntityType = DomainShared.Enums.LegalEntityType.Company,
                MasterClientCode = masterClientCode,
                Code = code,
                Name = name,
                IncorporationNr = incorporationNr,
                LegacyCode = $"legacy {code}",
                ReferralOffice = "REF1",
                EntityStatusCode = LegalEntityStatusCodes.Active,
                EntityStatus = LegalEntityStatusNames.Active,
                EntityTypeCode = "CI",
                EntityTypeName = LegalEntityTypes.LLC
            };
            request.LegalEntities.Add(legalEntity);

            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(request);
        }

        private Guid CreateCompany(string jurisdictionCode, string key, string masterClientCode, bool isActive = true)
        {
            // Generate an incorporation number based on the ASCII values of the key
            string incorporationNr = CharsToNumbers(key);

            var jurisdictionsRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
            var masterClientRepository = _serviceProvider.GetRequiredService<IMasterClientsRepository>();
            var masterClient = masterClientRepository.FindFirstOrDefaultByCondition(x => x.Code == masterClientCode);

            var jurisdiction = jurisdictionsRepository.FindFirstOrDefaultByCondition(j => j.Code == jurisdictionCode);

            var repository = _serviceProvider.GetRequiredService<ILegalEntitiesRepository>();

            var code = $"Code {key}";
            var existing = repository.FindFirstOrDefaultByCondition(x => x.Code == code);

            var generatedCompany = CompanyFaker
                                   .RuleFor(c => c.Code, code)
                                   .RuleFor(c => c.LegacyCode, $"Legacy {code}")
                                   .RuleFor(c => c.Name, $"Company {key}")
                                   .RuleFor(c => c.MasterClientId, masterClient.Id)
                                   .RuleFor(c => c.JurisdictionId, jurisdiction.Id)
                                   .RuleFor(c => c.IncorporationNr, incorporationNr)
                                   .RuleFor(c => c.IsActive, isActive)
                                   .RuleFor(owner => owner.ProductionOffice, (faker, owner) => "TPAN")
                                   .Generate();

            if (existing == null)
            {
                var dataManager = _serviceProvider.GetRequiredService<ILegalEntitiesDataManager>();

                var createCompanyDto = new CreateCompanyDTO
                {
                    Code = generatedCompany.Code,
                    LegacyCode = generatedCompany.LegacyCode,
                    Name = generatedCompany.Name,
                    MasterClientId = generatedCompany.MasterClientId,
                    JurisdictionId = generatedCompany.JurisdictionId.Value,
                    IncorporationNr = generatedCompany.IncorporationNr,
                    IncorporationDate = generatedCompany.IncorporationDate,
                    IsActive = generatedCompany.IsActive,
                    ProductionOffice = generatedCompany.ProductionOffice,
                    ReferralOffice = generatedCompany.ReferralOffice
                };

                var companyDTO = dataManager.CreateCompanyAsync(createCompanyDto, true).Result;

                existing = repository.GetQueryable().Single(x => x.Id == companyDTO.Id);
            }
            else
            {
                if (existing.MasterClientId != masterClient.Id)
                {
                    existing.MasterClientId = masterClient.Id;
                }

                CompanyFaker.Populate(existing);
                if (generatedCompany.IsActive)
                {
                    generatedCompany.ApproveOnboarding();
                }
            }

            repository.SaveChanges();

            CreateBOs(existing.Code);
            CreateDirectors(existing.Id, existing.Code, existing.Name);
            EnableModules(existing.Id, ModuleKeyConsts.BODirectors, ModuleKeyConsts.SimplifiedTaxReturn);

            return existing.Id;
        }

        private new void EnableModules(Guid legalEntityId, params string[] modules)
        {
            foreach (var moduleName in modules)
            {
                var modulesRepository = _serviceProvider.GetRequiredService<IModulesRepository>();
                var module = modulesRepository.FindFirstOrDefaultByCondition(x => x.Key == moduleName);

                var repository = _serviceProvider.GetRequiredService<ILegalEntityModulesRepository>();

                var existing = repository.FindFirstOrDefaultByCondition(x => x.LegalEntityId == legalEntityId && x.ModuleId == module.Id);
                if (existing == null)
                {
                    existing = new LegalEntityModule(legalEntityId, module.Id);

                    repository.Insert(existing, saveChanges: false);
                }
                else
                {
                    existing.IsEnabled = true;
                    repository.Update(existing, saveChanges: false);
                }

                existing.IsApproved = true;
                existing.IsEnabled = true;
                repository.SaveChanges();
            }
        }
    }
}
