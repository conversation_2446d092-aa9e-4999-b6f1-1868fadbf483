// <copyright file="ITemplateRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;

namespace NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator
{
    /// <summary>
    /// Interface for template row populator.
    /// </summary>
    /// <typeparam name="T">The type of the data.</typeparam>
    public interface ITemplateRowPopulator<T>
    {
        /// <summary>
        /// Populates the row with the data.
        /// </summary>
        /// <param name="worksheet">The worksheet to populate the row on.</param>
        /// <param name="currentRow">The current row to populate.</param>
        /// <param name="data">The data to populate the row with.</param>
        void PopulateRow(IXLWorksheet worksheet, int currentRow, T data);
    }
}