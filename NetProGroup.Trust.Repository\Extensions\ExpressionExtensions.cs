﻿// <copyright file="ExpressionExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Linq.Expressions;

namespace NetProGroup.Trust.Domain.Repository.Extensions
{
    /// <summary>
    /// Provides extension methods for combining expressions.
    /// </summary>
    public static class ExpressionExtensions
    {
        /// <summary>
        /// Combines two expressions with a logical AND operation.
        /// </summary>
        /// <typeparam name="T">The type of the parameter in the expressions.</typeparam>
        /// <param name="expr1">The first expression.</param>
        /// <param name="expr2">The second expression.</param>
        /// <returns>A new expression that represents the logical AND of the two input expressions.</returns>
        public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));

            var body = Expression.AndAlso(
                Expression.Invoke(expr1, parameter),
                Expression.Invoke(expr2, parameter));

            return Expression.Lambda<Func<T, bool>>(body, parameter);
        }

        /// <summary>
        /// Combines two expressions with a logical OR operation.
        /// </summary>
        /// <typeparam name="T">The type of the parameter in the expressions.</typeparam>
        /// <param name="expr1">The first expression.</param>
        /// <param name="expr2">The second expression.</param>
        /// <returns>A new expression that represents the logical OR of the two input expressions.</returns>
        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));
            var body = Expression.OrElse(
                Expression.Invoke(expr1, parameter),
                Expression.Invoke(expr2, parameter));

            return Expression.Lambda<Func<T, bool>>(body, parameter);
        }

        /// <summary>
        /// Create an expression where a property of parameter of the first expression is used as the parameter of the second expression.
        /// </summary>
        /// <typeparam name="TSource">The type of the input of the first expression.</typeparam>
        /// <typeparam name="TProp">The type of the property from the first expression.</typeparam>
        /// <typeparam name="TResult">The type of the result of the second expression.</typeparam>
        /// <param name="selectExpression">The expression that selects the property to be used as the parameter of the second expression.</param>
        /// <param name="transformExpression">The expression that transforms the property selected by the first expression.</param>
        /// <returns>A new expression that represents the transformation of the property selected by the first expression.</returns>
        public static Expression<Func<TSource, TResult>> Combine<TSource, TProp, TResult>(
            Expression<Func<TSource, TProp>> selectExpression,
            Expression<Func<TProp, TResult>> transformExpression)
        {
            ArgumentNullException.ThrowIfNull(selectExpression, nameof(selectExpression));

            var sourceParameter = selectExpression.Parameters[0];
            var propertyAccess = selectExpression.Body;
            var transformedAccess = Expression.Invoke(transformExpression, propertyAccess);
            return Expression.Lambda<Func<TSource, TResult>>(transformedAccess, sourceParameter);
        }

        /// <summary>
        /// Creates a conditional expression that returns one of two values based on a boolean condition.
        /// Equivalent to the ternary operator (condition ? trueValue : falseValue).
        /// </summary>
        /// <typeparam name="T">The type of the parameter in the condition expression.</typeparam>
        /// <typeparam name="TResult">The type of the result values.</typeparam>
        /// <param name="conditionExpression">The boolean expression to evaluate.</param>
        /// <param name="trueValue">The value to return when the condition is true.</param>
        /// <param name="falseValue">The value to return when the condition is false.</param>
        /// <returns>A new expression that represents the conditional logic.</returns>
        public static Expression<Func<T, TResult>> ConditionalSelect<T, TResult>(
            this Expression<Func<T, bool>> conditionExpression,
            TResult trueValue,
            TResult falseValue)
        {
            ArgumentNullException.ThrowIfNull(conditionExpression, nameof(conditionExpression));

            var parameter = Expression.Parameter(typeof(T));
            var conditionInvoke = Expression.Invoke(conditionExpression, parameter);
            var trueConstant = Expression.Constant(trueValue, typeof(TResult));
            var falseConstant = Expression.Constant(falseValue, typeof(TResult));
            var condition = Expression.Condition(conditionInvoke, trueConstant, falseConstant);

            return Expression.Lambda<Func<T, TResult>>(condition, parameter);
        }

        /// <summary>
        /// Creates a conditional expression that returns one of two expressions based on a boolean condition.
        /// Equivalent to the ternary operator but with expression results.
        /// </summary>
        /// <typeparam name="T">The type of the parameter in the expressions.</typeparam>
        /// <typeparam name="TResult">The type of the result expressions.</typeparam>
        /// <param name="conditionExpression">The boolean expression to evaluate.</param>
        /// <param name="trueExpression">The expression to evaluate when the condition is true.</param>
        /// <param name="falseExpression">The expression to evaluate when the condition is false.</param>
        /// <returns>A new expression that represents the conditional logic.</returns>
        public static Expression<Func<T, TResult>> ConditionalSelect<T, TResult>(
            this Expression<Func<T, bool>> conditionExpression,
            Expression<Func<T, TResult>> trueExpression,
            Expression<Func<T, TResult>> falseExpression)
        {
            ArgumentNullException.ThrowIfNull(conditionExpression, nameof(conditionExpression));
            ArgumentNullException.ThrowIfNull(trueExpression, nameof(trueExpression));
            ArgumentNullException.ThrowIfNull(falseExpression, nameof(falseExpression));

            var parameter = Expression.Parameter(typeof(T));
            var conditionInvoke = Expression.Invoke(conditionExpression, parameter);
            var trueInvoke = Expression.Invoke(trueExpression, parameter);
            var falseInvoke = Expression.Invoke(falseExpression, parameter);
            var condition = Expression.Condition(conditionInvoke, trueInvoke, falseInvoke);

            return Expression.Lambda<Func<T, TResult>>(condition, parameter);
        }
    }
}
